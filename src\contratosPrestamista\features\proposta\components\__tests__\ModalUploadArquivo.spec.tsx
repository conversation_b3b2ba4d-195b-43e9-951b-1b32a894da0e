import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { withQueryClient } from 'main/utils/__tests__/queryCliente';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import { act } from 'react-dom/test-utils';
import ModalUploadArquivo from '../ModalUploadArquivo';

describe('prestamista - propposta/ModalUpload', () => {
  it('deve clicar no botão cancelar e fechar a modal', () => {
    let modalOpen = true;
    window.HTMLElement.prototype.scrollIntoView = vi.fn();
    render(
      withQueryClient(
        <RenderPageWithThemeProviderAndRouter>
          <ModalUploadArquivo
            open={modalOpen}
            proposta={{
              numeroProposta: '80630770027888',
              dataHoraEmissaoDaProposta: '2019-12-20T00:00:00',
              numeroLinhaDoProduto: '2',
              codigoAgenciaVenda: 630,
              cpfCnpj: '22443025802',
              codigoDoEstipulante: '2',
            }}
            handleClose={() => {
              modalOpen = false;
            }}
          />
        </RenderPageWithThemeProviderAndRouter>,
      ),
    );

    const botaoCancelar = screen.getByTestId(/cancelar/i);

    act(() => {
      userEvent.click(botaoCancelar);
    });

    waitFor(
      () => {
        expect(modalOpen).toBeFalsy();
      },
      { timeout: 500 },
    );
  });

  it('deve clicar no botão confirmar e apresentar uma mensagem de erro', () => {
    let modalOpen = true;
    render(
      withQueryClient(
        <RenderPageWithThemeProviderAndRouter>
          <ModalUploadArquivo
            open={modalOpen}
            proposta={{
              numeroProposta: '80630770027888',
              dataHoraEmissaoDaProposta: '2019-12-20T00:00:00',
              numeroLinhaDoProduto: '2',
              codigoAgenciaVenda: 630,
              cpfCnpj: '22443025802',
              codigoDoEstipulante: '2',
            }}
            handleClose={() => {
              modalOpen = false;
            }}
          />
        </RenderPageWithThemeProviderAndRouter>,
      ),
    );

    const botaoConfirmar = screen.getByTestId(/confirmar/i);

    act(() => userEvent.click(botaoConfirmar));

    waitFor(
      () => {
        const mensagemErroUploadFile = screen.getByTestId(
          /mensagemErroUploadFile/i,
        );
        expect(modalOpen).toBeTruthy();
        expect(mensagemErroUploadFile).toBeInTheDocument();
      },
      { timeout: 500 },
    );
  });
});
