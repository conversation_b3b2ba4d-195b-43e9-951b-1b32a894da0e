import AppContextProvider from 'main/contexts/AppContext';
import { USER_PROFILES } from 'main/features/Auth/config/userProfiles';
import React, { PropsWithChildren } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  ThemeProvider as ComponentesPosVendaProvider,
  ApiGatewayCvpProvider,
} from '@cvp/componentes-posvenda';
import { vi } from 'vitest';
import { api } from 'main/services/api';

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(() => ({
    loading: false,
    response: null,
    responseBinary: null,
    error: null,
    fetchData: vi.fn(),
    invocarApiGatewayCvpComToken: vi.fn(),
    invocarApiGatewayCvpComRetornoBinary: vi.fn(),
    setResponse: vi.fn(),
    invalidateCache: vi.fn(),
  })),
}));

/**
 * Renderiza o componente com AppContextProvider e QueryClientProvider
 * @param param0
 * @returns
 */
export const RenderPageWithAppContextAndQueryClient: React.FC<
  PropsWithChildren
> = ({ children }) => {
  const queryClient = new QueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      <ComponentesPosVendaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: api,
            authMode: 'DEFAULT_GI',
            usernameKey: '@portal-eco:nomeAcesso',
            storageMode: 'LOCAL',
            operationPath: 'PortalEconomiario/',
          }}
        >
          <AppContextProvider
            menus={[
              {
                label: 'Consultar Cliente',
                alt: 'consultarCliente',
                path: '/cliente',
                icon: 'search',
                roles: [
                  USER_PROFILES.ANALISTA_TI,
                  USER_PROFILES.ECONOMIARIO,
                  USER_PROFILES.ANALISTA_POS_VENDA,
                ],
              },
            ]}
          >
            {children}
          </AppContextProvider>
        </ApiGatewayCvpProvider>
      </ComponentesPosVendaProvider>
    </QueryClientProvider>
  );
};
