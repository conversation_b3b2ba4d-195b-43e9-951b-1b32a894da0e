import styled from 'styled-components';
import { getTernaryResult } from 'main/utils/conditional';
import { ITextAreaProps } from './types';

export const Container = styled.div(() => ({
  display: 'block',
}));

export const Legend = styled.p<{ isValid: boolean }>(
  ({ theme: { color }, isValid }) => ({
    color: isValid ? color.neutral['03'] : color.feedback.error,
    fontSize: '11px',
  }),
);

export const TextArea = styled.textarea<{
  resize?: ITextAreaProps['resize'];
  height?: ITextAreaProps['height'];
  width?: ITextAreaProps['width'];
  error?: boolean | unknown;
}>(({ theme, error, resize, height, width }) => ({
  background: theme.color.neutral['08'],
  border: `1px solid ${getTernaryResult(
    !error,
    theme.color.line.medium,
    theme.color.feedback.error,
  )}`,
  borderRadius: '4px',
  boxSizing: 'border-box',
  color: '#414042',
  fontFamily: 'CAIXA Std',
  fontSize: 16,
  fontWeight: ' 400',
  height: height ?? 'auto',
  '-webkit-letter-spacing': 'auto',
  '-moz-letter-spacing': 'auto',
  '-ms-letter-spacing': ' auto',
  'letter-spacing': 'auto',
  lineHeight: '150%',
  maxWidth: '100%',
  minHeight: 56,
  outline: 'none',
  padding: '8px 12px',
  resize,
  '-webkit-transition': '0.3s',
  transition: ' 0.3s',
  width: width ?? '100%',
}));
