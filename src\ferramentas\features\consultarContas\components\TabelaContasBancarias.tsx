import RenderConditional from 'main/components/RenderConditional';
import SkeletonLoading from 'main/components/SkeletonLoading';
import Table from 'main/components/Table';
import { checkIfAllItemsAreTrue } from 'main/utils/conditional';
import React from 'react';
import { COLUNAS_CONTAS_BANCARIAS } from 'ferramentas/features/consultarContas/constants/colunas';
import { TTabelaContasBancariasProps } from 'ferramentas/features/consultarContas/types';

export const TabelaContasBancarias: React.FC<TTabelaContasBancariasProps> = ({
  dados,
  loading,
  iniciado,
}) => {
  return (
    <>
      <RenderConditional condition={loading}>
        <SkeletonLoading />
      </RenderConditional>

      <RenderConditional
        condition={checkIfAllItemsAreTrue([!loading, !!dados, !!iniciado])}
      >
        <Table
          columns={COLUNAS_CONTAS_BANCARIAS}
          data={dados}
          noDataComponent="Não há dados para exibir."
          pagination
          striped
          paginationPerPage={10}
          paginationComponentOptions={{
            rowsPerPageText: 'Items por página',
            rangeSeparatorText: 'de',
          }}
        />
      </RenderConditional>
    </>
  );
};
