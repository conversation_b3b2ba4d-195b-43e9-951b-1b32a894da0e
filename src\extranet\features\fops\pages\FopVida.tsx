import React from 'react';
import { Display, Card, Text, Grid, Divider } from '@cvp/design-system/react';
import ListarFops from 'extranet/components/ListarFops/ListarFops';
import { useFormFops } from 'extranet/hooks/useFormFops';
import SkeletonLoading from 'main/components/SkeletonLoading';
import { TIPOS_SEGMENTO } from 'main/features/Administracao/types/IFops';
import ModalMotivoUsoFop from 'extranet/components/ListarFops/ModalMotivoUsoFop';
import { FOP_TEXTS } from 'extranet/features/fops/constants/consts';

const FopVida: React.FC = () => {
  const { loadingFops, openMotivo, handleOpenMotivo, listarFopsProps } =
    useFormFops();

  if (loadingFops) {
    return <SkeletonLoading blocks={2} />;
  }

  return (
    <Display type="display-block">
      <Card>
        <Card.Content padding={[4, 4, 4]}>
          <Grid>
            <Grid.Item xs={1}>
              <Text
                variant="headline-05"
                color="primary"
                key="formulario-titulo"
              >
                {FOP_TEXTS.TITULOS.VIDA}
              </Text>
            </Grid.Item>
          </Grid>
          <Divider />
          <ListarFops tipoFop="vida" {...listarFopsProps} />
          <ListarFops tipoFop="prest_vida" {...listarFopsProps} />
          <ListarFops tipoFop="corp_outros" {...listarFopsProps} />
          <Divider />
        </Card.Content>
      </Card>

      <ModalMotivoUsoFop
        fop={openMotivo}
        segmento={TIPOS_SEGMENTO.VIDA}
        onClose={handleOpenMotivo}
        onConfirm={listarFopsProps.baixarArquivoFop}
      />
    </Display>
  );
};
export default FopVida;
