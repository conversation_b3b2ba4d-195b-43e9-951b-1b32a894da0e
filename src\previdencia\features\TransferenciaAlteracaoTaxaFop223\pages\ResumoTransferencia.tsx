import * as DS from '@cvp/design-system/react';
import * as S from '../components/styles';
import For from 'main/components/For';
import {
  checkIfSomeItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import { tryGetMonetaryValueOrDefault } from 'main/utils/money';
import CardSkeleton from 'main/components/Card/CardSkeleton';
import { useEffect, useState } from 'react';
import ModalResumoTransferenciaModalConfirmacao from '../components/Modais/ModalResumoTransferenciaModalConfirmacao';
import Icon from 'main/components/Icon';
import { useResumoTransferencia } from '../hooks/useResumoTransferencia';
import RenderConditional from 'main/components/RenderConditional';
import ModalAlertaContatos from 'main/components/AssinaturaDocuSign/ModalAlertaContatos';
import { useNavigate } from 'react-router-dom';
import { useObterContatosDefaultPrevidencia } from 'main/hooks/useObterContatosDefaultPrevidencia';
import PrevidenciaResumo from 'previdencia/components/PrevidenciaResumo';
import { ENDPOINTS_TRANSFERENCIA_ALTERACAO_TAXA_FOP_223 } from '../constants/endpoint';
import * as CONSTS from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/constants/textos';
import { IResponseListarMelhoresTaxaFamiliaFundo } from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/types/IListarMelhoresTaxasPeco';

export const ResumoTransferencia = () => {
  const [showModal, setShowModal] = useState(false);
  const [openModalAlerta, setOpenModalAlerta] = useState<boolean>(true);

  const navigate = useNavigate();
  const {
    loading,
    loadingConsultarSaldo,
    possuiFundosDiferenciados,
    existeCoberturasNaoComercializas,
    coberturas,
    saldoTotal,
    responseConsultarSaldo,
    loadingListarMelhoresTaxas,
    responseListarMelhoresTaxas,
    fundos,
    listaFundosSelect,
    mensagemError,
    coberturasNaoComerciaizadas,
    setFamiliaFundoEscolhida,
    handleNextStep,
    clearFeatureData,
    isDisabledButton,
  } = useResumoTransferencia();
  const {
    data: responseObterContatosDefault,
    isLoading: loadingObterContatos,
  } = useObterContatosDefaultPrevidencia();
  const { emailDefault, numerosTelefone } = responseObterContatosDefault ?? {};

  const handleCloseModalAlerta = () => {
    setOpenModalAlerta(!openModalAlerta);
    if (existeCoberturasNaoComercializas) setShowModal(!showModal);
  };

  useEffect(() => {
    clearFeatureData();
  }, []);

  const isLoading = checkIfSomeItemsAreTrue([
    loading,
    loadingConsultarSaldo,
    loadingListarMelhoresTaxas,
    loadingObterContatos,
  ]);

  return (
    <>
      <RenderConditional condition={isLoading}>
        <CardSkeleton />
      </RenderConditional>
      <RenderConditional condition={!isLoading}>
        <DS.Display type="block">
          <PrevidenciaResumo />
          <DS.Card>
            <DS.Card.Content>
              <DS.Text variant="headline-05" color="primary" margin>
                Transferência com alteração de taxa
              </DS.Text>
              <RenderConditional condition={!mensagemError}>
                <S.Disclaimer>
                  <DS.Disclaimer.Content
                    icon={<Icon name="warning" />}
                    text={CONSTS.CERTIFICADO_SELECIONADO}
                  />
                </S.Disclaimer>
              </RenderConditional>
              <RenderConditional condition={!!mensagemError}>
                <S.Disclaimer variant="error">
                  <DS.Disclaimer.Content
                    icon={<Icon name="warning" />}
                    text={mensagemError}
                  />
                </S.Disclaimer>
              </RenderConditional>
              <br />
              <RenderConditional condition={!mensagemError}>
                <DS.Grid>
                  <DS.Grid.Item md={1 / 2}>
                    <DS.Text color="primary">
                      <strong>Certificado de origem</strong>
                    </DS.Text>
                    <br />
                    <DS.Text variant="caption">Coberturas:</DS.Text>
                    <For
                      each={tryGetValueOrDefault(
                        [coberturas?.dadosCoberturas],
                        [],
                      )}
                    >
                      {item => (
                        <DS.Text variant="caption">
                          <strong>{item.cobertura}</strong>
                        </DS.Text>
                      )}
                    </For>
                    <br />
                    <DS.Text variant="caption">Fundos:</DS.Text>
                    <For each={tryGetValueOrDefault([fundos], [])}>
                      {item => (
                        <DS.Text variant="caption">
                          <strong>{item.descricaoFundo}</strong>
                        </DS.Text>
                      )}
                    </For>
                    <DS.Display>
                      <DS.Text variant="caption">Saldo:</DS.Text>
                      <DS.Tag
                        variant="secondary"
                        value={tryGetMonetaryValueOrDefault([saldoTotal], 0)}
                      />
                    </DS.Display>
                    <DS.Text variant="caption">
                      <strong>
                        Faixa de saldo atual total:{' '}
                        {responseListarMelhoresTaxas?.entidade?.taxaAtual?.replace(
                          '.',
                          ',',
                        )}
                      </strong>
                    </DS.Text>
                    <S.AlertContainer>{CONSTS.FAIXA_SAIDA}</S.AlertContainer>
                  </DS.Grid.Item>
                  <DS.Grid.Item md={1 / 2}>
                    <DS.Text color="primary">
                      <strong>Taxa de administração de fundo</strong>
                    </DS.Text>
                    <DS.Text variant="caption">
                      {CONSTS.SELECIONE_FAMILIA}
                    </DS.Text>
                    <S.Wrapper>
                      <S.SectionTitlePrimary>
                        <DS.Grid alignItems="center">
                          <DS.Grid.Item>
                            <DS.Text variant="caption">
                              Saldo total de reserva no CPF:{' '}
                              {tryGetMonetaryValueOrDefault(
                                [responseConsultarSaldo?.dados.saldo],
                                0,
                              )}
                            </DS.Text>
                          </DS.Grid.Item>
                          <RenderConditional
                            condition={possuiFundosDiferenciados}
                          >
                            <DS.Grid.Item>
                              <DS.Tooltip
                                variant="primary"
                                text={CONSTS.ELEGIVEL_DIFERENCIADO}
                                position="bottom"
                              >
                                <S.TagPremium value="CP PREMIUM" />
                              </DS.Tooltip>
                            </DS.Grid.Item>
                          </RenderConditional>
                        </DS.Grid>
                      </S.SectionTitlePrimary>
                    </S.Wrapper>
                    <DS.Grid>
                      <DS.Grid.Item md={1 / 3}>
                        <DS.Display alignItems="center">
                          <DS.Text variant="caption">
                            Faixas de reserva disponível
                          </DS.Text>
                        </DS.Display>
                      </DS.Grid.Item>
                      <DS.Grid.Item md={2 / 3}>
                        <div style={{ flex: 1 }}>
                          <DS.Select
                            placeholder="Escolha a opção"
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<HTMLInputElement>) =>
                              setFamiliaFundoEscolhida(Number(value))
                            }
                          >
                            {listaFundosSelect?.map(
                              (
                                item: IResponseListarMelhoresTaxaFamiliaFundo,
                              ) => {
                                return item?.familia ? (
                                  <DS.Select.Item
                                    key={item?.familia}
                                    value={item?.familia}
                                    text={item?.descricao}
                                  />
                                ) : (
                                  <S.LabelItem>{item.descricao}</S.LabelItem>
                                );
                              },
                            )}
                          </DS.Select>
                        </div>
                      </DS.Grid.Item>
                    </DS.Grid>
                  </DS.Grid.Item>
                </DS.Grid>
                <DS.Display alignItems="flex-end" justify="flex-end">
                  <DS.Button
                    variant="primary"
                    onClick={handleNextStep}
                    disabled={isDisabledButton()}
                  >
                    Avançar
                  </DS.Button>
                </DS.Display>
              </RenderConditional>
            </DS.Card.Content>
          </DS.Card>
          <ModalResumoTransferenciaModalConfirmacao
            show={showModal}
            onClose={() =>
              navigate(
                ENDPOINTS_TRANSFERENCIA_ALTERACAO_TAXA_FOP_223.CLIENTE_PRODUTO,
              )
            }
            coberturasNaoComerciaizadas={tryGetValueOrDefault(
              [coberturasNaoComerciaizadas],
              [],
            )}
            onContinue={() => setShowModal(!showModal)}
          />

          <ModalAlertaContatos
            open={openModalAlerta}
            onClose={handleCloseModalAlerta}
            redirect={() =>
              navigate(
                ENDPOINTS_TRANSFERENCIA_ALTERACAO_TAXA_FOP_223.DADOS_PARTICIPANTE,
              )
            }
            dados={{
              email: emailDefault,
              telefone: numerosTelefone,
            }}
          />
        </DS.Display>
      </RenderConditional>
    </>
  );
};
