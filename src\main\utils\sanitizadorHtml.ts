import {
  <PERSON><PERSON><PERSON>_TAG_STRONG_ATTRIBUTES,
  REMOVE_CLASS_ATTRIBUTE,
  REMOVE_DIR_ATTRIBUTE,
  REMOVE_STYLE_ATTRIBUTE,
  REMOVE_TAG_B,
  REMOVE_TAG_SPAN,
  SUBSTITUIR_DA_ABERTURA_P,
  SUBSTITUIR_DA_FECHAMENTO_P,
} from 'main/utils/regex';

export const sanitizadorHtml = (html: string): string =>
  html
    .replace(REMOVE_TAG_SPAN, '$1')
    .replace(REMOVE_TAG_B, '$1')
    .replace(CLEAN_TAG_STRONG_ATTRIBUTES, '<strong>$1</strong>')
    .replace(REMOVE_STYLE_ATTRIBUTE, '')
    .replace(REMOVE_CLASS_ATTRIBUTE, '')
    .replace(REMOVE_DIR_ATTRIBUTE, '')
    .replace(SUBSTITUIR_DA_ABERTURA_P, '<span>')
    .replace(SUBSTITUIR_DA_FECHAMENTO_P, '</span>');
