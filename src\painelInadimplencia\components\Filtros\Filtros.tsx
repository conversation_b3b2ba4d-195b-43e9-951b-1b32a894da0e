import {
  Button,
  Display,
  Grid,
  Select,
  TextField,
} from '@cvp/design-system/react';
import { ChangeEvent } from 'react';

import RenderConditional from 'main/components/RenderConditional';
import { required } from 'main/features/Validation/validations';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import masks from 'main/utils/masks';
import * as CONSTS from 'painelInadimplencia/constants/constants';
import { IDadosInadimplenciaPayload } from 'painelInadimplencia/types/ConsultarInadimplenciaRequest';
import { IFiltrosProps } from 'painelInadimplencia/types/PainelInadimplencia';

const Filtros = ({
  tipoVisao,
  payloadConsulta,
  setPayloadConsulta,
  consultarInadimplencia,
  filterFormaPagamento,
  handleBackStep,
  clearFilters,
  isDisabledBotaoVoltar,
  naoCotemFiltros
}: IFiltrosProps) => {
  return (
    <>
      <Grid>
        <RenderConditional condition={tipoVisao === CONSTS.TiposVisao.AG}>
          <Grid.Item xl={1 / 4} lg={2 / 4} xs={1}>
            <Select
              dettach
              validationRules={[required()]}
              label="Periodicidade de Pagamento"
              placeholder="Escolha a opção"
              onChange={(event: ChangeEvent<HTMLSelectElement>) => {
                const selectedValue = event.target.value;
                setPayloadConsulta((prevState: IDadosInadimplenciaPayload) => ({
                  ...prevState,
                  PeriodicidadePagamento: selectedValue,
                }));
              }}
            >
              {CONSTS.PeriodicidadePagamentoFilter.map(item => (
                <Select.Item
                  key={item.key}
                  text={item.label}
                  value={item.value}
                  selected={
                    item.value === payloadConsulta?.PeriodicidadePagamento
                  }
                />
              ))}
            </Select>
          </Grid.Item>

          <Grid.Item xl={1 / 4} lg={2 / 4} xs={1}>
            <Select
              dettach
              validationRules={[required()]}
              label="Forma de Pagamento"
              placeholder="Escolha a opção"
              onChange={(event: ChangeEvent<HTMLSelectElement>) => {
                const selectedValue = event.target.value;
                setPayloadConsulta((prevState: IDadosInadimplenciaPayload) => ({
                  ...prevState,
                  FormaPagamento: selectedValue,
                }));
              }}
            >
              {filterFormaPagamento().map(item => (
                <Select.Item
                  key={item.key}
                  text={item.value}
                  value={item.value}
                  selected={item.value === payloadConsulta?.FormaPagamento}
                />
              ))}
            </Select>
          </Grid.Item>

          <Grid.Item xl={1 / 4} lg={2 / 4} xs={1}>
            <TextField
              name="cpfCnpj"
              label="CPF / CNPJ"
              placeholder="Digite o CPF/CNPJ"
              value={masks.cpfCnpj.mask(
                tryGetValueOrDefault([payloadConsulta?.cpfCnpj], ''),
              )}
              onChange={(event: ChangeEvent<HTMLSelectElement>) => {
                setPayloadConsulta((prevState: IDadosInadimplenciaPayload) => ({
                  ...prevState,
                  cpfCnpj: masks.cpfCnpj.unmask(event.target.value),
                }));
              }}
            />
          </Grid.Item>

          <Grid.Item xl={1 / 4} lg={2 / 4} xs={1}>
            <TextField
              name="numeroContrato"
              label="Número do Contrato"
              placeholder="Digite o número do contrato"
              value={tryGetValueOrDefault(
                [payloadConsulta?.numeroContrato],
                '',
              )}
              onChange={(event: ChangeEvent<HTMLSelectElement>) => {
                setPayloadConsulta((prevState: IDadosInadimplenciaPayload) => ({
                  ...prevState,
                  numeroContrato: event.target.value,
                }));
              }}
            />
          </Grid.Item>
        </RenderConditional>
      </Grid>

      <Display>
        <RenderConditional condition={tipoVisao === CONSTS.TiposVisao.AG}>
          <Button
            disabled={isDisabledBotaoVoltar}
            type="submit"
            variant="outlined"
            onClick={() => {
              handleBackStep();
            }}
          >
            Voltar
          </Button>

          <Button
            disabled={naoCotemFiltros}
            data-testid="filtrar"
            variant="primary"
            onClick={() => consultarInadimplencia()}
          >
            Filtrar
          </Button>

          <Button
            disabled={naoCotemFiltros}
            data-testid="limpar-filtros"
            variant="secondary"
            onClick={clearFilters}
          >
            Limpar filtros
          </Button>
        </RenderConditional>
      </Display>
    </>
  );
};

export default Filtros;
