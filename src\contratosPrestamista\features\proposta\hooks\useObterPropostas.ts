import { useQuery } from '@tanstack/react-query';
import * as PropostaApi from 'contratosPrestamista/features/proposta/services/proposta.api';
import { IResponseListaPropostaQuery } from 'contratosPrestamista/features/proposta/types/IResponseListaProposta';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';
import { useToast } from 'main/hooks/useToast';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';

export const useObterListaPropostas = (): IResponseListaPropostaQuery => {
  return useQuery({
    queryKey: ['propostas', PropostaApi.obterListaPropostas],
    staleTime: reactQueryCacheDuration(),
    retry: false,
  });
};

export const useObterProposta = (
  id: string,
  numeroLinhaDoProduto: string,
  codigoDoEstipulante: string,
) => {
  const { toastError } = useToast();
  return useQueryCallbacks({
    queryKey: ['propostas', id],
    queryFn: () =>
      PropostaApi.obterProposta(id, numeroLinhaDoProduto, codigoDoEstipulante),
    staleTime: reactQueryCacheDuration(),
    retry: false,
    onError: (erro: Error) => toastError(erro.message),
  });
};
