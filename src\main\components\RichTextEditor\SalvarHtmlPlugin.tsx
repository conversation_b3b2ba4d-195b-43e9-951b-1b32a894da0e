import { $generateHtmlFromNodes } from '@lexical/html';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { ISalvarHtmlPlugin } from 'main/types/ISalvarHtmlPlugin';
import { useEffect } from 'react';

export const SalvarHtmlPlugin = ({ onSave }: ISalvarHtmlPlugin): null => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    const unsubscribe = editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const html = $generateHtmlFromNodes(editor);
        onSave(html);
      });
    });

    return () => unsubscribe();
  }, [editor, onSave]);

  return null;
};
