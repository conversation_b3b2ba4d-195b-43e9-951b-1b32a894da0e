import {
  useQuery,
  useQueryClient,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { useEffect } from 'react';

export function useQueryCallbacks<
  TQueryFnData,
  TError = Error,
  TData = TQueryFnData,
  TQueryKey extends readonly unknown[] = string[],
>(
  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {
    onSuccess?: (data: TData) => void;
    onError?: (error: TError) => void;
  },
): UseQueryResult<TData, TError> {
  const { onSuccess, onError, ...queryOptions } = options;
  const query = useQuery(queryOptions);
  const queryClient = useQueryClient();
  if (!queryClient) {
    console.warn(
      'QueryClient está ausente! Certifique-se de usar QueryClientProvider.',
    );
  }
  useEffect(() => {
    if (query.isSuccess && onSuccess) {
      onSuccess(query.data as TData);
    }
  }, [query.isSuccess]);

  useEffect(() => {
    if (query.isError && onError) {
      onError(query.error as TError);
    }
  }, [query.isError]);

  return query;
}
