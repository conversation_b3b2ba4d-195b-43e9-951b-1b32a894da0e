import React from 'react';
import { Text } from '@cvp/design-system/react';
import { AlertContainerFundosDiferenciados } from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/components/styles';
import * as CONSTS from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/constants/textos';
import RenderConditional from 'main/components/RenderConditional';
import { usePrevidenciaContext } from 'previdencia/contexts/PrevidenciaContextProvider';
import { ITransferenciaAlteracaoTaxaContext } from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/types/ITransferenciaAlteracaoTaxaContext';
import { checkIfAllItemsAreTrue } from 'main/utils/conditional';
import { ENUM_FUNDO_DIFERENCIADO } from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/types/enum';

const AlertaFundosDiferenciados: React.FC = () => {
  const { featureData } =
    usePrevidenciaContext<ITransferenciaAlteracaoTaxaContext>();
  return (
    <AlertContainerFundosDiferenciados>
      <RenderConditional
        condition={checkIfAllItemsAreTrue([
          !!featureData?.possuiFundosDiferenciados,
          featureData?.fundoDiferenciadoSelecionado ===
            ENUM_FUNDO_DIFERENCIADO.NAO,
        ])}
      >
        <Text variant="caption">
          <b>{CONSTS.ALERTA_FUNDOS_DIFERENCIADOS.TITULO}</b>
        </Text>
        {CONSTS.ALERTA_FUNDOS_DIFERENCIADOS.TEXTO}
      </RenderConditional>
    </AlertContainerFundosDiferenciados>
  );
};

export default AlertaFundosDiferenciados;
