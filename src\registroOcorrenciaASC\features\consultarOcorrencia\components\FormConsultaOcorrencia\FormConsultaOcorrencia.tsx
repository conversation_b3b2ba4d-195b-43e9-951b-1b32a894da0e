import React from 'react';
import { Grid, Text, Display, Button } from '@cvp/design-system/react';
import Calendar from 'main/components/form/Calendar/FormikCalendar';
import { checkIfSomeItemsAreTrue } from 'main/utils/conditional';
import masks from 'main/utils/masks';
import { useRenderizarFormConsultaOcorrencia } from 'registroOcorrenciaASC/features/consultarOcorrencia/hooks';
import {
  FormConsultaOcorrenciaProps,
  EnumBuscaPor,
} from 'registroOcorrenciaASC/features/consultarOcorrencia/types/ConsultarOcorrencia';
import { TEXTOS_OCORRENCIAS } from 'registroOcorrenciaASC/features/consultarOcorrencia/constants/constants';
import { ContainerInputGroup, FormikInput } from 'registroOcorrenciaASC/features/consultarOcorrencia/components/FormConsultaOcorrencia/styles';
import FormikSelect from 'main/components/form/Select/FormikSelect';
import { FormConsultaOcorrenciaOptions } from 'registroOcorrenciaASC/features/consultarOcorrencia/constants/forms';
import RenderConditional from 'main/components/RenderConditional';

const FormConsultaOcorrencia: React.FC<FormConsultaOcorrenciaProps> = ({
  obterHistoricoSolicitacao,
  setListaHistoricoSolicitacao,
  loadingDadosHistoricoSolicitacao,
}) => {
  const {
    values,
    errors,
    maxDate,
    maxDateRange,
    isCamposFormConsultaPreenchidos,
    handleReset,
    handleChange,
    handleObterHistoricoSolicitacoes,
  } = useRenderizarFormConsultaOcorrencia({
    setListaHistoricoSolicitacao,
    obterHistoricoSolicitacao,
  });

  return (
    <>
      <Grid>
        <Grid.Item xs={1}>
          <Text
            variant="body02-md"
            color="text"
            data-testid="subtituloPeriodoConsulta"
          >
            {TEXTOS_OCORRENCIAS.INFORME_PERIODO}
          </Text>
        </Grid.Item>
        <Grid.Item xs={1} lg={1 / 3}>
          <ContainerInputGroup>
            <Calendar
              values={values.inputDate}
              data-testid="inputCalendarioConsulta"
              placeholder="Selecione uma data"
              range
              maxDate={maxDate}
              maxDateRange={maxDateRange}
              errorFinalDate={!!errors.inputDate?.finalDate}
              errorInitialDate={!!errors.inputDate?.initialDate}
              onChange={inputDate => handleChange('inputDate', inputDate)}
            />
          </ContainerInputGroup>
        </Grid.Item>
      </Grid>
      <Grid>
        <Grid.Item xs={1 / 2} lg={1 / 3}>
          <ContainerInputGroup>
            <FormikSelect
              data-testid="selectClienteOuProtocolo"
              label="Consultar por"
              placeholder="Selecione o tipo de consulta"
              selected={values.selected}
              options={FormConsultaOcorrenciaOptions}
              onChange={value => handleChange('selected', value)}
            />
          </ContainerInputGroup>
        </Grid.Item>
        <Grid.Item xs={1 / 2} lg={1 / 3}>
          <ContainerInputGroup>
            <RenderConditional
              condition={values.selected === EnumBuscaPor.CLIENTE}
            >
              <FormikInput
                data-testid="inputCpfCnpjCliente"
                value={values.cpfCnpj}
                label="CPF/CNPJ"
                placeholder="Digite o CPF/CNPJ do cliente"
                inputMask={{
                  mask: (value: string) => masks.cpfCnpj.mask(value),
                  unmask: (value: string) => masks.cpfCnpj.unmask(value),
                }}
                error={!!errors.cpfCnpj}
                onChange={value => handleChange('cpfCnpj', value)}
              />
            </RenderConditional>
            <RenderConditional
              condition={values.selected === EnumBuscaPor.PROTOCOLO}
            >
              <FormikInput
                data-testid="inputProtocolo"
                value={values.protocolo}
                label="Protocolo"
                placeholder="Digite o protocolo da solicitação"
                inputMask={{
                  mask: (value: string) => masks.numberOnly.mask(value),
                  unmask: (value: string) => masks.numberOnly.unmask(value),
                }}
                error={!!errors.protocolo}
                onChange={value => handleChange('protocolo', value)}
              />
            </RenderConditional>
          </ContainerInputGroup>
        </Grid.Item>
      </Grid>
      <Display>
        <Button
          data-testid="botaoLimparFormularioConsulta"
          variant="outlined"
          onClick={handleReset}
        >
          Limpar
        </Button>
        <Button
          data-testid="botaoConsultarOcorrencia"
          variant="primary"
          disabled={checkIfSomeItemsAreTrue([
            !isCamposFormConsultaPreenchidos,
            loadingDadosHistoricoSolicitacao,
          ])}
          loading={loadingDadosHistoricoSolicitacao}
          onClick={handleObterHistoricoSolicitacoes}
        >
          Consultar
        </Button>
      </Display>
    </>
  );
};

export default FormConsultaOcorrencia;
