import { ISelectOption } from 'extranet/features/fops/pages/ISelectOption';

export const TEXTO_ATENCAO =
  ' Atenção: É obrigatório o preenchimento de todos os campos e envio do FOP 064, constando as informações dos colaboradores e benefícios, conforme informações deste formulário.';
export const TITULO_PAGINA =
  'FOP 062 - Solicitação de Estudo de Prev Empresarial Não Padrão';
export const CUSTEIO_PAGAMENTO =
  'Descreva neste espaço como será o custeio do pagamento';
export const AVISO_FOP_64 =
  'Será necessário preencher os campos específicos do FOP 064 disponível para download no final deste formulário.';
export const VALOR_SALARIO_AVISO =
  'Informe o valor do salário para cada participante no FOP 064';
export const SELECIONE_REGRA_CALCULO = 'Selecione a Regra Para o Cálculo';
export const REGRA_CALCULO = 'Regra Calculo';
export const CUIDADO_EXTRA = 'Tipo de Cuidado Extra';
export const INFORMATIVO_FOP64 =
  'Informe o valor do salário para cada participante no FOP 064';
export const PORCENTAGEM_SALARIO =
  ' Definir Porcentagem do Valor Com Base no Salário';
export const INFORMATIVO_DOWNLOAD_FOP = 'Faça o Download do FOP 064 Aqui';
export const TEXTO_ANEXO_FOP64 =
  'Anexe aqui o FOP 064 ou documentos importantes para elaboração do estudo';
export const TIPO_PLANO_EMPRESARIAL = 'Tipo de Plano Empresarial';
export const EMPRESA = 'Empresa';
export const COLABORADOR = 'Colaborador';
export const FUNCIONARIO = 'Funcionario';
export const NUMERO_PARTICIPANTES = 'Número de Participantes';
export const PENSAO_BENENFICIARIOS =
  'Por quantos anos a pensão será paga aos beneficiários?';
export const AVISO_CONFIRA_DADOS = 'Atenção! Confira seus dados';
export const BENEFICIO_BASICO = 'Tipo de Benefício Básico';
export const PORCENTAGEM_REVERSAO = 'porcentagem Reversão';
export const PRAZO_BENEFICIO_BASICO = 'Prazo de Benefício Básico';
export const FORMA_PAGAMENTO = 'Forma de pagamento';
export const VALORES_IGUAIS =
  'Os valores são iguais para todos os participantes';
export const SELECIONE = 'Selecione';
export const INFORMACOES_COMPLEMENTARES =
  'Informações Complementares (Opcional)';
export const TITULO_INFORMACOES_GERAIS = '1. Informações Gerais';
export const TITULO_DADOS_DO_PLANO = '2. Dados do Plano - Beneficio Básico';
export const TITULO_CUIDADO_EXTRA = '3. Cuidado Extra';
export const TITULO_INFORMACOES_COMPLEMENTARES =
  '4. Informações Complementares';
export const TITULO_DADOS_RESPONSAVEL =
  '5. Dados do Responsável Pelo Preenchimento';
export const VALOR_CONTRIBUICAO = 'Valor da Contribuição';
export const VALOR_FIXO = 'Valor fixo';
export const MENSAGEM_CAMPO_OBRIGATORIO = 'Campo obrigatório.';
export const MENSAGEM_NUMERO_PARTICIPANTE_MINIMO =
  'A quantidade mínima para contratação de planos empresariais é de 5 colaboradores. Para a quantidade de participantes preenchida, efetuar a contratação dos produtos Prev Sócio diretamente no SIGPF.';
export const MENSAGEM_NUMERO_PARTICIPANTES_LIMITE =
  'numero de participantes não pode ser superior a 10000';
export const MENSAGEM_EMAIL_INVALIDO = 'Informe um e-mail válido';
export const MENSAGEM_CAMPO_NUMERICO = 'Favor inserir apenas números';
export const MENSAGEM_DOMINIOS_CAIXA =
  'Favor Informar um e-mail com dominio Caixa ou Caixa Vida e Previdência';
export const DOMINIO_CVP = '@caixavidaeprevidencia.com.br';
export const DOMINIO_CAIXA = '@caixa.gov.br';
export const LIMITE_CARACTERES = 940;
export const PERGUNTA_PENSAO =
  'Por quantos anos a pensão será paga aos beneficiários?';
export const AVERBADO = 'averbado';

export const TEXTO_CONTADOR = ' caracteres restantes';
export const selectOptionsRegra: ISelectOption[] = [
  {
    key: 'contribuicaoMensal',
    value: 'Valor de contribuição mensal',
  },
  {
    key: 'contribuicaoAnual',
    value: 'Valor de contribuição Anual',
  },
];

export const selectOptionsPagamento: ISelectOption[] = [
  {
    key: 'averbado',
    value: 'Averbado: Custeio Somente Colaborador',
  },
  {
    key: 'instituido',
    value: 'Instituído: Custeio Somente Empresa',
  },
  {
    key: 'planoInstituido',
    value: 'Instituído Com Divisão de Pagamento: Custeio Empresa e Colaborador',
  },
];

export const selectOptionsPagamentoAverbado: ISelectOption[] = [
  {
    key: 'averbado',
    value: 'Averbado: Custeio Somente Colaborador',
  },
];

export const selectOptionsValoresParticipantes: ISelectOption[] = [
  {
    key: 'sim',
    value: 'Sim',
  },
  {
    key: 'nao',
    value: 'Não',
  },
];

export const selectOptionsValorContribuicaoEmpresa: ISelectOption[] = [
  {
    key: 'outraFormaContribuicao',
    value: 'Outra Forma de Pagamento',
  },
];

export const initialValues = {
  matriculaIndicador: '',
  nomeIndicador: '',
  nomeEmpresa: '',
  nomeCompleto: '',
  telefone: '',
  email: '',
  atividadePrincipal: '',
  cnpj: '',
  superintendenciaRegional: '',
  nomeAgencia: '',
  filial: '',
  numeroParticipantes: '',
  regraParaCalculo: '',
  formaPagamento: '',
  formaPagamentoCuidado: '',
  valoresParticipantes: '',
  valoresParticipanteCuidado: '',
  valorContribuicaoCuidadoExtra: '',
  valorContribuicaoFuncionario: '',
  valorContribuicaoFuncionarioCuidadoExtra: '',
  tipoDeBeneficioBasico: '',
  valorContribuicao: '',
  tipoCuidadoExtra: '',
  valorFixo: '',
  valorFixoEmpresa: '',
  valorContribuicaoEmpresa: '',
  valorFixoEmpresaCuidadoExtra: '',
  valorContribuicaoEmpresaCuidadoExtra: '',
  valorFixoFuncionario: '',
  valorFixoFuncionarioCuidadoExtra: '',
  anosPensao: '',
  regraCuidadoExtra: '',
  valorPercentual: '',
  valorPercentualEmpresa: '',
  valorPercentualEmpresaCuidadoExtra: '',
  valorPercentualFuncionario: '',
  valorPercentualFuncionarioCuidadoExtra: '',
  valorFixoCuidadoExtra: '',
  valorPercentualCuidadoExtra: '',
  prazoDeBeneficio: '',
  selectReversao: '',
  regraCuidadoExtraPensao: '',
};
