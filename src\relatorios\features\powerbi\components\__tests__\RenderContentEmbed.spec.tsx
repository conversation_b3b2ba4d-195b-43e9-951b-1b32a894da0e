import { render, screen } from '@testing-library/react';
import { RenderContentEmbed } from '../RenderContentEmbed';
import { usePowerBI } from '../../hooks/usePowerBI';
import ThemeProvider from 'main/components/ThemeProvider';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, beforeEach, it, expect } from 'vitest';

vi.mock('../../hooks/usePowerBI');

vi.mock('powerbi-client-react', () => ({
  PowerBIEmbed: () => <div data-testid="powerbi-embed">PowerBI Embed</div>,
}));
vi.mock('../../components/DisclaimerError', () => ({
  DisclaimerError: () => <div data-testid="mensagemErro">DisclaimerError</div>,
}));

const mockConfig = {
  GROUP_ID: 'test-group-id',
  RELATORIO_ID: 'test-report-id',
};
const queryClient = new QueryClient();

const renderComponent = () => {
  render(
    <ThemeProvider>
      <QueryClientProvider client={queryClient}>
        <RenderContentEmbed config={mockConfig} />
      </QueryClientProvider>
    </ThemeProvider>,
  );
};

describe('RenderContentEmbed', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('deve renderizar o SkeletonLoading quando loading for true', () => {
    (usePowerBI as jest.Mock).mockReturnValue({
      loading: true,
      reportSettingsLoaded: false,
      eventHandlersMap: new Map(),
      reportConfig: {},
    });

    renderComponent();

    expect(screen.getByTestId('card-skeleton-block')).toBeInTheDocument();
    expect(screen.queryByTestId('powerbi-embed')).not.toBeInTheDocument();
  });

  it('deve renderizar o DisclaimerError quando reportSettingsLoaded for false', () => {
    (usePowerBI as any).mockReturnValue({
      loading: false,
      reportSettingsLoaded: false,
      eventHandlersMap: new Map(),
      reportConfig: {},
    });

    renderComponent();

    expect(screen.getByTestId('mensagemErro')).toBeInTheDocument();
    expect(screen.queryByTestId('powerbi-embed')).not.toBeInTheDocument();
  });

  test.skip('deve renderizar o PowerBIEmbed quando loading for false e reportSettingsLoaded for true', () => {
    (usePowerBI as any).mockReturnValue({
      loading: false,
      reportSettingsLoaded: true,
      eventHandlersMap: new Map(),
      reportConfig: {
        type: 'report',
        embedUrl: 'test-url',
        tokenType: 'Embed',
        accessToken: 'test-token',
      },
    });

    renderComponent();

    expect(screen.queryByTestId('card-skeleton-block')).not.toBeInTheDocument();
    expect(screen.queryByTestId('mensagemErro')).not.toBeInTheDocument();
    expect(screen.getByTestId('powerbi-embed')).toBeInTheDocument();
    expect(screen.getByTestId('containerIframePowerBI')).toBeInTheDocument();
  });
});
