import { usePeco } from 'main/hooks/usePeco';
import { useToast } from 'main/hooks/useToast';
import { useState } from 'react';

import { obterArquivoCorp } from 'extranet/services/obterEndpointArquivo';
import useFieldLink from 'main/hooks/useFieldLink';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';

import { TEXTO_CONTADOR } from 'extranet/features/fops/constants/constantsFop62';
import { FOP_TEXTS, FOPS } from 'extranet/features/fops/constants/consts';
import * as enumFop62 from 'extranet/types/enumFop62';
import { usePecoObterListaFopsAtivos } from 'main/features/Administracao/hooks/useObterListaFopsAtivos';
import {
  IArquivoDownload,
  IResponseObterListaFopsAtivos,
  IUseFormFops,
  IUseFormFopsPrevidencia,
  RequestListarFops,
} from 'main/features/Administracao/types/IFops';
import {
  filesFormatsAllowed,
  maxFileSizeAllowed,
  requiredFile,
} from 'main/features/Validation/validations';
import { getTernaryResult } from 'main/utils/conditional';
export const FormatarNumerico = (value: string) => {
  return value.replace(/\D/g, '');
};
export const TextoCaracteres = (valorInicial: number, ValorAtual: number) => {
  const contador = `${valorInicial - ValorAtual}`;
  return `${contador}${TEXTO_CONTADOR}`;
};

export const useFormFops = (): IUseFormFops => {
  const { loading: loadingFops, response: responseFops } =
    usePecoObterListaFopsAtivos();

  const listaFops = responseFops?.entidade;

  const [fopAtivo, setFopAtivo] = useState<IResponseObterListaFopsAtivos>();

  const { loading: loadingDownload, fetchData } = usePeco<
    RequestListarFops,
    IArquivoDownload
  >({
    api: {
      operationPath: 'PECO_ObterArquivoCorp',
    },
    autoFetch: false,
  });

  const { toastError, toastSuccess } = useToast();
  const [loadingDownloadArquivo, setLoadingDownloadArquivo] =
    useState<boolean>(false);

  const obterArquivoFOP = async (
    codigoIdentificadorUnico: string,
    nomeArquivo: string,
  ): Promise<IArquivoDownload | undefined> => {
    const result = await fetchData({
      codigoIdentificadorUnico,
      nomeArquivo,
    });
    if (!result) toastError(enumFop62.EnumErrors.buscaArquivo);
    const { entidade } =
      result ?? ({} as unknown as IHandleReponseResult<IArquivoDownload>);
    return entidade;
  };

  const baixarArquivoFop = async (
    codigoIdentificadorUnico?: string,
    nomeArquivo?: string,
  ): Promise<string | number> => {
    if (codigoIdentificadorUnico && nomeArquivo) {
      const arquivo = await obterArquivoCorp(
        codigoIdentificadorUnico,
        nomeArquivo,
      );
      const downloadUrl = window.URL.createObjectURL(new Blob([arquivo]));
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', nomeArquivo);
      document.body.appendChild(link);
      link.click();
      link.remove();
      setLoadingDownloadArquivo(false);
    } else {
      return toastError(enumFop62.EnumErrors.download);
    }
    return '';
  };

  const regraFiles = [
    requiredFile(),
    filesFormatsAllowed(),
    maxFileSizeAllowed(() => enumFop62.EnumErrors.tamanoMaximo, 3),
  ];

  const [openMotivo, setOpenMotivo] = useState<IResponseObterListaFopsAtivos>();
  const [openModalFormulario, setOpenModalFormulario] =
    useState<boolean>(false);
  const [arquivoAnexoFop] = useFieldLink<FileList>({} as FileList);
  const [arquivoAnexoFop63] = useFieldLink<FileList>({} as FileList);

  const [textOutraFormaPagamento, setTextOutraFormaPagamento] =
    useState<string>('');

  const [textOutraFormaPgEmpresa, setTextOutraFormaPgEmpresa] =
    useState<string>('');
  const [
    textOutraFormaPagamentoEmpresaCuidadoExtra,
    setTextOutraFormaPagamentoEmpresaCuidadoExtra,
  ] = useState<string>('');
  const [
    textOutraFormaPagamentoFuncionario,
    setTextOutraFormaPagamentoFuncionario,
  ] = useState<string>('');
  const [
    textOutraFormaPagamentoFuncionarioCuidadoExtra,
    setTextOutraFormaPagamentoFuncionarioCuidadoExtra,
  ] = useState<string>('');
  const [
    textOutraFormaPagamentoCuidadoExtra,
    setTextOutraFormaPagamentoCuidadoExtra,
  ] = useState<string>('');
  const [
    textOutraRegraLiberacaoDaReserva,
    setTextOutraRegraLiberacaoDaReserva,
  ] = useState<string>('');
  const [textInformacoesComplementares, setTextInformacoesComplementares] =
    useState<string>('');

  const [textOutraFormaPagamentoEmpresa, setTextOutraFormaPagamentoEmpresa] =
    useState<string>('');

  const [openSelectTipoBenficio, setOpenSelectTipoBenficio] =
    useState<boolean>(false);

  const [openSelectReversivel, setOpenSelectReversivel] =
    useState<boolean>(false);
  const [openSelectValorContribuicao, setOpenSelectValorContribuicao] =
    useState<boolean>(false);
  const [
    openSelectValorContribuicaoCuidadoExtra,
    setOpenSelectValorContribuicaoCuidadoExtra,
  ] = useState<boolean>(false);
  const [openSelectValorFixo, setOpenSelectValorFixo] =
    useState<boolean>(false);
  const [openSelectValorFixoCuidadoExtra, setOpenSelectValorFixoCuidadoExtra] =
    useState<boolean>(false);
  const [openSelectValorFixoEmpresa, setOpenSelectValorFixoEmpresa] =
    useState<boolean>(false);
  const [
    openSelectValorFixoEmpresaCuidadoExtra,
    setOpenSelectValorFixoEmpresaCuidadoExtra,
  ] = useState<boolean>(false);
  const [openSelectValorFixoFuncionario, setOpenSelectValorFixoFuncionario] =
    useState<boolean>(false);
  const [
    openSelectValorFixoFuncionarioCuidadoExtra,
    setOpenSelectValorFixoFuncionarioCuidadoExtra,
  ] = useState<boolean>(false);
  const [openSelectValorPercentual, setOpenSelectValorPercentual] =
    useState<boolean>(false);
  const [
    openSelectValorPercentualCuidadoExtra,
    setOpenSelectValorPercentualCuidadoExtra,
  ] = useState<boolean>(false);
  const [
    openSelectValorPercentualEmpresa,
    setOpenSelectValorPercentualEmpresa,
  ] = useState<boolean>(false);
  const [
    openSelectValorPercentualEmpresaCuidadoExtra,
    setOpenSelectValorPercentualEmpresaCuidadoExtra,
  ] = useState<boolean>(false);
  const [
    openSelectValorPercentualFuncionario,
    setOpenSelectValorPercentualFuncionario,
  ] = useState<boolean>(false);
  const [
    openSelectValorPercentualFuncionarioCuidadoExtra,
    setOpenSelectValorPercentualFuncionarioCuidadoExtra,
  ] = useState<boolean>(false);

  const [openSelectPlanoInstituido, setOpenSelectPlanoInstituido] =
    useState<boolean>(false);

  const [openSelectOutraFormaPagamento, setOpenSelectOutraFormaPagamento] =
    useState<boolean>(false);
  const [
    openSelectPlanoInstituidoCuidadoExtra,
    setOpenSelectPlanoInstituidoCuidadoExtra,
  ] = useState<boolean>(false);

  const [
    openSelectOutraFormaPagamentoCuidadoExtra,
    setOpenSelectOutraFormaPagamentoCuidadoExtra,
  ] = useState<boolean>(false);

  const [openSelectPrazoBenficio, setOpenSelectPrazoBenficio] =
    useState<boolean>(false);

  const [openSelectValoresParticipantes, setOpenSelectValoresParticipantes] =
    useState<boolean>(false);

  const [openSelectPeculio, setOpenSelectPeculio] = useState<boolean>(false);
  const [openSelectPensao, setOpenSelectPensao] = useState<boolean>(false);
  const [openSelectFormaPagamentoCuidado, setOpenSelectFormaPagamentoCuidado] =
    useState<boolean>(false);

  const [
    openSelectOutraFormaPagamentoEmpresa,
    setOpenSelectOutraFormaPagamentoEmpresa,
  ] = useState<boolean>(false);

  const [
    openSelectOutraFormaPagamentoEmpresaCuidadoExtra,
    setOpenSelectOutraFormaPagamentoEmpresaCuidadoExtra,
  ] = useState<boolean>(false);

  const [
    openSelectOutraFormaPagamentoFuncionario,
    setOpenSelectOutraFormaPagamentoFuncionario,
  ] = useState<boolean>(false);

  const [
    openSelectOutraFormaPagamentoFuncionarioCuidadoExtra,
    setOpenSelectOutraFormaPagamentoFuncionarioCuidadoExtra,
  ] = useState<boolean>(false);

  const [openSelectTipoModalidade, setOpenSelectTipoModalidade] =
    useState<boolean>(false);

  const [openSelectLiberacaoReserva, setOpenSelectLiberacaoReserva] =
    useState<boolean>(false);

  const [openSelectSugestaoLiberacao, setOpenSelectSugestaoLiberacao] =
    useState<boolean>(false);

  const [openSelectOutraFormaLiberacao, setOpenSelectOutraFormaLiberacao] =
    useState<boolean>(false);

  const [openSelectTipoModalidadePGBL, setOpenSelectTipoModalidadePGBL] =
    useState<boolean>(false);

  const [openSelectTipoModalidadeVGBL, setOpenSelectTipoModalidadeVGBL] =
    useState<boolean>(false);

  const [openSelectTipoAporte, setOpenSelectTipoAporte] =
    useState<boolean>(false);

  const [openSelectPortabilidade, setOpenSelectPortabilidade] =
    useState<boolean>(false);

  const [openSelectComAporte, setOpenSelectComAporte] =
    useState<boolean>(false);

  const [openSelectIdadeAposentadoria, setOpenSelectIdadeAposentadoria] =
    useState<boolean>(false);

  const [openSelectPrazoContribuicao, setOpenSelectPrazoContribuicao] =
    useState<boolean>(false);

  const [openSelectAporteUnico, setOpenSelectAporteUnico] =
    useState<boolean>(false);

  const [openSelectDiaVencimento, setOpenSelectDiaVencimento] =
    useState<boolean>(false);

  const [openSelectDebitoEmConta, setOpenSelectDebitoEmConta] =
    useState<boolean>(false);

  const [openSelectFormaPagamentoRegra, setOpenSelectFormaPagamentoRegra] =
    useState<boolean>(false);

  const [openSelectRegraPagamentoCusteio, setBlackSelectFormaPagamentoRegra] =
    useState<boolean>(false);

  const [openFieldRecursoInstituidora, setOpenFieldInstituidora] =
    useState<boolean>(false);

  const selectRegraCalculo = (valor: string) => {
    if (valor === enumFop62.EnumselectRegraCalculo.aposentadoria) {
      setOpenSelectTipoBenficio(true);
    }
    if (valor === enumFop62.EnumselectRegraCalculo.contribuicaoMensal) {
      setOpenSelectTipoBenficio(false);
      setOpenSelectReversivel(false);
    }
    if (valor === enumFop62.EnumselectRegraCalculo.contribuicaoAnual) {
      setOpenSelectTipoBenficio(false);
      setOpenSelectReversivel(false);
    }
  };

  const selectFormaPagamento = (formaPagamento: string) => {
    if (formaPagamento === enumFop62.EnumFormaPagamento.pagamentoAverbado) {
      setOpenSelectValorContribuicao(true);
      setOpenSelectPlanoInstituido(false);
    }
    if (formaPagamento === enumFop62.EnumFormaPagamento.pagamentoInstituido) {
      setOpenSelectValorContribuicao(true);
      setOpenSelectPlanoInstituido(false);
    }
    if (
      formaPagamento === enumFop62.EnumFormaPagamento.pagamentoPlanoInstituido
    ) {
      setOpenSelectValorContribuicao(false);
      setOpenSelectOutraFormaPagamento(false);
      setOpenSelectValorFixo(false);
      setOpenSelectPlanoInstituido(true);
    }
  };

  const selectFormaPagamentoCuidado = (pagamentoCuidado: string) => {
    if (pagamentoCuidado === enumFop62.EnumFormaPagamento.pagamentoAverbado) {
      setOpenSelectValorContribuicaoCuidadoExtra(true);
      setOpenSelectPlanoInstituidoCuidadoExtra(false);
    }
    if (pagamentoCuidado === enumFop62.EnumFormaPagamento.pagamentoInstituido) {
      setOpenSelectValorContribuicaoCuidadoExtra(true);
      setOpenSelectPlanoInstituidoCuidadoExtra(false);
    }
    if (
      pagamentoCuidado === enumFop62.EnumFormaPagamento.pagamentoPlanoInstituido
    ) {
      setOpenSelectValorContribuicaoCuidadoExtra(false);
      setOpenSelectOutraFormaPagamentoCuidadoExtra(false);
      setOpenSelectPlanoInstituidoCuidadoExtra(true);
    }
  };

  const selectBeneficio = (beneficio: string) => {
    if (beneficio === enumFop62.EnumTipoBeneficios.rendaTemporaria) {
      setOpenSelectPrazoBenficio(true);
      setOpenSelectReversivel(false);
    }
    if (beneficio === enumFop62.EnumTipoBeneficios.prazoMinimo) {
      setOpenSelectPrazoBenficio(true);
      setOpenSelectReversivel(false);
    }
    if (beneficio === enumFop62.EnumTipoBeneficios.PrazoCerto) {
      setOpenSelectPrazoBenficio(true);
      setOpenSelectReversivel(false);
    }
    if (beneficio === enumFop62.EnumTipoBeneficios.vitalicia) {
      setOpenSelectPrazoBenficio(false);
      setOpenSelectReversivel(false);
    }
    if (beneficio === enumFop62.EnumTipoBeneficios.vitaliciareversivel) {
      setOpenSelectPrazoBenficio(false);
      setOpenSelectReversivel(true);
    }
  };

  const selectValoresParticipantes = (valoresParticipantes: string) => {
    if (valoresParticipantes === enumFop62.EnumConfirmatorio.afirmativo) {
      setOpenSelectValoresParticipantes(false);
    }
    if (valoresParticipantes === enumFop62.EnumConfirmatorio.negativo) {
      setOpenSelectValoresParticipantes(true);
      setOpenSelectValorContribuicao(false);
      setOpenSelectPlanoInstituido(false);
    }
  };

  const selectCuidadoExtra = (valorCuidadoExtra: string) => {
    if (valorCuidadoExtra === enumFop62.EnumCuidadoExtra.peculio) {
      setOpenSelectPensao(false);
      setOpenSelectPeculio(true);
      setOpenSelectFormaPagamentoCuidado(true);
    }
    if (valorCuidadoExtra === enumFop62.EnumCuidadoExtra.pensao) {
      setOpenSelectPeculio(false);
      setOpenSelectPensao(true);
      setOpenSelectFormaPagamentoCuidado(true);
    }
    if (valorCuidadoExtra === enumFop62.EnumCuidadoExtra.semCuidadoExtra) {
      setOpenSelectPensao(false);
      setOpenSelectPeculio(false);
      setOpenSelectFormaPagamentoCuidado(false);
      setOpenSelectValorFixoCuidadoExtra(false);
      setOpenSelectValorPercentualCuidadoExtra(false);
      setOpenSelectOutraFormaPagamentoCuidadoExtra(false);
    }
  };

  const selectValorContribuicao = (valorContribuicao: string) => {
    if (valorContribuicao === enumFop62.EnumTipoContribuicao.valorFixo) {
      setOpenSelectValorPercentual(false);
      setOpenSelectOutraFormaPagamento(false);
      setOpenSelectValorFixo(true);
    }
    if (valorContribuicao === enumFop62.EnumTipoContribuicao.valorBase) {
      setOpenSelectValorFixo(false);
      setOpenSelectOutraFormaPagamento(false);
      setOpenSelectValorPercentual(true);
    }
    if (valorContribuicao === enumFop62.EnumTipoContribuicao.outraForma) {
      setOpenSelectValorFixo(false);
      setOpenSelectValorPercentual(false);
      setOpenSelectOutraFormaPagamento(true);
    }
  };

  const selectValorContribuicaoCuidadoExtra = (
    valorContribuicaoCuidadoExtra: string,
  ) => {
    if (
      valorContribuicaoCuidadoExtra === enumFop62.EnumTipoContribuicao.valorFixo
    ) {
      setOpenSelectValorPercentualCuidadoExtra(false);
      setOpenSelectOutraFormaPagamentoCuidadoExtra(false);
      setOpenSelectValorFixoCuidadoExtra(true);
    }
    if (
      valorContribuicaoCuidadoExtra === enumFop62.EnumTipoContribuicao.valorBase
    ) {
      setOpenSelectValorFixoCuidadoExtra(false);
      setOpenSelectOutraFormaPagamentoCuidadoExtra(false);
      setOpenSelectValorPercentualCuidadoExtra(true);
    }
    if (
      valorContribuicaoCuidadoExtra ===
      enumFop62.EnumTipoContribuicao.outraForma
    ) {
      setOpenSelectValorFixoCuidadoExtra(false);
      setOpenSelectValorPercentualCuidadoExtra(false);
      setOpenSelectOutraFormaPagamentoCuidadoExtra(true);
    }
  };

  const selectValorContribuicaoEmpresa = (valorContribuicaoEmpresa: string) => {
    if (valorContribuicaoEmpresa === enumFop62.EnumTipoContribuicao.valorFixo) {
      setOpenSelectValorPercentualEmpresa(false);
      setOpenSelectOutraFormaPagamentoEmpresa(false);
      setOpenSelectValorFixoEmpresa(true);
    }
    if (valorContribuicaoEmpresa === enumFop62.EnumTipoContribuicao.valorBase) {
      setOpenSelectValorFixoEmpresa(false);
      setOpenSelectOutraFormaPagamentoEmpresa(false);
      setOpenSelectValorPercentualEmpresa(true);
    }
    if (
      valorContribuicaoEmpresa === enumFop62.EnumTipoContribuicao.outraForma
    ) {
      setOpenSelectOutraFormaPagamentoEmpresa(true);
      setOpenSelectValorFixoEmpresa(false);
      setOpenSelectValorPercentualEmpresa(false);
    }
  };

  const selectValorContribuicaoEmpresaCuidadoExtra = (
    valorContribuicaoEmpresaCuidadoExtra: string,
  ) => {
    if (
      valorContribuicaoEmpresaCuidadoExtra ===
      enumFop62.EnumTipoContribuicao.valorFixo
    ) {
      setOpenSelectValorPercentualEmpresaCuidadoExtra(false);
      setOpenSelectOutraFormaPagamentoEmpresaCuidadoExtra(false);
      setOpenSelectValorFixoEmpresaCuidadoExtra(true);
    }
    if (
      valorContribuicaoEmpresaCuidadoExtra ===
      enumFop62.EnumTipoContribuicao.valorBase
    ) {
      setOpenSelectValorFixoEmpresaCuidadoExtra(false);
      setOpenSelectOutraFormaPagamentoEmpresaCuidadoExtra(false);
      setOpenSelectValorPercentualEmpresaCuidadoExtra(true);
    }
    if (
      valorContribuicaoEmpresaCuidadoExtra ===
      enumFop62.EnumTipoContribuicao.outraForma
    ) {
      setOpenSelectOutraFormaPagamentoEmpresaCuidadoExtra(true);
      setOpenSelectValorFixoEmpresaCuidadoExtra(false);
      setOpenSelectValorPercentualEmpresaCuidadoExtra(false);
    }
  };

  const selectValorContribuicaoFuncionario = (
    valorContribuicaoFuncionario: string,
  ) => {
    if (
      valorContribuicaoFuncionario === enumFop62.EnumTipoContribuicao.valorFixo
    ) {
      setOpenSelectValorPercentualFuncionario(false);
      setOpenSelectOutraFormaPagamentoFuncionario(false);
      setOpenSelectValorFixoFuncionario(true);
    }
    if (
      valorContribuicaoFuncionario === enumFop62.EnumTipoContribuicao.valorBase
    ) {
      setOpenSelectOutraFormaPagamentoFuncionario(false);
      setOpenSelectValorFixoFuncionario(false);
      setOpenSelectValorPercentualFuncionario(true);
    }
    if (
      valorContribuicaoFuncionario === enumFop62.EnumTipoContribuicao.outraForma
    ) {
      setOpenSelectOutraFormaPagamentoFuncionario(true);
      setOpenSelectValorFixoFuncionario(false);
      setOpenSelectValorPercentualFuncionario(false);
    }
  };

  const selectValorContribuicaoFuncionarioCuidadoExtra = (
    valorContribuicaoFuncionarioCuidadoExtra: string,
  ) => {
    if (
      valorContribuicaoFuncionarioCuidadoExtra ===
      enumFop62.EnumTipoContribuicao.valorFixo
    ) {
      setOpenSelectValorPercentualFuncionarioCuidadoExtra(false);
      setOpenSelectOutraFormaPagamentoFuncionarioCuidadoExtra(false);
      setOpenSelectValorFixoFuncionarioCuidadoExtra(true);
    }
    if (
      valorContribuicaoFuncionarioCuidadoExtra ===
      enumFop62.EnumTipoContribuicao.valorBase
    ) {
      setOpenSelectOutraFormaPagamentoFuncionarioCuidadoExtra(false);
      setOpenSelectValorFixoFuncionarioCuidadoExtra(false);
      setOpenSelectValorPercentualFuncionarioCuidadoExtra(true);
    }
    if (
      valorContribuicaoFuncionarioCuidadoExtra ===
      enumFop62.EnumTipoContribuicao.outraForma
    ) {
      setOpenSelectOutraFormaPagamentoFuncionarioCuidadoExtra(true);
      setOpenSelectValorFixoFuncionarioCuidadoExtra(false);
      setOpenSelectValorPercentualFuncionarioCuidadoExtra(false);
    }
  };

  const selectFormaPagamentoFop63 = (formaPagamentoFop63: string) => {
    if (
      formaPagamentoFop63 === enumFop62.EnumFormaPagamento.pagamentoAverbado
    ) {
      setOpenSelectValorContribuicao(true);
      setOpenSelectTipoModalidade(true);
      setOpenSelectTipoModalidadePGBL(false);
      setOpenSelectTipoModalidadeVGBL(false);
      setOpenSelectLiberacaoReserva(false);
      setOpenSelectPlanoInstituido(false);
    }
    if (
      formaPagamentoFop63 === enumFop62.EnumFormaPagamento.pagamentoInstituido
    ) {
      setOpenSelectValorContribuicao(true);
      setOpenSelectLiberacaoReserva(true);
      setOpenSelectTipoModalidadePGBL(true);
      setOpenSelectTipoModalidadeVGBL(false);
      setOpenSelectTipoModalidade(false);
      setOpenSelectPlanoInstituido(false);
    }
    if (
      formaPagamentoFop63 ===
      enumFop62.EnumFormaPagamento.pagamentoPlanoInstituido
    ) {
      setOpenSelectTipoModalidadeVGBL(true);
      setOpenSelectLiberacaoReserva(true);
      setOpenSelectPlanoInstituido(true);
      setOpenSelectValorContribuicao(false);
      setOpenSelectOutraFormaPagamento(false);
      setOpenSelectTipoModalidadePGBL(false);
      setOpenSelectTipoModalidade(false);
      setOpenSelectValorFixo(false);
    }
  };

  const selecLiberacaoReserva = (liberacaoReserva: string) => {
    if (liberacaoReserva === enumFop62.EnumliberacaoReserva.sugestao) {
      setOpenSelectSugestaoLiberacao(true);
      setOpenSelectOutraFormaLiberacao(false);
    }
    if (liberacaoReserva === enumFop62.EnumliberacaoReserva.outraRegra) {
      setOpenSelectSugestaoLiberacao(false);
      setOpenSelectOutraFormaLiberacao(true);
    }
    if (liberacaoReserva === enumFop62.EnumliberacaoReserva.liberacaoTotal) {
      setOpenSelectSugestaoLiberacao(false);
      setOpenSelectOutraFormaLiberacao(false);
    }
    if (liberacaoReserva === enumFop62.EnumliberacaoReserva.semLiberacao) {
      setOpenSelectSugestaoLiberacao(false);
      setOpenSelectOutraFormaLiberacao(false);
    }
  };

  const selectRegraAporte = (valorRegraAporte: string) => {
    if (valorRegraAporte === enumFop62.EnumRegraAporte.semAporte) {
      setOpenSelectTipoAporte(false);
    }
    if (valorRegraAporte === enumFop62.EnumRegraAporte.comAporte) {
      setOpenSelectTipoAporte(true);
      setOpenSelectComAporte(true);
      setOpenSelectPortabilidade(false);
    }
    if (valorRegraAporte === enumFop62.EnumRegraAporte.portabilidade) {
      setOpenSelectTipoAporte(true);
      setOpenSelectPortabilidade(true);
      setOpenSelectComAporte(false);
    }
  };

  const selectTipoConcessao = (tipoConcessao: string) => {
    if (tipoConcessao === enumFop62.EnumTipoConcessao.aposentadoria) {
      setOpenSelectIdadeAposentadoria(true);
      setOpenSelectPrazoContribuicao(false);
    }
    if (tipoConcessao === enumFop62.EnumTipoConcessao.prazoContribuicao) {
      setOpenSelectPrazoContribuicao(true);
      setOpenSelectIdadeAposentadoria(false);
    }
  };

  const selecTipoPagamentoFatura = (tipoPagamentoFatura: string) => {
    if (tipoPagamentoFatura === enumFop62.EnumTipoPagamento.aporteUnico) {
      setOpenSelectAporteUnico(true);
      setOpenSelectDiaVencimento(false);
    }
    if (tipoPagamentoFatura === enumFop62.EnumTipoPagamento.aporteMensal) {
      setOpenSelectDiaVencimento(true);
      setOpenSelectAporteUnico(false);
    }
  };

  const selectDadosCobranca = (dadosCobranca: string) => {
    if (dadosCobranca === enumFop62.EnumDadosCobranca.boleto) {
      setOpenSelectDebitoEmConta(false);
    }
    if (dadosCobranca === enumFop62.EnumDadosCobranca.debito) {
      setOpenSelectDebitoEmConta(true);
    }
  };

  const selectRegraCalculoFOP62 = (valor: string) => {
    if (valor === enumFop62.EnumselectRegraCalculo.contribuicaoMensal) {
      setOpenSelectTipoBenficio(false);
      setOpenSelectReversivel(false);
    }
    if (valor === enumFop62.EnumselectRegraCalculo.contribuicaoAnual) {
      setOpenSelectTipoBenficio(false);
      setOpenSelectReversivel(false);
    }
  };

  const openModal = (): void => {
    setOpenModalFormulario(true);
  };

  const fecharModal = (): void => {
    setOpenModalFormulario(false);
  };

  const baixarArquivosFop = (item?: IResponseObterListaFopsAtivos): void => {
    if (!item) return;

    baixarArquivoFop(
      getTernaryResult(
        !!item.dadosArquivos,
        item.dadosArquivos?.[0]?.codigoIdentificadorUnico,
        '00000000',
      ),
      getTernaryResult(
        !!item.dadosArquivos,
        item.dadosArquivos?.[0]?.nomeArquivo,
        FOP_TEXTS.DOWNLOADS.PLANILHA_TESTE,
      ),
    );

    setFopAtivo(undefined);
    setOpenMotivo(undefined);
  };

  const fopClick = (fop: IResponseObterListaFopsAtivos): void => {
    if (fop.codigo === FOPS.CODIGOS.FOP_59) setFopAtivo(fop);
    else baixarArquivosFop(fop);
  };

  const abrirFop = (fop: IResponseObterListaFopsAtivos): void => {
    if (fop.motivoSolicitacaoAtivo) setOpenMotivo(fop);
    else fopClick(fop);
  };

  const handleFopAtivo = (value?: IResponseObterListaFopsAtivos): void => {
    setFopAtivo(value);
  };

  const handleOpenMotivo = (value?: IResponseObterListaFopsAtivos): void => {
    setOpenMotivo(value);
  };

  const listarFopsProps: IUseFormFopsPrevidencia['listarFopsProps'] = {
    dataToList: listaFops,
    fopAtivo,
    abrirFop,
    baixarArquivoFop: baixarArquivosFop,
  };
  const selectFormaPagamentoRegra = (FormaPagamentoRegraContratual: string) => {
    if (FormaPagamentoRegraContratual === 'debitoConta')
      setOpenSelectFormaPagamentoRegra(true);
  };

  const setselectFormaPagamentoCusteio = (
    FormaCusteioModalidadePlano: string,
  ) => {
    if (FormaCusteioModalidadePlano === 'averbado')
      setBlackSelectFormaPagamentoRegra(true);
  };

  const openFieldInstituidoraValor = (penalidade: string) => {
    if (penalidade === 'fundo') setOpenFieldInstituidora(true);
  };

  return {
    openMotivo,
    handleFopAtivo,
    handleOpenMotivo,
    openSelectTipoBenficio,
    openSelectReversivel,
    openSelectPlanoInstituido,
    openSelectValorContribuicao,
    openSelectValorContribuicaoCuidadoExtra,
    openSelectValorFixo,
    openSelectValorFixoCuidadoExtra,
    openSelectValorFixoEmpresa,
    openSelectValorFixoEmpresaCuidadoExtra,
    openSelectValorFixoFuncionario,
    openSelectValorPercentualCuidadoExtra,
    openSelectValorFixoFuncionarioCuidadoExtra,
    openSelectValorPercentual,
    openSelectValorPercentualEmpresa,
    openSelectValorPercentualEmpresaCuidadoExtra,
    openSelectValorPercentualFuncionario,
    openSelectValorPercentualFuncionarioCuidadoExtra,
    openSelectOutraFormaPagamento,
    openSelectPlanoInstituidoCuidadoExtra,
    openSelectPrazoBenficio,
    openSelectOutraFormaPagamentoCuidadoExtra,
    openSelectValoresParticipantes,
    openSelectPeculio,
    openSelectPensao,
    openSelectFormaPagamentoCuidado,
    openSelectOutraFormaPagamentoEmpresa,
    openSelectOutraFormaPagamentoEmpresaCuidadoExtra,
    openSelectOutraFormaPagamentoFuncionario,
    openSelectOutraFormaPagamentoFuncionarioCuidadoExtra,
    openSelectTipoModalidade,
    openSelectTipoModalidadePGBL,
    openSelectTipoModalidadeVGBL,
    openSelectLiberacaoReserva,
    openSelectSugestaoLiberacao,
    openSelectOutraFormaLiberacao,
    openSelectTipoAporte,
    openSelectPortabilidade,
    openSelectComAporte,
    openSelectIdadeAposentadoria,
    openSelectPrazoContribuicao,
    openSelectAporteUnico,
    openSelectDiaVencimento,
    openSelectDebitoEmConta,
    openSelectFormaPagamentoRegra,
    openFieldRecursoInstituidora,
    openSelectRegraPagamentoCusteio,
    loadingDownload,
    loadingDownloadArquivo,
    setLoadingDownloadArquivo,
    loadingFops,
    responseFops,
    textOutraFormaPagamento,
    textOutraFormaPagamentoEmpresa,
    textOutraFormaPgEmpresa,
    textOutraFormaPagamentoEmpresaCuidadoExtra,
    textOutraFormaPagamentoFuncionario,
    textOutraRegraLiberacaoDaReserva,
    textOutraFormaPagamentoFuncionarioCuidadoExtra,
    textOutraFormaPagamentoCuidadoExtra,
    textInformacoesComplementares,
    openModalFormulario,
    arquivoAnexoFop,
    arquivoAnexoFop63,
    regraFiles,
    baixarArquivoFop,
    fecharModal,
    setTextInformacoesComplementares,
    setTextOutraFormaPagamentoEmpresa,
    setTextOutraFormaPgEmpresa,
    setTextOutraFormaPagamentoCuidadoExtra,
    setTextOutraRegraLiberacaoDaReserva,
    setTextOutraFormaPagamentoFuncionarioCuidadoExtra,
    setTextOutraFormaPagamentoFuncionario,
    setTextOutraFormaPagamentoEmpresaCuidadoExtra,
    setTextOutraFormaPagamento,
    toastSuccess,
    selectRegraCalculo,
    selectFormaPagamento,
    selectFormaPagamentoCuidado,
    selectBeneficio,
    selectValoresParticipantes,
    selectValorContribuicao,
    selectValorContribuicaoCuidadoExtra,
    selectCuidadoExtra,
    selectValorContribuicaoEmpresa,
    selectValorContribuicaoEmpresaCuidadoExtra,
    selectValorContribuicaoFuncionario,
    selectValorContribuicaoFuncionarioCuidadoExtra,
    selectFormaPagamentoFop63,
    selecLiberacaoReserva,
    selectRegraAporte,
    selectTipoConcessao,
    selecTipoPagamentoFatura,
    selectDadosCobranca,
    selectRegraCalculoFOP62,
    selectFormaPagamentoRegra,
    openFieldInstituidoraValor,
    setselectFormaPagamentoCusteio,
    obterArquivoFOP,
    openModal,
    listarFopsProps,
  };
};
