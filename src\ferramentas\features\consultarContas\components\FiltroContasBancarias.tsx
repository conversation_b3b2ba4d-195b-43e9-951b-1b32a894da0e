import {
  But<PERSON>,
  <PERSON>,
  Display,
  Grid,
  Text,
  TextField,
} from '@cvp/design-system/react';
import masks from 'main/utils/masks';
import React, { useState } from 'react';
import { TFiltroContasBancariasProps } from 'ferramentas/features/consultarContas/types';

export const FiltroContasBancarias: React.FC<TFiltroContasBancariasProps> = ({
  onSubmit,
  onReset,
}) => {
  const [cpf, setCpf] = useState('');

  const handleLimpar = () => {
    setCpf('');

    if (onReset) onReset();
  };

  return (
    <Card>
      <Card.Content>
        <Text variant="body-medium3" color="text-light">
          Informe o CPF ou CNPJ do cliente para listar as contas bancárias
        </Text>
        <br />

        <Grid>
          <Grid.Item xs={1 / 3}>
            <TextField
              name="cpf"
              label="CPF/CNPJ"
              value={masks.cpf.mask(cpf)}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                setCpf(masks.cpf.unmask(event.target.value))
              }
            />
          </Grid.Item>
        </Grid>

        <Display style={{ marginTop: '1rem' }}>
          <Button variant="outlined" type="button" onClick={handleLimpar}>
            Limpar
          </Button>
          <Button
            data-testid="emitir-extrato"
            disabled={cpf.length < 11}
            onClick={() => onSubmit(cpf)}
          >
            Consultar
          </Button>
        </Display>
      </Card.Content>
    </Card>
  );
};
