import React from 'react';
import Table from 'main/components/Table';
import { tryGetMonetaryValueOrDefault } from 'main/utils/money';
import { TabelaHistoricoSimulacoesProps } from 'previdencia/features/SimulacaoRenda/types/SimulacaoRendaComponentProps';
import { COLUNAS_TABELA_HISTORICO_SIMULACAO_RENDA } from 'previdencia/features/SimulacaoRenda/constants/constants';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import { THistoricoSimulacaoRenda } from 'previdencia/features/SimulacaoRenda/types/historicoSimulacaoRenda';
import { Button } from 'previdencia/features/SimulacaoRenda/components/TabelaHistoricoSimulacoes/styles';

const TabelaHistoricoSimulacoes: React.FC<TabelaHistoricoSimulacoesProps> = ({
  dados,
  obterDetalhes,
}) => {
  const result: THistoricoSimulacaoRenda[] = tryGetValueOrDefault(
    [
      dados?.map(simulacao => ({
        ...simulacao,
        dthDiaSimulacaoFormatada: `${simulacao.dthDiaSimulacaoFormatada} - ${simulacao.dthHoraSimulacaoFormatada}`,
        nomTipoPagamento: (
          <Button
            variant="text"
            onClick={() => obterDetalhes(simulacao.seqSimulacao)}
          >
            <b>{simulacao.nomTipoPagamento}</b>
          </Button>
        ),
        vlrBeneficioLiquido: tryGetMonetaryValueOrDefault(
          simulacao.vlrBeneficioLiquido,
        ),
      })),
    ],
    [],
  );

  return (
    <Table
      noHeader
      responsive
      highlightOnHover
      striped
      data={result}
      columns={COLUNAS_TABELA_HISTORICO_SIMULACAO_RENDA}
      noDataComponent="Não há dados para a serem exibidos."
    />
  );
};

export default TabelaHistoricoSimulacoes;
