import { TextareaHTMLAttributes } from 'react';

export enum ResizeOption {
  NONE = 'none',
  BOTH = 'both',
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
}
export interface ITextAreaProps
  extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  value: string;
  label?: string;
  resize?: ResizeOption;
  height?: string;
  width?: string;
  error?: boolean;
  errorMessage?: string;
  placeholder?: string;
  disabled?: boolean;
  legend?: string;
}
