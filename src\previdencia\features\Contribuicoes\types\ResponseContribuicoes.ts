import React from 'react';

export type ResponseContribuicoes = {
  numCobranca: string;
  codStatusCobranca: string;
  descStatusCobranca: string;
  codMeioPagamento: string;
  dataVencimento: string;
  dataBaixa: string;
  valorCobrado: number;
  valorBaixado: number;
  permiteEmissaoBoleto: string;
  permiteReprocessar: string;
  permiteImpressao: string;
  linhaDigitavel: string;
  dataDentroLimite: boolean;
};

export type ContribuicoesConvertido = {
  numCobranca: string;
  status: string;
  descStatus: string;
  dataVencimento: string;
  dataBaixa: string;
  valorCobrado: number;
  permiteEmissaoBoleto: string;
  permiteReprocessar: string;
  permiteImpressao: string;
  linhaDigitavel: string;
  segundaVia?: boolean;
  codMeioPagamento: string;
};

export type TContribuicoesConvertidoDataColumn = {
  dataVencimento: string;
  dataPagamento: string;
  formaPagamento: string;
  valorContribuicao: string;
  statusRequisicao: string;
  boleto: string | React.JSX.Element;
};

export type PayloadContribuicoes = {
  numeroCertificado: string;
  Cpf: string;
  DataVencimentoInicial: string;
  DataVencimentoFinal: string;
};
