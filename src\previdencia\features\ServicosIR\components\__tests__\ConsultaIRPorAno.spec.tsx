import { cleanup, render, screen } from '@testing-library/react';
import AppContextProvider from 'main/contexts/AppContext';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import ConsultaIRPorAno from 'previdencia/features/ServicosIR/components/ConsultaIRPorAno';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import {
  ThemeProvider as ComponentesPosVendaProvider,
  ApiGatewayCvpProvider,
} from '@cvp/componentes-posvenda';
import { vi } from 'vitest';
import { api } from 'main/services/api';

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(() => ({
    loading: false,
    response: null,
    responseBinary: null,
    error: null,
    fetchData: vi.fn(),
    invocarApiGatewayCvpComToken: vi.fn(),
    invocarApiGatewayCvpComRetornoBinary: vi.fn(),
    setResponse: vi.fn(),
    invalidateCache: vi.fn(),
  })),
}));

describe('<ConsultaIRPorAno />', () => {
  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });

  const componentePadrao = (
    anoSelecionado: string,
    setAnoSelecionado: string,
    exerciciosIR: string[],
    messageError: boolean,
    obterIR: any,
    loading: boolean,
    certificadoSelecionado: string,
    recuperacaoEmailExecutada: boolean,
  ) => {
    const queryClient = new QueryClient();

    return render(
      <RenderPageWithThemeProviderAndRouter>
        <QueryClientProvider client={queryClient}>
          <ComponentesPosVendaProvider>
            <ApiGatewayCvpProvider
              configure={{
                defaultAxiosClient: api,
                authMode: 'DEFAULT_GI',
                usernameKey: '@portal-eco:nomeAcesso',
                storageMode: 'LOCAL',
                operationPath: 'PortalEconomiario/',
              }}
            >
              <AppContextProvider menus={[]}>
                <ConsultaIRPorAno
                  anoSelecionado={anoSelecionado}
                  setAnoSelecionado={() => setAnoSelecionado}
                  exerciciosIR={{ anosDisponiveis: exerciciosIR }}
                  messageError={messageError}
                  obterIR={() => obterIR}
                  loading={loading}
                  certificadoSelecionado={certificadoSelecionado}
                  recuperacaoEmailExecutada={recuperacaoEmailExecutada}
                />
              </AppContextProvider>
            </ApiGatewayCvpProvider>
          </ComponentesPosVendaProvider>
        </QueryClientProvider>
      </RenderPageWithThemeProviderAndRouter>,
    );
  };

  it('deveria existir pelo menos um informe de rendimento para este cpf', () => {
    const mockObterIr = vi.fn();

    componentePadrao(
      '2021',
      '2021',
      ['2021', '2020'],
      false,
      mockObterIr,
      false,
      '55301720163',
      false,
    );

    const buttonConsultarIR = screen.getAllByTestId('ButtonAnoConsultaIR');

    expect(buttonConsultarIR[0]).toBeInTheDocument();
  });

  it('deveria exibir a mensagem (Não existem informes de rendimento para este CPF.) se não existir pelo menos um informe de rendimento para este cpf', () => {
    const mockObterIr = vi.fn();

    componentePadrao(
      '2021',
      '2021',
      [],
      false,
      mockObterIr,
      false,
      '55301720163',
      false,
    );

    const msgConsultarIR = screen.getByTestId('msgNaoExisteIRCPF');

    expect(msgConsultarIR).toBeInTheDocument();
    expect(msgConsultarIR.innerHTML).toBe(
      'Não existem informes de rendimento para este CPF.',
    );
  });
});
