import DataTable from 'react-data-table-component';
import styled from 'styled-components';
import {
  Card,
  Disclaimer as DisclaimerDS,
  Tag as TagDs,
  Button,
} from '@cvp/design-system/react';
import { IDadosCobertura } from '../types/TransferenciaConsultar';

export const SectionTitle = styled.div({
  backgroundColor: '#f2f2f2',
  color: '#000',
  padding: 15,
  margin: '15px 0px',
});

export const SectionTitlePrimary = styled.div`
  background-color: ${({ theme }) => theme.color.brandPrimary.light};
  padding: 15px;
  p {
    color: ${({ theme }) => theme.color.neutral['08']};
  }
`;

export const Wrapper = styled.div`
  margin: 10px 0 60px;
`;
export const CardContent = styled(Card.Content)({
  '> p': {
    margin: '4px 0',
  },
});

export const Table = styled(DataTable)<IDadosCobertura>`
  .rdt_TableCol,
  .rdt_TableCell {
    font-size: ${({ theme }) => theme.font.size.sm};
    font-family: ${({ theme }) => theme.font.family.base};
  }

  .rdt_TableCol {
    font-weight: ${({ theme }) => theme.font.weight.lg};
    background-color: ${({ theme }) => theme.color.neutral['08']};
    color: ${({ theme }) => theme.color.brandPrimary.light};

    &:hover {
      color: ${({ theme }) => theme.color.brandPrimary.light};
    }
  }
`;

export const ListBeneficios = styled.div({
  display: 'flex',
  justifyContent: 'center',
  ul: {
    textAlign: 'left',
  },
});

export const AlertContainer = styled.div({
  backgroundColor: `#fbe5d6`,
  width: '80%',
  padding: '15px',
  margin: '10px 0',
});

export const AlertContainerFundosDiferenciados = styled(AlertContainer)({
  backgroundColor: `#FFE299`,
});

export const Disclaimer = styled(DisclaimerDS)({
  button: {
    opacity: 0,
  },
});

export const IconTransfer = styled.div(({ theme: { color } }) => ({
  marginTop: '5%',
  svg: {
    fill: color.brandSecondary.light,
  },
}));

export const ComprovanteSection = styled.div({
  border: '1px dotted #000',
  padding: '20px',
  width: '500px',
  marginTop: 20,
});

export const ButtonLink = styled(Button)({
  padding: 5,
  minWidth: 'initial',
  textDecoration: 'underline',
});

export const TagPremium = styled(TagDs)({
  backgroundColor: `#FFE299`,
  color: 'rgba(0, 0, 0, 0.8)',
  paddingLeft: '10px',
  paddingRight: '10px',
});

export const LabelItem = styled.div(({ theme }) => ({
  background: theme.color.neutral['08'],
  border: `1px solid ${theme.color.neutral['06']}`,
  textAlign: 'left',
  padding: `${theme.spacing.inline['4xs']} ${theme.spacing.inline['5xs']}`,
  color: theme.color.neutral['02'],
  fontWeight: theme.font.weight.xl,
}));
