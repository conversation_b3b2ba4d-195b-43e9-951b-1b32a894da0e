import { FormikErrors, FormikProps, FormikTouched } from 'formik';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import * as RESPONSE_TYPES from 'registroOcorrenciaASC/features/consultarOcorrencia/types/ConsultarOcorrenciaResponse';

export type FormConsultaOcorrenciaProps = {
  obterHistoricoSolicitacao: (dynamicPayload: {
    [key: string]: unknown;
  }) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.IHistoricoSolicitacaoResponse>
    | undefined
  >;
  setListaHistoricoSolicitacao: React.Dispatch<
    React.SetStateAction<RESPONSE_TYPES.IHistoricoSolicitacaoRetorno[]>
  >;
  loadingDadosHistoricoSolicitacao: boolean;
};

export type CardDetalhesOcorrenciaProps = {
  loadingDadosDetalhesOcorrencia: boolean;
  dadosDetalhesOcorrencia:
    | IHandleReponseResult<RESPONSE_TYPES.IDetalharOcorrenciaResponse>
    | undefined;
};

export type CardComplementosProps = {
  loadingDadosDetalhesOcorrencia: boolean;
  dadosDetalhesOcorrencia:
    | IHandleReponseResult<RESPONSE_TYPES.IDetalharOcorrenciaResponse>
    | undefined;
};

export type CardAnexosProps = {
  loadingDadosAnexos: boolean;
  loadingDadosDetalhesOcorrencia: boolean;
  dadosDetalhesOcorrencia:
    | IHandleReponseResult<RESPONSE_TYPES.IDetalharOcorrenciaResponse>
    | undefined;
  baixarAnexo: (idSalesforce: string) => void;
  loadingConsultaDocumentoAssinadoFop223: boolean;
};

export type CardConclusaoProps = {
  loadingDadosDetalhesOcorrencia: boolean;
  dadosDetalhesOcorrencia:
    | IHandleReponseResult<RESPONSE_TYPES.IDetalharOcorrenciaResponse>
    | undefined;
};

export type CardAdicionarComplementoProps = {
  formikDetalhesOcorrencia: FormikProps<IFormikValuesDetalhesOcorrencia>;
  handleDataTextarea: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  adicionarComplemento: () => Promise<void>;
  habilitarBtnAddComplemento: boolean;
  exibirBotaoComplementar: () => boolean;
};

export interface IFormikValuesDetalhesOcorrencia {
  conteudoTextarea: string;
  quantidadeCaracteresTextarea: number;
  arquivoAnexo: FileList;
}

export type IrParaDetalhesOcorrenciaParams = {
  numeroSolicitacao: string;
  cpfCnpj: string;
  numeroContrato: string | null;
};

export interface IFormikConsultaOcorrenciaValues {
  inputDate: {
    initialDate: null;
    finalDate: Date;
  };
  selected?: EnumBuscaPor;
  cpfCnpj: string;
  protocolo: string;
}

export enum EnumBuscaPor {
  CLIENTE = 'cliente',
  PROTOCOLO = 'protocolo',
}
export interface IRenderizarFormConsultaOcorrencia {
  values: IFormikConsultaOcorrenciaValues;
  errors: FormikErrors<IFormikConsultaOcorrenciaValues>;
  touched: FormikTouched<IFormikConsultaOcorrenciaValues>;
  maxDate: Date;
  maxDateRange: Date;
  isCamposFormConsultaPreenchidos: boolean;
  handleChange: FormikProps<IFormikConsultaOcorrenciaValues>['setFieldValue'];
  handleReset: FormikProps<IFormikConsultaOcorrenciaValues>['handleReset'];
  handleObterHistoricoSolicitacoes: () => void;
}

export interface IUseDetalharOcorrencia {
  loadingDadosAnexos: boolean;
  loadingDadosDetalhesOcorrencia: boolean;
  dadosDetalhesOcorrencia?: IHandleReponseResult<RESPONSE_TYPES.IDetalharOcorrenciaResponse>;
  baixarAnexo: (idSalesforce: string) => Promise<void>;
  formik: FormikProps<IFormikValuesDetalhesOcorrencia>;
  handleDataTextarea: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  adicionarComplemento: () => Promise<void>;
  habilitarBtnAddComplemento: boolean;
  exibirBotaoComplementar: () => boolean;
  exibirCardStatusConclusao: () => boolean;
  isReconsultarFop223: boolean;
  reconsultarFop223: () => Promise<void>;
  loadingConsultaDocumentoAssinadoFop223: boolean;
}
