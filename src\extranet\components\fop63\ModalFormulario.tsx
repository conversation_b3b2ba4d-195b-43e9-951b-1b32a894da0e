import * as DS from '@cvp/design-system/react';
import { FieldArray } from 'formik';
import { TextLabel } from 'main/styles/GlobalStyle';
import masks from 'main/utils/masks';
import * as Enum from '../../types/enum';
import { IModalFormulario } from '../../types/InterfacesFop63/IModalFormulario';
import { TabelaCuidadoExtra } from './TabelaCuidadoExtra';
import { TabelaValoresPlano } from './TabelaValoresPlano';
import { TabelaAposentadoria } from './TableAposentadora';

export const ModalFormulario: React.FC<IModalFormulario> = ({
  setFieldValue,
  values,
  formFop,
  handleBlur,
}) => {
  return (
    <DS.Modal
      show={formFop.openModalFormulario}
      onClose={formFop.fecharModal}
      style={{ maxWidth: `90%` }}
    >
      <DS.Display justify="center" style={{ flexDirection: 'column', margin: "0"}}>
        <DS.Text variant="body02-md" margin>
          <strong>{Enum.Titulos.AvisaDados}</strong>
        </DS.Text>
        <br />
        <DS.Card.Content style={{ margin: "0 0 0 0", width: "100%" }}>
          <DS.Accordion open>
            <DS.Accordion.Item title={Enum.Titulos.InformacoesGerais}>
              <DS.Grid>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.NomeEmpresa}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="nomeDaEmpresa">
                      {values.nomeDaEmpresa}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.AtividadePrincipal}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="atividadePrincipalEmpresa"
                    >
                      {values.atividadePrincipalEmpresa}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.CNPJ}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="cnpjEmpresa">
                      {masks.cnpj.mask(values.cnpjEmpresa)}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.Faturamento}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="faturamento">
                      {
                        Enum.SELECT_OPTIONS_FATURAMENTO.find(
                          x => x.key === values.faturamento,
                        )?.value
                      }
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.EmailInstitucional}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="emailInstitucional"
                    >
                      {values.emailInstitucional}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.Logradouro}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="logradouro">
                      {values.logradouro}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.Bairro}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="bairro">
                      {values.bairro}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.Cidade}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="cidade">
                      {values.cidade}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.UF}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="uf">
                      {values.uf}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.InformacoesGerais.CEP}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="cep">
                      {masks.cep.mask(values.cep)}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
              </DS.Grid>
            </DS.Accordion.Item>
          </DS.Accordion>
          <DS.Accordion open>
            <DS.Accordion.Item title={Enum.Titulos.DadosRepresentante}>
              <DS.Grid>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosRepresnetantes.NomeRepresentante}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="AgenciaFilial">
                      {values.nomeRepresentante}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosRepresnetantes.EmailRepresentante}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="agenciaSr">
                      {values.emailRepresentante}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosRepresnetantes.CargoRepresentante}
                    </TextLabel>
                    <DS.Text variant="body02-sm">
                      {values.cargoRepresentante}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
              </DS.Grid>
            </DS.Accordion.Item>
          </DS.Accordion>
          <DS.Accordion open>
            <DS.Accordion.Item title={Enum.Titulos.DadosAgencia}>
              <DS.Grid>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosAgencia.NumeroAgencia}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="numeroDaAgencia">
                      {values.numeroDaAgencia}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosAgencia.SuperintendênciaRegional}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="agenciaSr">
                      {values.agenciaSr}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosAgencia.NomeAgência}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="nomeDaAgencia">
                      {values.nomeDaAgencia}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosAgencia.Filial}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="AgenciaFilial">
                      {
                        Enum.SELECT_OPTIONS_FILIAL.find(
                          x => x.key === values.agenciaFilial,
                        )?.value
                      }
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosAgencia.MatrículaIndicador}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="matriculaDoIndicador"
                    >
                      {values.matriculaIndicador}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosAgencia.NomeIndicador}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="nomeIndicador">
                      {values.nomeIndicador}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
              </DS.Grid>
            </DS.Accordion.Item>
          </DS.Accordion>
          <DS.Accordion open>
            <DS.Accordion.Item title={Enum.Titulos.BeneficioBasico}>
              <DS.Grid>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.BeneficioBasico.NumeroParticipantes}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="numeroDeParticipantes"
                    >
                      {values.numeroDeParticipantes}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.BeneficioBasico.AporteInicial}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="regraCalculo">
                      {
                        Enum.SELECT_OPTIONS_REGRA_APORTE.find(
                          x => x.key === values.aporteInicial,
                        )?.value
                      }
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                {values.valorAporteInicial && (
                  <DS.Grid.Item xs={1} lg={1 / 2}>
                    <div>
                      <TextLabel variant="body02-sm">
                        {Enum.BeneficioBasico.ValorAporteInicial}
                      </TextLabel>
                      <DS.Text
                        variant="body02-sm"
                        data-testid="valorAporteInicial"
                      >
                        {values.valorAporteInicial}
                      </DS.Text>
                    </div>
                  </DS.Grid.Item>
                )}
                {values.valorPortabilidade && (
                  <DS.Grid.Item xs={1} lg={1 / 2}>
                    <div>
                      <TextLabel variant="body02-sm">
                        {Enum.BeneficioBasico.ValorPortabilidade}
                      </TextLabel>
                      <DS.Text
                        variant="body02-sm"
                        data-testid="valorPortabilidade"
                      >
                        {values.valorPortabilidade}
                      </DS.Text>
                    </div>
                  </DS.Grid.Item>
                )}
              </DS.Grid>
            </DS.Accordion.Item>
          </DS.Accordion>
          <DS.Accordion open>
            <DS.Accordion.Item title={Enum.Titulos.RegrasContratuais}>
              <DS.Grid.Item xs={1} lg={1 / 2}>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.FormaCusteioModalidadePlano}
                  </TextLabel>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="formaCusteioModalidade"
                  >
                    {
                      Enum.SELECT_OPTION_CUSTEIO_MODALIDADE.find(
                        x => x.key === values.formaCusteioModalidadePlano,
                      )?.value
                    }
                  </DS.Text>
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1 / 2}>
                <TextLabel variant="body01-sm">
                  {Enum.Titulos.ValorContribuicao}
                </TextLabel>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1}>
                <div>
                  <TextLabel variant="body01-sm">
                    {Enum.RegrasContratuais.Aposentadoria}
                  </TextLabel>
                  <FieldArray name="valoresContribuicao">
                    {() => (
                      <TabelaAposentadoria
                        setFieldValue={setFieldValue}
                        values={values}
                      />
                    )}
                  </FieldArray>
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1}>
                <div>
                  <TextLabel variant="body01-sm">
                    {Enum.RegrasContratuais.CuidadoExtra}
                  </TextLabel>
                  <TabelaCuidadoExtra
                    setFieldValue={setFieldValue}
                    values={values}
                  />
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1 / 2}>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.PagamentoContribuicao}
                  </TextLabel>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="pagamentoContribuicao"
                  >
                    {
                      Enum.SELECT_OPTIONS_PAGAMENTO_cONTRIBUICAO.find(
                        x => x.key === values.pagamentoContribuicaoParticipante,
                      )?.value
                    }
                  </DS.Text>
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1 / 2}>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.DiaPagamento}
                  </TextLabel>
                  <DS.Text variant="body02-sm" data-testid="diaPagamento">
                    {
                      Enum.SELECT_OPTION_DIA_PAGAMENTO.find(
                        x => x.key === values.diaPagamento,
                      )?.value
                    }
                  </DS.Text>
                </div>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.FormaPagamento}
                  </TextLabel>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="formaPagamentoRegraContratual"
                  >
                    {
                      Enum.SELECT_FORMA_PAGAMENTO_REGRA_CONTRATUAL.find(
                        x => x.key === values.formaPagamentoRegraContratual,
                      )?.value
                    }
                  </DS.Text>
                </div>
              </DS.Grid.Item>

              <DS.Grid.Item xs={1} lg={1}>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.PerdaVinculo}
                  </TextLabel>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="formaPagamentoRegraContratual"
                  >
                    {
                      Enum.SELECT_OPTION_PERDA_VINCULO.find(
                        x => x.key === values.perdaVinculo,
                      )?.value
                    }
                  </DS.Text>
                </div>
                <div>
                  <TabelaValoresPlano
                    setFieldValue={setFieldValue}
                    values={values}
                    handleBlur={handleBlur}
                  />
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1}>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.JustaCausa}
                  </TextLabel>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="formaPagamentoRegraContratual"
                  >
                    {
                      Enum.SELECT_DEMISSAO_JUSTA_CAUSA.find(
                        x => x.key === values.demisaoJustaCausa,
                      )?.value
                    }
                  </DS.Text>
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1 / 2}>
                <TextLabel variant="body02-sm">
                  {Enum.Titulos.Penalidades}
                </TextLabel>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1}>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.InformativoPenalidades}
                  </TextLabel>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="formaPagamentoRegraContratual"
                  >
                    {
                      Enum.SELET_OPTIONS_PENALIDADE.find(
                        x => x.key === values.penalidades,
                      )?.value
                    }
                  </DS.Text>
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1 / 2}>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.DistribuicaoContaColetiva}
                  </TextLabel>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="formaPagamentoRegraContratual"
                  >
                    {
                      Enum.SELECT_OPTIONS_DISTRIBUICAO_CONTA_COLETIVA.find(
                        x => x.key === values.distribuicaoContaColetiva,
                      )?.value
                    }
                  </DS.Text>
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1 / 2}>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.Titulos.RegraAposentadoria}
                  </TextLabel>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.IdadeAposentadoria}
                  </TextLabel>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="formaPagamentoRegraContratual"
                  >
                    {values.idadeAposentadoria}
                  </DS.Text>
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1 / 2}>
                <div>
                  <TextLabel variant="body02-sm">
                    {Enum.RegrasContratuais.TempoMinimoPlano}
                  </TextLabel>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="formaPagamentoRegraContratual"
                  >
                    {values.tempoMinimoPlano}
                  </DS.Text>
                </div>
              </DS.Grid.Item>
              <DS.Grid.Item xs={1} lg={1 / 2}>
                <TextLabel variant="body02-sm">
                  {Enum.Titulos.AplicacaoRecursosInstituidora}
                </TextLabel>
                <TextLabel variant="body02-sm">
                  {Enum.RegrasContratuais.RecursosInstituidoras}
                </TextLabel>
                <DS.Text
                  variant="body02-sm"
                  data-testid="formaPagamentoRegraContratual"
                  label={Enum.RegrasContratuais.RecursosInstituidoras}
                >
                  {
                    Enum.SELECT_OPTION_RECURSOS_INSTITUIDORA.find(
                      x => x.key === values.recursosInstituidora,
                    )?.value
                  }
                </DS.Text>
              </DS.Grid.Item>
            </DS.Accordion.Item>
          </DS.Accordion>
          <DS.Accordion open>
            <DS.Accordion.Item title={Enum.Titulos.InformacoesComplementares}>
              <DS.Grid>
                <DS.Grid.Item xs={1} lg={1}>
                  <DS.Text
                    variant="body02-sm"
                    data-testid="formaPagamentoRegraContratual"
                  >
                    {values.textInformacoesComplementares}
                  </DS.Text>
                </DS.Grid.Item>
              </DS.Grid>
            </DS.Accordion.Item>
          </DS.Accordion>
          <DS.Accordion open>
            <DS.Accordion.Item title={Enum.Titulos.AssinaturaContrato}>
              <DS.Grid>
                <DS.Grid.Item xs={1} lg={1 / 3}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.AssinaturaContrato.NomeRepresentantelegal}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="nomeRepresentanteLegal"
                    >
                      {values.nomeRepresentanteLegal}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 3}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.AssinaturaContrato.CPFRepresentantelegal}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="cpfRepresentanteLegal"
                    >
                      {values.cpfRepresentanteLegal}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 3}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.AssinaturaContrato.EmailRepresentanteLegal}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="emailRepresentanteLegal"
                    >
                      {values.emailRepresentanteLegal}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 3}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.AssinaturaContrato.NomeTestemunha}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="nomePrimeiraTestemunha"
                    >
                      {values.nomePrimeiraTestemunha}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 3}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.AssinaturaContrato.CPFTestemunha}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="cpfPrimeiraTestemunha"
                    >
                      {values.cpfPrimeiraTestemunha}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 3}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.AssinaturaContrato.EmailTestemunha}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="emailPrimeiraTestemunha"
                    >
                      {values.emailPrimeiraTestemunha}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
              </DS.Grid>
            </DS.Accordion.Item>
          </DS.Accordion>
          <DS.Accordion open>
            <DS.Accordion.Item title={Enum.Titulos.DadosResponsavel}>
              <DS.Grid>
                <DS.Grid.Item xs={1} lg={1 / 3}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosResponsavelPreenchimento.NomeCompleto}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="nomeCompletoResponsavel"
                    >
                      {values.nomeCompletoResponsavel}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 3}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosResponsavelPreenchimento.Telefone}
                    </TextLabel>
                    <DS.Text
                      variant="body02-sm"
                      data-testid="telefoneResponsavel"
                    >
                      {values.telefoneResponsavel}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
                <DS.Grid.Item xs={1} lg={1 / 3}>
                  <div>
                    <TextLabel variant="body02-sm">
                      {Enum.DadosResponsavelPreenchimento.Email}
                    </TextLabel>
                    <DS.Text variant="body02-sm" data-testid="emailResponsavel">
                      {values.emailResponsavel}
                    </DS.Text>
                  </div>
                </DS.Grid.Item>
              </DS.Grid>
            </DS.Accordion.Item>
          </DS.Accordion>
        </DS.Card.Content>
        <DS.Display justify="center" style={{ marginTop: 20 }}>
          <DS.Button
            variant="secondary"
            onClick={formFop.fecharModal}
            data-testid="voltar-ok"
          >
            {Enum.Botoes.Ok}
          </DS.Button>
        </DS.Display>
      </DS.Display>
    </DS.Modal>
  );
};
