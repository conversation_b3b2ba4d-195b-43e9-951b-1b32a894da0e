import React from 'react';
import { Button, Display, Text, Modal } from '@cvp/design-system/react';
import { TModalMotivoFop } from 'main/features/Administracao/types/IFops';
import { useModalMotivoUsoFops } from 'extranet/hooks/useModalMotivoFops';
import TextArea from 'main/components/form/TextArea';
import ParseHTML from 'main/components/ParseHTML/ParseHTML';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import { FOP_TEXTS } from 'extranet/features/fops/constants/consts';
import { ResizeOption } from 'main/components/form/TextArea/types';

const ModalMotivoUsoFop: React.FC<TModalMotivoFop> = ({
  fop,
  segmento,
  onClose,
  onConfirm,
}) => {
  const {
    user,
    descricaoMotivo,
    labelContador,
    desabilitarBotaoSeguir,
    errorDescricaoMotivo,
    handleDescricaoMotivo,
    handleSubmit,
    handleClose,
  } = useModalMotivoUsoFops({ segmento, fop, onClose, onConfirm });

  return (
    <Modal show={!!fop} onClose={handleClose}>
      <Text variant="headline-04" color="primary" margin>
        {FOP_TEXTS.MOTIVO.ATENCAO}
      </Text>

      <ParseHTML
        html={tryGetValueOrDefault([fop?.descricaoMotivoSolicitacao], '')}
      />

      <Display type="inline-block">
        <TextArea
          value={descricaoMotivo}
          legend={labelContador}
          resize={ResizeOption.NONE}
          height="200px"
          error={!!errorDescricaoMotivo}
          errorMessage={errorDescricaoMotivo}
          onChange={handleDescricaoMotivo}
        />
      </Display>
      <Text color="primary">Matrícula: {user.nomeAcesso}</Text>
      <Display type="inline-block">
        <Button variant="outlined" onClick={handleClose}>
          {FOP_TEXTS.MOTIVO.CANCELAR}
        </Button>
        <Button
          variant="primary"
          disabled={desabilitarBotaoSeguir}
          onClick={handleSubmit}
        >
          {FOP_TEXTS.MOTIVO.SEGUIR}
        </Button>
      </Display>
    </Modal>
  );
};

export default ModalMotivoUsoFop;
