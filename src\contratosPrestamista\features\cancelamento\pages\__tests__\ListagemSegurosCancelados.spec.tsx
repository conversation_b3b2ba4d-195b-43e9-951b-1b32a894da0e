import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { api } from 'main/services';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import { act } from 'react-dom/test-utils';
import ListagemSegurosCancelados from '../ListagemSegurosCancelados';

describe('Prestamista - Listagem de cancelamentos', () => {
  it('renderiza a tela e verifica se os dados foram apresentados', () => {
    vi.spyOn(api, 'post').mockReturnValue(
      Promise.resolve({
        data: {
          dados: {
            entidade: [
              {
                numeroPropostaEmissao: null,
                numeroPropostaEstipulante: '389945',
                codTipoOperacaoFinanceira: 'R',
                statusMotivoParcela: '3',
                statusCobranca: '1',
                statusReversao: 'DENTRO DO PRAZO DE RETENÇÃO',
                txtDescricaoReversao: 'EM PROCESSAMENTO              ',
                dtaFimReversao: '2022-02-20T00:00:00',
                qtdDiasReversao: '5',
                numContrato: '28272964',
                numeroLinhaDoProduto: '2',
                numeroPesOperador: null,
                codigoDoEstipulante: '2',
                numeroProposta: null,
                cpfCnpj: '61768278091',
                numeroContratoTerceiro: null,
                valorImportanciaSegurada: 4499100,
                valorPremio: 423170,
                dataHoraEmissaoDaProposta: '2017-06-13T00:00:00',
                codigoAgenciaVenda: '3445',
                descricaoStatusProposta: null,
                dataInicioVigencia: null,
                dataFimVigencia: null,
                valorPremioLiquido: null,
                descricaoPeriodicidadeCobranca: null,
                numeroCertificado: null,
                nomeSegurado:
                  'MARCELO DA CUNHA COLL OLIVEIRA                                                                      ',
                dddCelular: null,
                numeroCelular: null,
                email: null,
                dataCancelamento: '2022-02-11T00:00:00',
                statusContrato: 'E',
              },
            ],
            sucesso: true,
            mensagens: [
              {
                codigo: 'INFCBSVC0',
                descricao: 'Validação da conta bancária realizada com sucesso.',
              },
            ],
          },
        },
      }),
    );

    const { container } = render(
      <RenderPageWithThemeProviderAndRouter>
        <QueryClientProvider client={new QueryClient()}>
          <ListagemSegurosCancelados />
        </QueryClientProvider>
      </RenderPageWithThemeProviderAndRouter>,
    );

    waitFor(
      () => {
        expect(container.querySelector('div[role="rowgroup"]')).not.toBeNull();
      },
      { timeout: 1000 },
    );
  });

  it('clica na ação de reversão e verifica se a modal aparece', () => {
    vi.spyOn(api, 'post').mockReturnValue(
      Promise.resolve({
        data: {
          dados: {
            entidade: [
              {
                numeroPropostaEmissao: null,
                statusMotivoParcela: '3',
                numeroPropostaEstipulante: '389945',
                statusReversao: 'DENTRO DO PRAZO DE RETENÇÃO',
                codTipoOperacaoFinanceira: 'R',
                txtDescricaoReversao: 'EM PROCESSAMENTO              ',
                statusCobranca: '1',
                numContrato: '28272964',
                dtaFimReversao: '2022-02-20T00:00:00',
                qtdDiasReversao: '5',
                codigoDoEstipulante: '2',
                numeroLinhaDoProduto: '2',
                cpfCnpj: '61768278091',
                numeroPesOperador: null,
                valorImportanciaSegurada: 4499100,
                numeroProposta: null,
                numeroContratoTerceiro: null,
                codigoAgenciaVenda: '3445',
                valorPremio: 423170,
                dataHoraEmissaoDaProposta: '2017-06-13T00:00:00',
                dataFimVigencia: null,
                descricaoStatusProposta: null,
                dataInicioVigencia: null,
                numeroCertificado: null,
                valorPremioLiquido: null,
                descricaoPeriodicidadeCobranca: null,
                dddCelular: null,
                nomeSegurado:
                  'MARCELO DA CUNHA COLL OLIVEIRA                                                                      ',
                numeroCelular: null,
                statusContrato: 'E',
                email: null,
                dataCancelamento: '2022-02-11T00:00:00',
              },
            ],
            sucesso: true,
            mensagens: [
              {
                codigo: 'INFCBSVC0',
                descricao: 'Validação da conta bancária realizada com sucesso.',
              },
            ],
          },
        },
      }),
    );

    const { container } = render(
      <RenderPageWithThemeProviderAndRouter>
        <QueryClientProvider client={new QueryClient()}>
          <ListagemSegurosCancelados />
        </QueryClientProvider>
      </RenderPageWithThemeProviderAndRouter>,
    );

    waitFor(
      () => {
        expect(container.querySelector('div[role="rowgroup"]')).not.toBeNull();
        const buttonReversao = container.querySelectorAll(
          'button[id^="reversaoButton"',
        )[0];
        expect(buttonReversao).not.toBeNull();
        act(() => {
          userEvent.click(buttonReversao);
        });

        waitFor(
          () => {
            const modal = screen.getByTestId('modalReversaoCancelamento');
            expect(modal).toBeInTheDocument();
          },
          { timeout: 1000 },
        );
      },
      { timeout: 1000 },
    );
  });
});
