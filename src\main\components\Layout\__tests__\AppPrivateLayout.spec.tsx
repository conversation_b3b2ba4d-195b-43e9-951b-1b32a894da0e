import { render, screen } from '@testing-library/react';
import ThemeProvider from 'main/components/ThemeProvider';
import AppContextProvider from 'main/contexts/AppContext';
import { USER_PROFILES } from 'main/features/Auth/config/userProfiles';
import { BrowserRouter as Router } from 'react-router-dom';
import AppPrivateLayout from '../AppPrivateLayout';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { vi } from 'vitest';
import { api } from 'main/services/api';

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ComponentesPosVendaProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

describe('Layout.AppPrivateLayout', () => {
  const queryClient = new QueryClient();

  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

  beforeEach(() => {
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: null,
      responseBinary: null,
      error: null,
      fetchData: vi.fn(),
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('deve renderizar o componente AppPrivateLayout', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <ComponentesPosVendaProvider>
          <ApiGatewayCvpProvider
            configure={{
              defaultAxiosClient: api,
              authMode: 'DEFAULT_GI',
              usernameKey: '@portal-eco:nomeAcesso',
              storageMode: 'LOCAL',
              operationPath: 'PortalEconomiario/',
            }}
          >
            <ThemeProvider>
              <AppContextProvider
                menus={[
                  {
                    label: 'Consultar Cliente',
                    alt: 'consultarCliente',
                    path: '/cliente',
                    icon: 'search',
                    roles: [
                      USER_PROFILES.ANALISTA_TI,
                      USER_PROFILES.ECONOMIARIO,
                      USER_PROFILES.ANALISTA_POS_VENDA,
                    ],
                  },
                ]}
              >
                <Router>
                  <AppPrivateLayout>
                    <div data-testid="page-children">Página Teste</div>
                  </AppPrivateLayout>
                </Router>
              </AppContextProvider>
            </ThemeProvider>
          </ApiGatewayCvpProvider>
        </ComponentesPosVendaProvider>
      </QueryClientProvider>,
    );

    const pageChildren = screen.getByTestId('page-children');
    expect(pageChildren).toBeInTheDocument();
    expect(pageChildren).toHaveTextContent('Página Teste');
  });
});
