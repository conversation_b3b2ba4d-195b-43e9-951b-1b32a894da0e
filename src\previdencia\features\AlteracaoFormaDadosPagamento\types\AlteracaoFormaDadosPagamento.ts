import { FormikProps } from 'formik';

import { ICliente } from 'main/types/ICliente';
import { ResponseDadosCertificadosPorCpf } from 'previdencia/types/DadosCertificado';
import React from 'react';

export type ListaTiposContasBancarias = {
  descricaoPagamento: string;
  digitoConta: string;
  idCanalRecuperado: string;
  numeroAgencia: string;
  numeroBanco: string;
  numeroConta: string;
  metodoPagamento: string;
  tipoContaBanco: string;
  tipoPagamentoId: string;
  marcarBanco?: React.JSX.Element;
  operacao?: string;
  conta?: string;
};

export type TTipoContasBancarias = {
  certificado?: string;
  formaPagamento?: React.JSX.Element;
  banco?: string;
  agencia?: string;
  conta?: string;
};

export type TTipoContasBancariasDataColumn = ListaTiposContasBancarias &
  TTipoContasBancarias;

export type CanalDados = {
  canal: string;
  isDisabled: boolean;
};

export type DadosPagamento = {
  tipoPagamentoId: string;
  tipoContaBanco: string;
  numeroBanco: string;
  numeroAgencia: string;
  digitoAgencia?: string;
  numeroConta: string;
  digitoConta: string;
  canalId?: string;
};

export type ResponseRecuperarContribuicoesCertificado = {
  beneficioContribuicaoCertificado: [
    {
      beneficioId: string;
      categoriaContribuicao: string;
      dataPagamento: string;
      dataProximoPagamento: string;
      descricaoBeneficio: string;
      descricaoRenda: string;
      diaPagamento: string;
      fatorRenda: string;
      identificadorLegal: string;
      indRevisaoPermitida: string;
      nomeContribuicao: string;
      origemContribuicao: string;
      periodicidade: string;
      planoId: string;
      statusBeneficio: string;
      statusContribuicao: string;
      subStatusBeneficio: string;
      termoDesejado: string;
      tipoBeneficio: string;
      tipoCobertura: string;
      tipoContribuicao: string;
      tipoRenda: string;
      valorBeneficio: string;
      valorBeneficioEsperado: string;
      valorContribuicaoEsperado: string;
      valorPagamento: string;
    },
  ];
  contaId: string;
  cpfPessoaCertificado: string;
  descricaoProduto: string;
  empresaId: string;
  nomePessoaCertificado: string;
  produtoAgredado: string;
  produtoId: string;
  subCategoriaProduto: string;
  valorTotalSaldo: string;
};

export type ResponseResponsavelFinanceiro = {
  cpf: string;
  pessoaNome: string;
  idPessoa: string;
  indPessoaFisica: string;
  idPessoaFisica: string;
};

export interface IComprovanteNovaContaProps {
  dadosNovaConta: DadosPagamento | undefined;
  dadoscontaAntiga: ResponseDadosCertificadosPorCpf | undefined;
  cpfcnpj: string | undefined;
  voltar: () => void;
}

export interface IDadosPagamentoAtualProps {
  dadosCertificado: ResponseDadosCertificadosPorCpf | undefined;
  dadosResponsavel?: {
    nome?: string;
    cpf?: string;
  };
  valor: string | undefined;
  alterarConta: () => void;
}

export interface IFormikValuesAlteracaoContaDebito {
  agencia: string;
  conta: string;
  operacao: string;
  digitoBanco: string;
}

export type AlteracaoContaDebitoProps = {
  voltar: () => void;
  dadosResponsavel?: {
    nome?: string;
    cpf?: string;
  };
  dadosBancos: ListaTiposContasBancarias[] | undefined;
  alterar: (
    canalId: string,
    dadosNovaConta?: DadosPagamento | undefined,
  ) => void;
};

export type ExibirFormularioProps = (
  exibir: boolean,
  opcao1: string,
  opcao2: string,
) => string;

export interface IFormAdicionarContaProps {
  formik: FormikProps<IFormikValuesAlteracaoContaDebito>;
}

export interface ITabelaContasBancariasProps {
  dados: ListaTiposContasBancarias[] | undefined;
  canal: CanalDados;
  obterContaCadastradaSelecionada: (row: ListaTiposContasBancarias) => void;
}

export interface IColunasTabelaContasBancariasParams {
  canal: CanalDados;
  obterContaCadastradaSelecionada: (row: ListaTiposContasBancarias) => void;
}

export interface IComprovanteAlteracaoProps {
  dadosParticipante: ResponseDadosCertificadosPorCpf | undefined;
  dadosListaBancos: ListaTiposContasBancarias[] | undefined;
  canalId: string;
  dadosNovaConta: DadosPagamento | undefined;
  voltar: () => void;
}

export interface IConfirmarAdicionarNovaContaProps {
  setConfirmacaoDadosNovaConta: React.Dispatch<React.SetStateAction<boolean>>;
  novosDadosConta: ListaTiposContasBancarias[] | undefined;
  certificado: string;
  voltar: () => void;
  fetchingCriarCanalPagamento: boolean;
}

export interface IDadosSolicitacaoProps {
  voltar?: () => void;
}

export interface IDadosBancariosFactoryParams {
  dadosCertificado: ResponseDadosCertificadosPorCpf | undefined;
  cliente: ICliente;
}

export interface IDadosContaFactoryParams {
  cliente: ICliente;
  novosDadosConta: ListaTiposContasBancarias[] | undefined;
}

export interface IConfirmarAlteracaoProps {
  proximo: () => void;
  voltar: () => void;
  dadosListaBancos: ListaTiposContasBancarias[] | undefined;
  canalId: string;
  idPessoa: string | undefined;
  dadosNovaConta: DadosPagamento | undefined;
  dadosCertificado: ResponseDadosCertificadosPorCpf | undefined;
  tipoBeneficio: string | undefined;
}
