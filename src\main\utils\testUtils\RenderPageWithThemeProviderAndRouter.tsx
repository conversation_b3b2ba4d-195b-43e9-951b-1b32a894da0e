import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ThemeProvider from 'main/components/ThemeProvider';
import React, { PropsWithChildren } from 'react';
import { BrowserRouter as Router } from 'react-router-dom';

/**
 * Renderiza o componente com ThemeProvider e BrowserRoute
 * @param param0
 * @returns
 */
export const RenderPageWithThemeProviderAndRouter: React.FC<
  PropsWithChildren
> = ({ children }) => {
  const queryClient = new QueryClient();
  return (
    <ThemeProvider>
      <QueryClientProvider client={queryClient}>
        <Router>{children}</Router>
      </QueryClientProvider>
    </ThemeProvider>
  );
};
