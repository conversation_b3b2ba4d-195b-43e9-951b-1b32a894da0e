import { act, renderHook } from '@testing-library/react-hooks';
import { api } from 'main/services';
import { getHookWrapper } from 'main/utils/__tests__/queryCliente';
import { useUploadProposta } from '../useUploadProposta';

interface MockFile {
  name: string;
  body: string;
  mimeType: string;
}

const createFileFromMockFile = (file: MockFile): File => {
  const blob = new Blob([file.body], { type: file.mimeType }) as any;
  blob.lastModifiedDate = new Date();
  blob.name = file.name;
  return blob as File;
};

export const createMockFileList = (files: MockFile[]) => {
  const fileList = {
    length: files.length,
    item(index: number): File {
      return fileList[index];
    },
  } as unknown as FileList;
  files.forEach((file, index) => {
    fileList[index] = createFileFromMockFile(file);
  });

  return fileList;
};

const getPropostaMock = () => ({
  numeroProposta: '80630770027861',
  dataHoraEmissaoDaProposta: '2019-12-20T00:00:00',
  numeroLinhaDoProduto: '2',
  codigoAgenciaVenda: 630,
  cpfCnpj: '26399419840',
  codigoDoEstipulante: '2',
});

const getFileListMock = () => {
  return createMockFileList([
    {
      body: 'test',
      mimeType: 'imagem/png',
      name: 'test.png',
    },
  ]);
};

describe('prestamista - hooks/useUploadProposta', () => {
  const wrapper = getHookWrapper();
  it('deve realizar um upload com sucesso', async () => {
    vi.spyOn(api, 'post').mockReturnValue(
      Promise.resolve({
        data: {
          dados: {
            sucesso: true,
            entidade: null,
          },
        },
      }),
    );

    const { result } = renderHook(() => useUploadProposta(), { wrapper });
    let resultadoUpload = null;

    await act(async () => {
      resultadoUpload = await result.current.upload(
        getPropostaMock(),
        getFileListMock(),
      );
    });

    expect(result.current.loading).toBe(false);
    expect(resultadoUpload).toBeTruthy();
  });

  it('deve realizar um upload com falha', async () => {
    vi.spyOn(api, 'post').mockReturnValue(
      Promise.resolve({
        data: {
          dados: {
            sucesso: false,
            entidade: null,
          },
        },
      }),
    );

    const { result } = renderHook(() => useUploadProposta(), { wrapper });
    let resultadoUpload = null;

    await act(async () => {
      resultadoUpload = await result.current.upload(
        getPropostaMock(),
        getFileListMock(),
      );
    });

    expect(result.current.loading).toBe(false);
    expect(resultadoUpload).toBeFalsy();
  });

  it('deve lançar uma exceção ao realizar o upload', async () => {
    vi.spyOn(api, 'post').mockReturnValue(
      Promise.reject(new Error('ocorreu um erro ao realizar o upload')),
    );

    const { result } = renderHook(() => useUploadProposta(), { wrapper });

    try {
      await act(async () => {
        await result.current.upload(getPropostaMock(), getFileListMock());
      });
    } catch (error) {
      expect(error).toEqual('ocorreu um erro ao realizar o upload');
    }
  });
});
