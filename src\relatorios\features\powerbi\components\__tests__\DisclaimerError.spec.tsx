import { cleanup, render, screen } from '@testing-library/react';
import ThemeProvider from 'main/components/ThemeProvider';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { api } from 'main/services';
import { DisclaimerError } from '../DisclaimerError';

afterEach(cleanup);
vi.mock('/src/assets/icons/rounded-warning.svg?react', () => ({
  default: 'SvgMock',
}));

describe('<DashboardUploadPrestamista />', () => {
  vi.spyOn(api, 'post').mockReturnValue(
    Promise.resolve({
      data: {
        dados: {
          sucesso: true,
          entidade: null,
        },
      },
    }),
  );
  const queryClient = new QueryClient();

  it('deve exibir o componente de erro', () => {
    render(
      <ThemeProvider>
        <QueryClientProvider client={queryClient}>
          <DisclaimerError show />
        </QueryClientProvider>
      </ThemeProvider>,
    );

    const mensagemErro = screen.getByTestId('mensagemErro');
    expect(mensagemErro).toBeInTheDocument();
  });
});
