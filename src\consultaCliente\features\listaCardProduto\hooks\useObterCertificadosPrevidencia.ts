import { useQuery } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { obterProdutosPrevidencia } from '../service/consultarProdutosApi';

const useObterCertificadosPrevidencia = (codCliente?: string) => {
  return useQuery({
    queryKey: ['produtos-previdencia', codCliente],
    queryFn: () => obterProdutosPrevidencia(codCliente),
    staleTime: reactQueryCacheDuration(),
    retry: false,
  });
};

export default useObterCertificadosPrevidencia;
