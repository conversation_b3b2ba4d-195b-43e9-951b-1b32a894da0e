import styled from 'styled-components';
import { ContentEditable as LexicalContentEditable } from '@lexical/react/LexicalContentEditable';
import { getTernaryResult } from 'main/utils/conditional';

export const EditorWrapper = styled.div`
  border: 1px solid #bfbfbf;
  border-radius: 8px;
  font-family: sans-serif;
  overflow: hidden;
  height: 150px;
`;

export const Toolbar = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
`;

export const ContentEditable = styled(LexicalContentEditable)`
  min-height: 70px;
  padding: 10px;
  font-family: sans-serif;
  outline: none;
  border: none;
  background-color: #fff;
  color: #333;
  text-align: left;

  .underline {
    text-decoration: underline;
  }

  & > div {
    text-align: inherit;
  }
`;

export const Button = styled.button<{ active?: boolean }>`
  background: ${({ active }) =>
    getTernaryResult(!!active, 'rgba(223, 232, 250, 0.3)', 'none')};
  border: none;
  cursor: pointer;
  font-size: 15px;
  font-weight: ${({ active }) => getTernaryResult(!!active, 'bold', 'normal')};
  opacity: ${({ active }) => getTernaryResult(!!active, 1, 0.6)};
  &:hover {
    opacity: 1;
  }
`;
