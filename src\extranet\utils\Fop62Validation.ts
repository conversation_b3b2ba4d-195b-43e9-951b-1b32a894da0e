import * as TextsFop from 'extranet/features/fops/constants/constantsFop62';
import { checkIfAllItemsAreTrue } from 'main/utils/conditional';
import * as Yup from 'yup';
import { AnyObject } from 'yup/lib/types';

const validadorSelect = (
  valor: string | undefined,
  contextoYup: Yup.TestContext<AnyObject>,
  nomeObjeto: string,
) => {
  if (valor === TextsFop.SELECIONE) {
    contextoYup.createError({
      message: TextsFop.MENSAGEM_CAMPO_OBRIGATORIO,
      path: nomeObjeto,
    });
    return false;
  }
  return true;
};

const FormFopsValidationSchema = Yup.object({
  nomeEmpresa: Yup.string().required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO),
  atividadePrincipal: Yup.string().required(
    TextsFop.MENSAGEM_CAMPO_OBRIGATORIO,
  ),
  cnpj: Yup.string().required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO),
  nomeCompleto: Yup.string().required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO),
  superintendenciaRegional: Yup.string().required(
    TextsFop.MENSAGEM_CAMPO_OBRIGATORIO,
  ),
  nomeAgencia: Yup.string().required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO),
  telefone: Yup.string().required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO),
  matriculaIndicador: Yup.string().required(
    TextsFop.MENSAGEM_CAMPO_OBRIGATORIO,
  ),
  nomeIndicador: Yup.string().required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO),
  filial: Yup.string()
    .required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO)
    .test('filial', TextsFop.MENSAGEM_CAMPO_OBRIGATORIO, (value, context) =>
      validadorSelect(value, context, 'filial'),
    ),
  regraParaCalculo: Yup.string()
    .required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'regraParaCalculo',
      TextsFop.MENSAGEM_CAMPO_OBRIGATORIO,
      (value, context) => validadorSelect(value, context, 'regraParaCalculo'),
    ),
  formaPagamento: Yup.string()
    .required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'formaPagamento',
      TextsFop.MENSAGEM_CAMPO_OBRIGATORIO,
      (value, context) => validadorSelect(value, context, 'formaPagamento'),
    ),
  tipoCuidadoExtra: Yup.string()
    .required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'tipoCuidadoExtra',
      TextsFop.MENSAGEM_CAMPO_OBRIGATORIO,
      (value, context) => validadorSelect(value, context, 'tipoCuidadoExtra'),
    ),
  email: Yup.string()
    .required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO)
    .email(TextsFop.MENSAGEM_EMAIL_INVALIDO)
    .test('email', TextsFop.MENSAGEM_DOMINIOS_CAIXA, function (value) {
      if (
        checkIfAllItemsAreTrue([
          !value?.toLowerCase().match(TextsFop.DOMINIO_CVP),
          !value?.toLowerCase().match(TextsFop.DOMINIO_CAIXA),
        ])
      ) {
        this.createError({
          message: TextsFop.MENSAGEM_DOMINIOS_CAIXA,
          path: 'email',
        });
        return false;
      }
      return true;
    }),
  numeroParticipantes: Yup.number()
    .required(TextsFop.MENSAGEM_CAMPO_OBRIGATORIO)
    .transform(valorOriginal => {
      if (valorOriginal === '') return '';
      return Number(valorOriginal);
    })
    .min(5, TextsFop.MENSAGEM_NUMERO_PARTICIPANTE_MINIMO)
    .max(10000, TextsFop.MENSAGEM_NUMERO_PARTICIPANTES_LIMITE),
});

export default FormFopsValidationSchema;
