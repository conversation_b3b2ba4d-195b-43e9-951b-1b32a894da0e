import { IUser } from 'main/features/Auth/interfaces';
import { IApiResponsePaginadoBFF } from 'main/services';
import { getTernaryResult } from 'main/utils/conditional';
import { CAPTURA_TEXTO_ENTRE_ECONOMIARIO_E_PONTO_PARA } from 'main/utils/regex';
import { EnumTipo, EnumTipoLabel } from 'painelAdministracao/constants/enum';
import { IRelatorioSolicitacaoFop } from 'painelAdministracao/types/IRelatorioSolicitacaoFop';

export const definirLabelTipo = (tipo?: string) => {
  switch (tipo) {
    case EnumTipo.VIDA:
      return EnumTipoLabel.VIDA;
    case EnumTipo.PREVIDENCIA:
      return EnumTipoLabel.PREVIDENCIA;
    case EnumTipo.PREST:
      return EnumTipoLabel.PREST;
    case EnumTipo.PREV_SIMULADOR:
      return EnumTipoLabel.PREV_SIMULADOR;
    case EnumTipo.PREV_ADESAO:
      return EnumTipoLabel.PREV_ADESAO;
    case EnumTipo.PREV_MANUTENCAO:
      return EnumTipoLabel.PREV_MANUTENCAO;
    case EnumTipo.CORP:
      return EnumTipoLabel.CORP;
    case EnumTipo.PREV_PJ:
      return EnumTipoLabel.PREV_PJ;
    case EnumTipo.PREV_SAIDA:
      return EnumTipoLabel.PREV_SAIDA;
    case EnumTipo.PREV_OUTROS:
      return EnumTipoLabel.PREV_OUTROS;
    case EnumTipo.PREST_VIDA:
      return EnumTipoLabel.PREST_VIDA;
    default:
      return tipo;
  }
};

export const validarDataVersao = (dataVersao?: string) => {
  const novaData = new Date();
  if (dataVersao) {
    const data = new Date(dataVersao);
    if (!Number.isNaN(data.getTime())) {
      return data;
    }
    return novaData;
  }
  return novaData;
};

export const extrairTextoEditavel = (texto: string): string => {
  const regex = CAPTURA_TEXTO_ENTRE_ECONOMIARIO_E_PONTO_PARA;
  const match = texto.match(regex);
  return String(
    getTernaryResult(!!match && !!match[1], match?.[1]?.trim(), ''),
  );
};

export const convertToCSV = (
  data: IApiResponsePaginadoBFF<IRelatorioSolicitacaoFop[]> | undefined,
  user: IUser,
  sessionId: string,
  fileName: string,
) => {
  if (!data?.entidade || !data.entidade?.length) return '';

  const metadata = [
    `# Matricula: ${user.nomeAcesso}`,
    `# SessionId: ${sessionId}`,
    `# Description: ${fileName}`,
  ].join('\n');

  const header = Object.keys(data?.entidade?.[0]).join(';');
  const rows = data.entidade?.map(row => Object.values(row).join(';'));
  return [metadata, header, ...rows].join('\n');
};

export const downloadCSV = (csv: string, filename: string): void => {
  const BOM = '\uFEFF';
  const blob = new Blob([BOM + csv], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
