import RenderConditional from 'main/components/RenderConditional';
import { checkIfAllItemsAreTrue } from 'main/utils/conditional';
import * as CONSTS from 'painelAdministracao/constants/constants';
import { AdmContext } from 'painelAdministracao/context/AdministracaoContext';
import useEditarFop from 'painelAdministracao/hooks/useEditarFop';
import { useHandleAtualizarFop } from 'painelAdministracao/hooks/useHandleAtualizarFop';
import { useContext, useState } from 'react';
import { ModalEditarConfirmacao } from './ModalEditarConfirmacao';
import { useHandleAtualizarFopMotivo } from 'painelAdministracao/hooks/useHandleAtualizarFopMotivo';
import useEditarMotivoFop from 'painelAdministracao/hooks/useEditarMotivoFop';
import { ModalEditarFormulario } from './ModalEditarFormulario';
import { ModalEditarMotivo } from 'painelAdministracao/components/ModalEditarMotivo/ModalEditarMotivo';

const ModalEditar = () => {
  const [mostrarConfirmacao, setMostrarConfirmacao] = useState(false);
  const { formik, handleChangeDataVersao, handleChangeAtivo } = useEditarFop();
  const { formikMotivo, handleChangeMotivoAtivo, handleDescricao } =
    useEditarMotivoFop();
  const {
    modalEditar,
    handleFecharModal,
    fopEditar,
    documentoEditar,
    modalEditarMotivo,
  } = useContext(AdmContext);

  const { loading, atualizarFop } = useHandleAtualizarFop(
    formik,
    fopEditar,
    documentoEditar,
    handleFecharModal,
  );

  const onClose = () => {
    handleFecharModal();
    formik.setValues(CONSTS.INITIAL_FORMIK_STATE);
  };

  const onCloseMotivo = (): void => {
    handleFecharModal();
    formikMotivo.setValues(CONSTS.INITIAL_FORMIK_MOTIVO_STATE);
  };

  const handleAtualizar = () => {
    if (!formik.values.ativo) {
      setMostrarConfirmacao(true);
    } else {
      atualizarFop();
    }
  };

  const { loadingMotivoFop, atualizarFopMotivo } = useHandleAtualizarFopMotivo(
    formikMotivo,
    fopEditar,
    handleFecharModal,
  );

  return (
    <>
      <RenderConditional
        condition={checkIfAllItemsAreTrue([modalEditar, !mostrarConfirmacao])}
      >
        <ModalEditarFormulario
          onClose={onClose}
          fopEditar={fopEditar}
          formik={formik}
          handleChangeDataVersao={handleChangeDataVersao}
          handleChangeAtivo={handleChangeAtivo}
          handleAtualizar={handleAtualizar}
          loading={loading}
        />
      </RenderConditional>
      <RenderConditional
        condition={checkIfAllItemsAreTrue([modalEditar, mostrarConfirmacao])}
      >
        <ModalEditarConfirmacao
          fopEditar={fopEditar}
          handleConfirm={() => {
            atualizarFop();
            setMostrarConfirmacao(false);
          }}
          handleClose={() => setMostrarConfirmacao(false)}
        />
      </RenderConditional>
      <RenderConditional condition={modalEditarMotivo}>
        <ModalEditarMotivo
          onClose={onCloseMotivo}
          fopEditar={fopEditar}
          formik={formikMotivo}
          loading={loadingMotivoFop}
          handleChangeMotivoAtivo={handleChangeMotivoAtivo}
          handleDescricao={handleDescricao}
          handleAtualizarMotivo={atualizarFopMotivo}
        />
      </RenderConditional>
    </>
  );
};

export default ModalEditar;
