/* eslint-disable @typescript-eslint/no-explicit-any */

import { Radio } from '@cvp/design-system/react';

import {
  checkIfAllItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import { isNegativeCurrent } from 'main/utils/string';
import { Beneficiario } from 'previdencia/features/AlteracaoBeneficiarios/types/Beneficiarios';
import * as ALTERACAO_CONTA_TYPES from 'previdencia/features/AlteracaoFormaDadosPagamento/types/AlteracaoFormaDadosPagamento';
import * as ALTERACAO_CONTA_UTILS from 'previdencia/features/AlteracaoFormaDadosPagamento/utils/alteracaoFormaDadosPagamento';
import { IResponseConsultarBeneficiariosData } from 'previdencia/features/InformesBeneficiarios/types/InformesBeneficiariosTypes';
import { AnoHistorico } from 'previdencia/features/ReajusteAnualPlano/types/ResponseAnosHistorico';
import { ExtratoRentabilidade } from 'previdencia/features/RentabilidadeCotas/types/ResponseExtratoRentabilidadeCotas';
import { TDadosCertificado } from 'previdencia/types/DadosCertificado';
import { TFundoColunasTabela } from 'previdencia/types/Fundo.type';
import {
  CellTypeRentabilidadeCotas,
  TabelaFundosType,
} from 'previdencia/types/Tabelas';
import {
  obterCodigoOperacaoENumeroConta,
  ordenaDataRealizado,
  ordenaValor,
} from 'previdencia/utils/previdencia';
import { TableColumn } from 'react-data-table-component';
import { IReservaTabela } from '../features/AlteracaoBeneficiarios/types/TabelaReserva';
import { IHistoricoSolicitacoesColunas } from 'previdencia/features/HistoricoSolicitacoes/components/TabelaMovimentacoes/types/TabelaMovimentacoes';

export const COLUNAS_TABELA_FUNDOS: TableColumn<TFundoColunasTabela>[] = [
  {
    wrap: true,
    selector: row => row.check as boolean,
    width: '50px',
    name: '',
  },
  {
    selector: row => row.descFundo as string,
    name: 'Fundo',
    minWidth: '250px',
    cell: ({ descFundo }: TabelaFundosType) => (
      <p title={descFundo}>{descFundo}</p>
    ),
  },
  {
    name: 'Perfil do Risco',
    minWidth: '180px',
    cell: row => row.perfil,
  },
  {
    name: 'Rentabilidade (últimos 12 meses)',
    minWidth: '140px',
    selector: row => row.rentabilidade as number,
  },
  {
    minWidth: '140px',
    name: 'Saldo',
    selector: row => row.saldo,
  },
];

export const COLUNAS_TABELA_EXTRATO_RENTABILIDADE_COTAS: TableColumn<ExtratoRentabilidade>[] =
  [
    {
      name: 'Data Extrato',
      minWidth: '135px',
      selector: row => row.dataExtrato,
    },
    {
      name: 'Data Movimentação',
      minWidth: '130px',
      selector: row => row.dataMotivo,
    },
    {
      name: 'Tipo Movimentação',
      minWidth: '200px',
      selector: row => row.nomeMotivo,
      cell: ({ nomeMotivo }: CellTypeRentabilidadeCotas) => (
        <p title={nomeMotivo}>{nomeMotivo}</p>
      ),
    },
    {
      name: 'Valor Movimentação',
      selector: row => row.valorMotivo,
      minWidth: '160px',
      conditionalCellStyles: [
        {
          when: (row: any) => isNegativeCurrent(row.valorMotivo),
          style: {
            color: 'red',
          },
        },
      ],
    },
    {
      name: 'Data Cota Movimentação',
      selector: row => row.dataCota,
      minWidth: '130px',
      style: {
        backgroundColor: 'rgba(239, 239, 239, 1)',
      },
    },
    {
      name: 'Valor Cota no Dia *',
      selector: row => row.valorCota,
      minWidth: '180px',
      style: {
        backgroundColor: 'rgba(239, 239, 239, 1)',
      },
    },
    {
      name: 'Quantidade Total de Cotas',
      selector: row => row.valorTotalCota,
      minWidth: '180px',
      style: {
        backgroundColor: 'rgba(239, 239, 239, 1)',
      },
      conditionalCellStyles: [
        {
          when: (row: any) => isNegativeCurrent(row.valorTotalCota),
          style: {
            color: 'red',
          },
        },
      ],
    },
    {
      name: 'Reserva Total',
      minWidth: '140px',
      selector: row => row.valorSaldo,
      conditionalCellStyles: [
        {
          when: (row: any) => isNegativeCurrent(row.Reserva),
          style: {
            color: 'red',
          },
        },
      ],
    },
    {
      name: 'Rentabilidade',
      minWidth: '180px',
      selector: row => row.valorRentabilidade,
      conditionalCellStyles: [
        {
          when: (row: any) => isNegativeCurrent(row.valorRentabilidade),
          style: {
            color: 'red',
          },
        },
      ],
    },
    {
      name: 'Cód fundo **',
      selector: row => row.codigoFundo,
      minWidth: '90px',
      style: {
        backgroundColor: 'rgba(239, 239, 239, 1)',
      },
    },
  ];

export const COLUNAS_TABELA_RESERVA: TableColumn<IReservaTabela>[] = [
  {
    id: '1',
    name: 'Beneficiário',
    cell: row => row.beneficiario,
    minWidth: '400px',
  },
  {
    id: '2',
    name: 'Parentesco',
    cell: row => row.parentesco,
  },
  {
    id: '3',
    name: 'Data Nascimento',
    cell: row => row.dataNascimento,
  },
  {
    id: '4',
    name: 'Percentual',
    cell: row => row.percentual,
  },
  {
    id: '5',
    name: 'Remover',
    cell: row => row.remover,
  },
];

export const COLUNAS_TABELA_RESERVA_IMPRIMIR: TableColumn<IReservaTabela>[] = [
  {
    id: '1',
    name: 'Beneficiário',
    cell: row => row.beneficiario,
    minWidth: '260px',
  },
  {
    id: '2',
    name: 'Parentesco',
    cell: row => row.parentesco,
    maxWidth: '40px',
  },
  {
    id: '3',
    name: 'Dt Nascimento',
    cell: row => row.dataNascimento,
    maxWidth: '130px',
  },
  {
    id: '4',
    name: 'Percentual',
    cell: row => row.percentual,
  },
];

export const COLUNAS_TABELA_ALTERACAO_BENEFICIARIOS: TableColumn<Beneficiario>[] =
  [
    {
      id: '1',
      name: 'Beneficiário',
      selector: row => row.beneficiario,
      minWidth: '500px',
    },
    {
      id: '2',
      name: 'Data Nascimento',
      selector: row => row.dataNascimento,
    },
    {
      id: '3',
      name: 'Grau Parentesco',
      selector: row => row.grauParentesco,
    },
    {
      id: 4,
      name: 'Coberturas',
      cell: row => row.coberturas,
    },
  ];

export const COLUNAS_TABELA_ALTERACAO_BENEFICIARIOS_IMPRIMIR: TableColumn<Beneficiario>[] =
  [
    {
      id: '1',
      name: 'Beneficiário',
      selector: row => row.beneficiario,
      minWidth: '280px',
    },
    {
      id: '2',
      name: 'Dt Nascimento',
      selector: row => row.dataNascimento,
      maxWidth: '130px',
    },
    {
      id: '3',
      name: 'Parentesco',
      selector: row => row.grauParentesco,
      maxWidth: '40px',
    },
    {
      id: '4',
      name: 'Coberturas',
      cell: row => row.coberturas,
      maxWidth: '75px',
    },
  ];

export const COLUNAS_TABELA_TIPO_RESERVA: TableColumn<AnoHistorico>[] = [
  {
    name: 'Tipo de atualização',
    selector: row => row.desTipoAtualizacao,
    minWidth: '320px',
  },
  {
    name: 'Data',
    selector: row => row.dthRecal,
  },
  {
    name: 'Valor anterior',
    selector: row => row.vlrAnterior,
  },
  {
    name: 'Atualização*',
    selector: row => row.pctAtualizacaoCodIndice as string,
  },
  {
    name: 'Valor final',
    selector: row => row.vlrFinal,
  },
];

export const COLUNAS_TABELA_TIPO_CUIDADO_EXTRA: TableColumn<AnoHistorico>[] = [
  {
    name: 'Tipo de atualização',
    selector: row => row.desTipoAtualizacao,
    minWidth: '360px',
  },
  {
    name: 'Data',
    selector: row => row.dthRecal,
  },
  {
    name: 'Cuidado Extra',
    minWidth: '250px',
    selector: row => row.desPagamento,
  },
  {
    name: 'Valor anterior',
    selector: row => row.vlrFinal,
  },
  {
    name: 'Atualização*',
    selector: row => row.pctAtualizacaoCodIndice as string,
  },
  {
    name: 'Valor final',
    selector: row => row.vlrFinal,
  },
];

export const COLUNAS_HISTORICO_SOLICITACOES: TableColumn<IHistoricoSolicitacoesColunas>[] =
  [
    {
      name: 'Serviço',
      selector: row => row.servico,
      wrap: true,
      sortable: true,
      minWidth: '180px',
    },
    {
      name: 'Realizado',
      selector: row => row.realizado,
      sortable: true,
      minWidth: '140px',
      sortFunction: ordenaDataRealizado,
    },
    {
      name: 'Canal',
      selector: row => row.canal,
      sortable: true,
      wrap: true,
      minWidth: '150px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      wrap: true,
      sortable: true,
      minWidth: '150px',
    },
    {
      name: 'Valor',
      selector: row => row.valor,
      minWidth: '150px',
      sortable: true,
      sortFunction: ordenaValor,
    },
    {
      name: 'Comprovante',
      selector: row => row.comprovante,
      cell: row => row.comprovanteElement,
      wrap: true,
      minWidth: '180px',
    },
  ];

export const COLUNAS_INFORMES_BENEFICIARIOS: TableColumn<IResponseConsultarBeneficiariosData>[] =
  [
    {
      name: 'Nome do Beneficiário',
      selector: row => row.nomeBeneficiario,
      minWidth: '500px',
    },
    {
      name: 'CPF',
      selector: row => row.cpf,
      minWidth: '100px',
    },
    {
      name: 'Informe de Rendimentos',
      cell: row => row.informe,
      minWidth: '350px',
    },
  ];

export const COLUNAS_TABELA_CONTAS_BANCARIAS = ({
  canal,
  obterContaCadastradaSelecionada,
}: ALTERACAO_CONTA_TYPES.IColunasTabelaContasBancariasParams): TableColumn<ALTERACAO_CONTA_TYPES.ListaTiposContasBancarias>[] => [
  {
    name: '',
    wrap: true,
    width: '50px',
    cell: (row: ALTERACAO_CONTA_TYPES.ListaTiposContasBancarias) => (
      <Radio
        data-testid="btnEmail"
        onChange={() => {
          obterContaCadastradaSelecionada(row);
        }}
        checked={checkIfAllItemsAreTrue([
          !canal.isDisabled,
          canal.canal === row.idCanalRecuperado,
        ])}
        value="EMAIL"
        name="selectedMethod"
        disabled={canal.isDisabled}
      />
    ),
  },
  {
    name: 'Banco',
    selector: row => row.numeroBanco,
    minWidth: '140px',
  },
  {
    name: 'Agência',
    selector: row => row.numeroAgencia,
    minWidth: '140px',
  },
  {
    name: 'Operação',
    selector: row => row.operacao as string,
    minWidth: '140px',
    cell: (row: ALTERACAO_CONTA_TYPES.ListaTiposContasBancarias) =>
      obterCodigoOperacaoENumeroConta(row.numeroConta).operacao,
  },
  {
    name: 'Conta',
    selector: row => row.conta as string,
    minWidth: '140px',
    cell: (row: ALTERACAO_CONTA_TYPES.ListaTiposContasBancarias) =>
      `${obterCodigoOperacaoENumeroConta(row.numeroConta).numeroConta}-${
        row.digitoConta
      }`,
  },
];

export const COLUNAS_TABELA_DADOS_BANCARIOS_CONFIRMACAO = (
  certificado?: string,
): TableColumn<
  Partial<ALTERACAO_CONTA_TYPES.TTipoContasBancariasDataColumn>
>[] => [
  {
    name: 'CERTIFICADO',
    selector: row => row.certificado as string,
    wrap: true,
    minWidth: '140px',
    cell: () => certificado,
  },
  {
    name: 'FORMA DE PAGAMENTO',
    minWidth: '140px',
    cell: row =>
      ALTERACAO_CONTA_UTILS.buscarMetodoPagamento(
        row.metodoPagamento as string,
      ),
  },
  {
    name: 'BANCO',
    selector: row => row.banco as string,
    minWidth: '140px',
    cell: row => row.numeroBanco,
  },
  {
    name: 'AGÊNCIA',
    selector: row => row.agencia as string,
    minWidth: '140px',
    cell: row => row.numeroAgencia,
  },
  {
    name: 'CONTA',
    selector: row => row.conta as string,
    minWidth: '140px',
    cell: row =>
      `${
        obterCodigoOperacaoENumeroConta(
          tryGetValueOrDefault([row.numeroConta], ''),
        ).numeroConta
      }-${row.digitoConta}`,
  },
];

export const COLUNAS_TABELA_DADOS_ANTIGO_BANCARIOS_CONFIRMACAO = (
  certificado?: string,
): TableColumn<TDadosCertificado>[] => [
  {
    name: 'CERTIFICADO',
    selector: row => row.certificadoAntigo as string,
    wrap: true,
    minWidth: '140px',
    cell: () => certificado,
  },
  {
    name: 'FORMA DE PAGAMENTO',
    selector: row => row.formaPagamentoAntigo as string,
    minWidth: '140px',
  },
  {
    name: 'BANCO',
    selector: row => row.bancoAntigo as string,
    minWidth: '140px',
  },
  {
    name: 'AGÊNCIA',
    selector: row => row.agenciaAntiga as string,
    minWidth: '140px',
  },
  {
    name: 'CONTA',
    selector: row => row.contaAntiga as string,
    minWidth: '140px',
  },
];

export const prevQueryCacheDuration = (duration = 60): number =>
  Number(import.meta.env.VITE_CACHE_DURATION) * duration;

export const COD_EMPRESA = '500';

export const ASSINATURA_BENEFICIO_PROTECAO = {
  CODIGO_EMPRESA: COD_EMPRESA,
  TIPO_ASSINATURA: 'OFERTA_RISCO',
  TIPO_ENVIO: 'SMS',
};

export enum TabelaFundosDefaultValue {
  PERFIL_RISCO_MODERADO = 'Moderado',
}
export const ALIQUOTA = {
  TIPO_REGIME_REGRESSIVO: 'R',
  TIPO_REGIME_PROGRESSIVO: 'P',
  TIPO_RESGATE_TOTAL: 'T',
  TIPO_RESGATE_PARCIAL: 'P',
  TRIBUTACAO_REGRESSIVA: 'TR',
  TRIBUTACAO_PROGRESSIVA: 'TP',
  PROGRESSIVO: 'Alíquota Progressiva',
  REGRESSIVO: 'Alíquota Regressiva',
  PROGRESSIVO_SHORT: 'Progressiva',
  REGRESSIVO_SHORT: 'Regressiva',
  TEXTO_CONFIRMAR: 'Confirmar',
  TEXTO_CANCELAR: 'Cancelar',
  TEXTO_PROGRESSIVO: 'Progressivo',
  TEXTO_REGRESSIVO: 'Regressivo',
  TEXTO_FORMA_PAGAMENTO: 'Conta',
  TEXTO_CONFIRMAR_MUDANCA_ALIQUOTA:
    'A alíquota escolhida não poderá mais ser alterada, sendo assim, definitiva. Tem certeza da escolha do modelo tributário?',
};
export const TIPO_ASSINATURA = {
  ENTRE_CERTIFICADOS: 'TRANSCERT',
  TRANSFERENCIA_ADESAO: 'T_ADS',
};

export const TIPO_ENVIO_ASSINATURA = {
  SMS: 'SMS',
  EMAIL: 'EMAIL',
};

export const FLAGS_FOPS = {
  FOP223: '_FOP223',
  TRANSFUND_PJ: '_TRANSFUND_PJ',
};
