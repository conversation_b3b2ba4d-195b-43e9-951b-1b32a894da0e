import { usePeco } from 'main/hooks/usePeco';
import {
  TPayloadRegistrarSolicitacaoFop,
  TResponseRegistrarSolicitacaoFop,
} from 'extranet/types/RegistrarSolicitacao';
import { ENDPOINT_FOP } from 'extranet/config/endpoints';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';

export const usePecoRegistrarSolicitacaoFOP =
  (): TResponseRegistrarSolicitacaoFop => {
    const { response, loading, error, fetchData } = usePeco({
      api: { operationPath: ENDPOINT_FOP.REGISTRAR_SOLICITACAO },
      autoFetch: false,
    });

    const salvarMotivo = async (
      payload: TPayloadRegistrarSolicitacaoFop,
    ): Promise<IHandleReponseResult<unknown> | undefined> => {
      return fetchData(payload);
    };

    return {
      response,
      loading,
      error,
      salvarMotivo,
    };
  };
