import { render, screen, waitFor } from '@testing-library/react';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { api } from 'main/services';
import { act } from 'react-dom/test-utils';
import userEvent from '@testing-library/user-event';
import PropostasPendentes from '../PropostasPendentes';

describe('Prestamista - ListagemPropostas.spec', () => {
  const queryClient = new QueryClient();
  it('renderiza a tela e verifica se os dados foram apresentados', () => {
    vi.spyOn(api, 'post').mockReturnValue(
      Promise.resolve({
        data: {
          dados: {
            sucesso: false,
            entidade: [
              {
                numeroProposta: '80630760003552',
                dataHoraEmissaoDaProposta: '2020-08-31T00:00:00',
                numeroLinhaDoProduto: '2',
                codigoAgenciaVenda: 630,
                cpfCnpj: '03187279000152',
                codigoDoEstipulante: '2',
              },
              {
                numeroProposta: '80630770028311',
                dataHoraEmissaoDaProposta: '2020-08-14T00:00:00',
                numeroLinhaDoProduto: '2',
                codigoAgenciaVenda: 630,
                cpfCnpj: '42720028134',
                codigoDoEstipulante: '2',
              },
            ],
          },
        },
      }),
    );

    render(
      <RenderPageWithThemeProviderAndRouter>
        <QueryClientProvider client={queryClient}>
          <PropostasPendentes />
        </QueryClientProvider>
      </RenderPageWithThemeProviderAndRouter>,
    );

    const tabelaPropostas = screen.getByTestId(/tabelaListaPropostas/i);
    expect(tabelaPropostas).toBeInTheDocument();
  });

  it('deve clicar no botão de upload e mostrar a modal', () => {
    render(
      <RenderPageWithThemeProviderAndRouter>
        <QueryClientProvider client={queryClient}>
          <PropostasPendentes />
        </QueryClientProvider>
      </RenderPageWithThemeProviderAndRouter>,
    );

    const tabelaPropostas = screen.getByTestId(/tabelaListaPropostas/i);
    expect(tabelaPropostas).toBeInTheDocument();

    waitFor(
      () => {
        const [uploadButton] = screen.getAllByTestId(/^uploadButton.*$/);

        act(() => {
          userEvent.click(uploadButton);
        });

        const modalUploadArquivo = screen.getByTestId(/modalUploadArquivo/i);
        expect(modalUploadArquivo).toBeInTheDocument();
      },
      { timeout: 1000 },
    );
  });
});
