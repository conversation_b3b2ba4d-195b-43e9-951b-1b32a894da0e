import { IFilterableEntity } from 'main/types/IFilterableEntity';

export interface IResponseListaPropostaQuery {
  isLoading: boolean;
  data?: IResponseListaProposta[] | unknown;
}
export interface IResponseListaProposta extends IFilterableEntity {
  numeroProposta: string;
  dataHoraEmissaoDaProposta: string;
  numeroLinhaDoProduto: string;
  codigoAgenciaVenda: number;
  cpfCnpj: string;
  codigoDoEstipulante: string;
  valorPremioLiquido?: number | null;
}

export interface IPropostaDataColumn
  extends Omit<IResponseListaProposta, 'valorPremioLiquido'> {
  valorPremioLiquido?: number | string;
}
