import * as DS from '@cvp/design-system/react';
import { maskCpfCnpj } from 'contratosPrestamista/utils/maskCpfCnpj';
import * as Constants from 'extranet/features/fops/constants/constantsFop62';
import { initialValues } from 'extranet/features/fops/constants/constantsFop62';
import { ISelectOption } from 'extranet/features/fops/pages/ISelectOption';
import useSolicitarAberturaFop62 from 'extranet/hooks/useEnviarSolicitacaoAberturaFop62';
import { FormatarNumerico, TextoCaracteres } from 'extranet/hooks/useFormFops';
import * as Enum from 'extranet/types/enum';
import * as enumFop from 'extranet/types/enumFop62';
import FormFopsValidationSchema from 'extranet/utils/Fop62Validation';
import { Field, Formik } from 'formik';
import InputFile, {
  DescriptionFormatsFilesAllowed,
} from 'main/components/form/InputFile';
import Icon from 'main/components/Icon';
import RenderConditional from 'main/components/RenderConditional/RenderConditional';
import { DownloadButton } from 'main/features/prospeccao/components/styles';
import { TextLabel, TitleSection } from 'main/styles/GlobalStyle';
import masks from 'main/utils/masks';
import React from 'react';
import { Label } from 'vida/features/AdicionarBeneficiario/styles';
import * as S from './styles';
import { useToast } from 'main/hooks/useToast';

const Fop062: React.FC = () => {
  const {
    user,
    handleFormSubmit,
    dataLocation,
    formFop,
    loading,
    validarArquivoAnexado
  } = useSolicitarAberturaFop62();
  const { toastError } = useToast();

  return (
    <DS.Display type="display-block" key="form-fop062">
      <Formik
        key="form-fop-prev-empresarial"
        initialValues={{
          ...initialValues,
          matriculaIndicador: user.nomeAcesso,
          nomeIndicador: user.nomeUsuario,
          numeroAgencia: String(user.agenciaVinculada),
        }}
        validationSchema={FormFopsValidationSchema}
        onSubmit={values => handleFormSubmit(values)}
      >
        {({
          handleSubmit,
          handleBlur,
          values,
          errors,
          touched,
          setFieldValue,
          resetForm,
        }) => (
          <form onSubmit={handleSubmit} style={{ height: '100%' }}>
            <DS.Card key="form-header">
              <DS.Card.Content padding={[4, 4, 4]}>
                <DS.Text
                  variant="body-medium3"
                  color="text-light"
                  key="form-atencao"
                >
                  {Constants.TEXTO_ATENCAO}
                </DS.Text>
                <br />
                <DS.Grid>
                  <DS.Grid.Item xs={1}>
                    <DS.Text
                      variant="headline-05"
                      color="primary"
                      key="formulario-titulo"
                    >
                      {Constants.TITULO_PAGINA}
                    </DS.Text>
                  </DS.Grid.Item>
                </DS.Grid>
              </DS.Card.Content>
            </DS.Card>
            <br />
            <DS.Card key="form-informacoes-gerais">
              <DS.Card.Content padding={[4, 4, 4]}>
                <DS.Accordion open>
                  <S.AccordionItem
                    title={Constants.TITULO_INFORMACOES_GERAIS}
                    style
                  >
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="nomeEmpresa"
                        label={enumFop.InformacoesGerais.nomeEmpresa}
                        id="nomeEmpresa"
                        component={DS.TextField}
                        maxLength={50}
                        value={values.nomeEmpresa}
                        error={errors.nomeEmpresa && touched.nomeEmpresa}
                        errorMessage={errors.nomeEmpresa}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue('nomeEmpresa', value);
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="atividadePrincipal"
                        label={enumFop.InformacoesGerais.atividadePrincipal}
                        component={DS.TextField}
                        value={values.atividadePrincipal}
                        maxLength={50}
                        error={errors.atividadePrincipal}
                        errorMessage={errors.atividadePrincipal}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue('atividadePrincipal', value);
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="cnpj"
                        label={enumFop.InformacoesGerais.CNPJ}
                        component={DS.TextField}
                        type="text"
                        inputMode="numeric"
                        pattern="[0-9]*"
                        maxLength={17}
                        value={maskCpfCnpj(values.cnpj)}
                        error={errors.cnpj}
                        errorMessage={errors.cnpj}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue('cnpj', maskCpfCnpj(FormatarNumerico(value)));
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="numeroAgencia"
                        label={enumFop.InformacoesGerais.numeroAgencia}
                        component={DS.TextField}
                        value={user.agenciaVinculada}
                        disabled
                        maxLength={4}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1 / 2} lg={1 / 2}>
                      <Field
                        name="superintendenciaRegional"
                        label={
                          enumFop.InformacoesGerais.superintendenciaRegional
                        }
                        component={DS.TextField}
                        type="text"
                        inputMode="numeric"
                        pattern="[0-9]*"
                        maxLength={4}
                        value={values.superintendenciaRegional}
                        error={errors.superintendenciaRegional}
                        errorMessage={errors.superintendenciaRegional}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue(
                            'superintendenciaRegional',
                            FormatarNumerico(value),
                          );
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="nomeAgencia"
                        label={enumFop.InformacoesGerais.nomeAgencia}
                        component={DS.TextField}
                        maxLength={50}
                        value={values.nomeAgencia}
                        error={errors.nomeAgencia}
                        errorMessage={errors.nomeAgencia}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue('nomeAgencia', value);
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="filial"
                        label={enumFop.InformacoesGerais.filial}
                        component={DS.Select}
                        placeholder={Constants.SELECIONE}
                        value={values.filial}
                        error={errors.filial}
                        errorMessage={errors.filial}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<{
                          text: string;
                          value: string;
                        }>) => {
                          setFieldValue('filial', value);
                        }}
                        onBlur={handleBlur}
                      >
                        {Enum.SELECT_OPTIONS_FILIAL.map(optionsFilial => (
                          <DS.Select.Item
                            key={optionsFilial.key}
                            value={optionsFilial.key}
                            text={optionsFilial.value}
                            selected={optionsFilial.key === values.filial}
                          />
                        ))}
                      </Field>
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1 / 2} lg={1 / 2}>
                      <Field
                        name="matriculaIndicador"
                        label={enumFop.InformacoesGerais.matriculaIndicador}
                        component={DS.TextField}
                        maxLength={8}
                        value={values.matriculaIndicador}
                        error={errors.matriculaIndicador}
                        errorMessage={errors.matriculaIndicador}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue('matriculaIndicador', value);
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1 / 2}>
                      <Field
                        name="nomeIndicador"
                        label={enumFop.InformacoesGerais.nomeIndicador}
                        component={DS.TextField}
                        value={values.nomeIndicador}
                        maxLength={50}
                        error={errors.nomeIndicador}
                        errorMessage={errors.nomeIndicador}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue('nomeIndicador', value);
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                  </S.AccordionItem>
                </DS.Accordion>
              </DS.Card.Content>
            </DS.Card>
            <br />
            <DS.Card>
              <DS.Card.Content padding={[4, 4, 4]}>
                <DS.Accordion open>
                  <S.AccordionItem title={Constants.TITULO_DADOS_DO_PLANO}>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="numeroParticipantes"
                        label={Constants.NUMERO_PARTICIPANTES}
                        component={DS.TextField}
                        type="text"
                        inputMode="numeric"
                        pattern="[0-9]*"
                        maxLength={5}
                        value={values.numeroParticipantes}
                        error={errors.numeroParticipantes}
                        errorMessage={errors.numeroParticipantes}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue(
                            'numeroParticipantes',
                            FormatarNumerico(value),
                          );
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="regraParaCalculo"
                        label={Constants.SELECIONE_REGRA_CALCULO}
                        component={DS.Select}
                        placeholder={Constants.SELECIONE}
                        value={values.regraParaCalculo}
                        error={errors.regraParaCalculo}
                        errorMessage={errors.regraParaCalculo}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<{
                          text: string;
                          value: string;
                        }>) => {
                          setFieldValue('regraParaCalculo', value);
                          formFop.selectRegraCalculoFOP62(value);
                        }}
                        onBlur={handleBlur}
                      >
                        {Constants.selectOptionsRegra.map(OptionsRegra => (
                          <DS.Select.Item
                            key={OptionsRegra.key}
                            value={OptionsRegra.key}
                            text={OptionsRegra.value}
                            selected={
                              OptionsRegra.key === values.regraParaCalculo
                            }
                          />
                        ))}
                      </Field>
                    </DS.Grid.Item>
                    <RenderConditional
                      condition={formFop.openSelectTipoBenficio}
                    >
                      <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Field
                          name="tipoDeBeneficioBasico"
                          label={Constants.BENEFICIO_BASICO}
                          component={DS.Select}
                          placeholder={Constants.SELECIONE}
                          value={values.tipoDeBeneficioBasico}
                          error={errors.tipoDeBeneficioBasico}
                          errorMessage={errors.tipoDeBeneficioBasico}
                          onChange={({
                            target: { value },
                          }: React.ChangeEvent<{
                            text: string;
                            value: string;
                          }>) => {
                            setFieldValue('tipoDeBeneficioBasico', value);
                            formFop.selectBeneficio(value);
                          }}
                          onBlur={handleBlur}
                        >
                          {Enum.SELECT_OPTIONS_BENEFICIO.map(optionsBeneficio => (
                            <DS.Select.Item
                              key={optionsBeneficio.key}
                              value={optionsBeneficio.key}
                              text={optionsBeneficio.value}
                              selected={
                                optionsBeneficio.key ===
                                values.tipoDeBeneficioBasico
                              }
                            />
                          ))}
                        </Field>
                      </DS.Grid.Item>
                      <RenderConditional
                        condition={formFop.openSelectPrazoBenficio}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="prazoDeBeneficio"
                            label={Constants.PRAZO_BENEFICIO_BASICO}
                            component={DS.Select}
                            placeholder={Constants.SELECIONE}
                            value={values.prazoDeBeneficio}
                            error={errors.prazoDeBeneficio}
                            errorMessage={errors.prazoDeBeneficio}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<{
                              text: string;
                              value: string;
                            }>) => {
                              setFieldValue('prazoDeBeneficio', value);
                            }}
                            onBlur={handleBlur}
                          >
                            {Enum.SELECT_OPTIONS_PRAZO_BENEFICIO.map(
                              optionsPrazoBeneficio => (
                                <DS.Select.Item
                                  key={optionsPrazoBeneficio.key}
                                  value={optionsPrazoBeneficio.key}
                                  text={optionsPrazoBeneficio.value}
                                  selected={
                                    optionsPrazoBeneficio.key ===
                                    values.prazoDeBeneficio
                                  }
                                />
                              ),
                            )}
                          </Field>
                        </DS.Grid.Item>
                      </RenderConditional>
                      <RenderConditional
                        condition={formFop.openSelectReversivel}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="selectReversao"
                            label={Constants.PORCENTAGEM_REVERSAO}
                            component={DS.Select}
                            placeholder={Constants.SELECIONE}
                            value={values.selectReversao}
                            error={errors.selectReversao}
                            errorMessage={errors.selectReversao}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<{
                              text: string;
                              value: string;
                            }>) => {
                              setFieldValue('selectReversao', value);
                            }}
                            onBlur={handleBlur}
                          >
                            {Enum.SELECT_OPTIONS_REVERSAO.map(
                              optSelectReversao => (
                                <DS.Select.Item
                                  key={optSelectReversao.key}
                                  value={optSelectReversao.key}
                                  text={optSelectReversao.value}
                                  selected={
                                    optSelectReversao.key ===
                                    values.selectReversao
                                  }
                                />
                              ),
                            )}
                          </Field>
                        </DS.Grid.Item>
                      </RenderConditional>
                    </RenderConditional>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="formaPagamento"
                        label={Constants.TIPO_PLANO_EMPRESARIAL}
                        component={DS.Select}
                        placeholder={Constants.SELECIONE}
                        value={values.formaPagamento}
                        error={errors.formaPagamento}
                        errorMessage={errors.formaPagamento}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<{
                          text: string;
                          value: string;
                        }>) => {
                          setFieldValue('formaPagamento', value);
                          setFieldValue('valorContribuicao', '');
                          formFop.setTextOutraFormaPagamentoEmpresa('')
                          setFieldValue('valorContribuicaoEmpresa', '');
                          formFop.setTextOutraFormaPagamentoFuncionario(
                            '',
                          )
                          setFieldValue(
                            'valorContribuicaoFuncionario',
                            '',
                          );
                          formFop.selectFormaPagamento(value);
                        }}
                        onBlur={handleBlur}
                      >
                        {Constants.selectOptionsPagamento.map(
                          optRegraParaCalculo => (
                            <DS.Select.Item
                              key={optRegraParaCalculo.key}
                              value={optRegraParaCalculo.key}
                              text={optRegraParaCalculo.value}
                              selected={
                                optRegraParaCalculo.key ===
                                values.formaPagamento
                              }
                            />
                          ),
                        )}
                      </Field>
                    </DS.Grid.Item>
                    <RenderConditional
                      condition={formFop.openSelectValorContribuicao}
                    >
                      <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Field
                          name="valorContribuicao"
                          label={Constants.VALOR_CONTRIBUICAO}
                          component={DS.Select}
                          placeholder={Constants.SELECIONE}
                          value={values.valorContribuicao}
                          error={errors.valorContribuicao}
                          errorMessage={errors.valorContribuicao}
                          onChange={({
                            target: { value },
                          }: React.ChangeEvent<{
                            text: string;
                            value: string;
                          }>) => {
                            setFieldValue('valorContribuicao', value);
                            formFop.setTextOutraFormaPagamento('')
                            formFop.selectValorContribuicao(value);
                          }}
                          onBlur={handleBlur}
                        >
                          {Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO
                            .filter(
                              opt =>
                                opt.value !== 'Valor fixo' &&
                                opt.value !== 'Valor com base no percentual do salário'
                            )
                            .map(optValorContribuicao => (
                              <DS.Select.Item
                                key={optValorContribuicao.key}
                                value={optValorContribuicao.key}
                                text={optValorContribuicao.value}
                                selected={optValorContribuicao.key === values.valorContribuicao}
                              />
                            ))}
                        </Field>
                      </DS.Grid.Item>
                      <RenderConditional
                        condition={formFop.openSelectValorFixo}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="valorFixo"
                            label={Constants.VALOR_FIXO}
                            component={DS.TextField}
                            value={values.valorFixo}
                            error={errors.valorFixo}
                            maxLength={16}
                            errorMessage={errors.valorFixo}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<HTMLInputElement>) => {
                              setFieldValue('valorFixo',
                                masks.currencyInput.mask(value));
                            }}
                            onBlur={handleBlur}
                          />
                        </DS.Grid.Item>
                      </RenderConditional>
                      <RenderConditional
                        condition={formFop.openSelectValorPercentual}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="valorPercentual"
                            label={Constants.PORCENTAGEM_SALARIO}
                            component={DS.TextField}
                            value={masks.percentage.mask(
                              values.valorPercentual,
                            )}
                            error={errors.valorPercentual}
                            errorMessage={errors.valorPercentual}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<HTMLInputElement>) => {
                              setFieldValue('valorPercentual', value);
                            }}
                            onBlur={handleBlur}
                          />
                        </DS.Grid.Item>
                      </RenderConditional>
                      <RenderConditional
                        condition={formFop.openSelectOutraFormaPagamento}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Label>{Constants.CUSTEIO_PAGAMENTO}</Label>
                          <S.TextAreaFop
                            maxLength={150}
                            spellCheck
                            value={formFop.textOutraFormaPagamento}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<{ value: string }>) =>
                              formFop.setTextOutraFormaPagamento(value)
                            }
                          />
                          <Label>
                            {TextoCaracteres(
                              150,
                              formFop.textOutraFormaPagamento.length,
                            )}
                          </Label>
                        </DS.Grid.Item>
                      </RenderConditional>
                    </RenderConditional>
                    <RenderConditional
                      condition={formFop.openSelectPlanoInstituido}
                    >
                      <TitleSection>{Constants.EMPRESA}</TitleSection>
                      <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Field
                          name="valorContribuicaoEmpresa"
                          label={Constants.VALOR_CONTRIBUICAO}
                          component={DS.Select}
                          placeholder={Constants.SELECIONE}
                          value={values.valorContribuicaoEmpresa}
                          error={errors.valorContribuicaoEmpresa}
                          errorMessage={errors.valorContribuicaoEmpresa}
                          onChange={({
                            target: { value },
                          }: React.ChangeEvent<{
                            text: string;
                            value: string;
                          }>) => {
                            setFieldValue('valorContribuicaoEmpresa', value);
                            formFop.selectValorContribuicaoEmpresa(value);
                          }}
                          onBlur={handleBlur}
                        >
                          {Constants.selectOptionsValorContribuicaoEmpresa.map(
                            optValorContribuicaoEmpresa => (
                              <DS.Select.Item
                                key={optValorContribuicaoEmpresa.key}
                                value={optValorContribuicaoEmpresa.key}
                                text={optValorContribuicaoEmpresa.value}
                                selected={
                                  optValorContribuicaoEmpresa.key ===
                                  values.valorContribuicaoEmpresa
                                }
                              />
                            ),
                          )}
                        </Field>
                      </DS.Grid.Item>
                      <RenderConditional
                        condition={formFop.openSelectValorFixoEmpresa}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="valorFixoEmpresa"
                            label={Constants.VALOR_FIXO}
                            component={DS.TextField}
                            value={values.valorFixoEmpresa}
                            error={errors.valorFixoEmpresa}
                            maxLength={16}
                            errorMessage={errors.valorFixoEmpresa}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<HTMLInputElement>) => {
                              setFieldValue(
                                'valorFixoEmpresa',
                                masks.currencyInput.mask(value),
                              );
                            }}
                            onBlur={handleBlur}
                          />
                        </DS.Grid.Item>
                      </RenderConditional>
                      <RenderConditional
                        condition={formFop.openSelectValorPercentualEmpresa}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="valorPercentualEmpresa"
                            label={Constants.PORCENTAGEM_SALARIO}
                            component={DS.TextField}
                            value={masks.percentage.mask(
                              values.valorPercentualEmpresa,
                            )}
                            error={errors.valorPercentualEmpresa}
                            errorMessage={errors.valorPercentualEmpresa}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<HTMLInputElement>) => {
                              setFieldValue('valorPercentualEmpresa', value);
                            }}
                            onBlur={handleBlur}
                          />
                          {/* <p style={{ color: 'red' }}>
                            {Constants.VALOR_SALARIO_AVISO}
                          </p> */}
                        </DS.Grid.Item>
                      </RenderConditional>
                      <RenderConditional
                        condition={formFop.openSelectOutraFormaPagamentoEmpresa}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Label>{Constants.CUSTEIO_PAGAMENTO}</Label>
                          <S.TextAreaFop
                            maxLength={150}
                            spellCheck
                            value={formFop.textOutraFormaPagamentoEmpresa}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<{ value: string }>) =>
                              formFop.setTextOutraFormaPagamentoEmpresa(value)
                            }
                          />
                          <Label>
                            {TextoCaracteres(
                              150,
                              formFop.textOutraFormaPagamentoEmpresa.length,
                            )}
                          </Label>
                        </DS.Grid.Item>
                      </RenderConditional>
                      <DS.Divider />
                      <TitleSection>{Constants.COLABORADOR}</TitleSection>
                      <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Field
                          name="valorContribuicaoFuncionario"
                          label={Constants.VALOR_CONTRIBUICAO}
                          component={DS.Select}
                          placeholder={Constants.SELECIONE}
                          value={values.valorContribuicaoFuncionario}
                          error={errors.valorContribuicaoFuncionario}
                          errorMessage={errors.valorContribuicaoFuncionario}
                          onChange={({
                            target: { value },
                          }: React.ChangeEvent<{
                            text: string;
                            value: string;
                          }>) => {
                            setFieldValue(
                              'valorContribuicaoFuncionario',
                              value,
                            );
                            setFieldValue('valorFixoFuncionario', '');
                            setFieldValue('valorPercentualFuncionario', '',);
                            formFop.setTextOutraFormaPagamentoFuncionario('')
                            formFop.selectValorContribuicaoFuncionario(value);
                          }}
                          onBlur={handleBlur}
                        >
                          {Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.map(
                            optValorContribuicaoFuncionario => (
                              <DS.Select.Item
                                key={optValorContribuicaoFuncionario.key}
                                value={optValorContribuicaoFuncionario.key}
                                text={optValorContribuicaoFuncionario.value}
                                selected={
                                  optValorContribuicaoFuncionario.key ===
                                  values.valorContribuicaoFuncionario
                                }
                              />
                            ),
                          )}
                        </Field>
                      </DS.Grid.Item>
                      <RenderConditional
                        condition={formFop.openSelectValorFixoFuncionario}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="valorFixoFuncionario"
                            label={Constants.VALOR_FIXO}
                            component={DS.TextField}
                            value={values.valorFixoFuncionario}
                            error={errors.valorFixoFuncionario}
                            errorMessage={errors.valorFixoFuncionario}
                            maxLength={16}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<HTMLInputElement>) => {
                              setFieldValue(
                                'valorFixoFuncionario',
                                masks.currencyInput.mask(value),
                              );
                            }}
                            onBlur={handleBlur}
                          />
                        </DS.Grid.Item>
                      </RenderConditional>
                      <RenderConditional
                        condition={formFop.openSelectValorPercentualFuncionario}
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="valorPercentualFuncionario"
                            label={Constants.PORCENTAGEM_SALARIO}
                            component={DS.TextField}
                            value={masks.percentage.mask(
                              values.valorPercentualFuncionario,
                            )}
                            error={errors.valorPercentualFuncionario}
                            errorMessage={errors.valorPercentualFuncionario}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<HTMLInputElement>) => {
                              setFieldValue(
                                'valorPercentualFuncionario',
                                masks.percentage.mask(value),
                              );
                            }}
                            onBlur={handleBlur}
                          />
                          {/* <p style={{ color: 'red' }}>
                            {Constants.VALOR_SALARIO_AVISO}
                          </p> */}
                        </DS.Grid.Item>
                      </RenderConditional>
                      <RenderConditional
                        condition={
                          formFop.openSelectOutraFormaPagamentoFuncionario
                        }
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Label>{Constants.CUSTEIO_PAGAMENTO}</Label>
                          <S.TextAreaFop
                            maxLength={150}
                            spellCheck
                            value={formFop.textOutraFormaPagamentoFuncionario}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<{ value: string }>) =>
                              formFop.setTextOutraFormaPagamentoFuncionario(
                                value,
                              )
                            }
                          />
                          <Label>
                            {TextoCaracteres(
                              150,
                              formFop.textOutraFormaPagamentoFuncionario.length,
                            )}
                          </Label>
                        </DS.Grid.Item>
                      </RenderConditional>
                    </RenderConditional>
                  </S.AccordionItem>
                </DS.Accordion>
              </DS.Card.Content>
            </DS.Card>
            <br />
            <DS.Card>
              <DS.Card.Content padding={[4, 4, 4]}>
                <DS.Accordion open>
                  <S.AccordionItem title={Constants.TITULO_CUIDADO_EXTRA} style>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="tipoCuidadoExtra"
                        label={Constants.CUIDADO_EXTRA}
                        component={DS.Select}
                        placeholder={Constants.SELECIONE}
                        value={values.tipoCuidadoExtra}
                        error={errors.tipoCuidadoExtra}
                        errorMessage={errors.tipoCuidadoExtra}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<{
                          text: string;
                          value: string;
                        }>) => {
                          setFieldValue('tipoCuidadoExtra', value);
                          formFop.selectCuidadoExtra(value);
                        }}
                        onBlur={handleBlur}
                      >
                        {Enum.SELECT_OPTIONS_CUIDADO_EXTRA.map(
                          optTipoCuidadoExtra => (
                            <DS.Select.Item
                              key={optTipoCuidadoExtra.key}
                              value={optTipoCuidadoExtra.key}
                              text={optTipoCuidadoExtra.value}
                              selected={
                                optTipoCuidadoExtra.key ===
                                values.tipoCuidadoExtra
                              }
                            />
                          ),
                        )}
                      </Field>
                    </DS.Grid.Item>
                    <RenderConditional condition={formFop.openSelectPeculio}>
                      <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Field
                          name="regraCuidadoExtra"
                          label={Constants.SELECIONE_REGRA_CALCULO}
                          component={DS.Select}
                          placeholder={Constants.SELECIONE}
                          value={values.regraCuidadoExtra}
                          error={errors.regraCuidadoExtra}
                          errorMessage={errors.regraCuidadoExtra}
                          onChange={({
                            target: { value },
                          }: React.ChangeEvent<{
                            text: string;
                            value: string;
                          }>) => {
                            setFieldValue('regraCuidadoExtra', value);
                          }}
                          onBlur={handleBlur}
                        >
                          {Enum.SELECT_OPTIONS_REGRA_CUIDADO.map(
                            optRegraCuidadoExtra => (
                              <DS.Select.Item
                                key={optRegraCuidadoExtra.key}
                                value={optRegraCuidadoExtra.key}
                                text={optRegraCuidadoExtra.value}
                                selected={
                                  optRegraCuidadoExtra.key ===
                                  values.regraCuidadoExtra
                                }
                              />
                            ),
                          )}
                        </Field>
                      </DS.Grid.Item>
                    </RenderConditional>
                    <RenderConditional condition={formFop.openSelectPensao}>
                      <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Field
                          name="anosPensao"
                          label={Constants.PERGUNTA_PENSAO}
                          component={DS.Select}
                          placeholder={Constants.SELECIONE}
                          value={values.anosPensao}
                          error={errors.anosPensao}
                          errorMessage={errors.anosPensao}
                          onChange={({
                            target: { value },
                          }: React.ChangeEvent<{
                            text: string;
                            value: string;
                          }>) => {
                            setFieldValue('anosPensao', value);
                          }}
                          onBlur={handleBlur}
                        >
                          {Enum.SELECT_OPTIONS_ANOS_PENSAO.map(optAnosPensao => (
                            <DS.Select.Item
                              key={optAnosPensao.key}
                              value={optAnosPensao.key}
                              text={optAnosPensao.value}
                              selected={optAnosPensao.key === values.anosPensao}
                            />
                          ))}
                        </Field>
                      </DS.Grid.Item>
                      <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Field
                          name="regraCuidadoExtraPensao"
                          label={Constants.SELECIONE_REGRA_CALCULO}
                          component={DS.Select}
                          placeholder={Constants.SELECIONE}
                          value={values.regraCuidadoExtraPensao}
                          error={errors.regraCuidadoExtraPensao}
                          errorMessage={errors.regraCuidadoExtraPensao}
                          onChange={({
                            target: { value },
                          }: React.ChangeEvent<{
                            text: string;
                            value: string;
                          }>) => {
                            setFieldValue('regraCuidadoExtraPensao', value);
                          }}
                          onBlur={handleBlur}
                        >
                          {Enum.SELECT_OPTIONS_REGRA_CUIDADO_PENSAO.map((optRegraCuidadoExtraPensao: ISelectOption) => (
                            <DS.Select.Item
                              key={optRegraCuidadoExtraPensao.key}
                              value={optRegraCuidadoExtraPensao.key}
                              text={optRegraCuidadoExtraPensao.value}
                              selected={
                                optRegraCuidadoExtraPensao.key ===
                                values.regraCuidadoExtraPensao
                              }
                            />
                          ),
                          )}
                        </Field>
                      </DS.Grid.Item>
                    </RenderConditional>
                    <RenderConditional
                      condition={formFop.openSelectFormaPagamentoCuidado}
                    >
                      <>
                        <RenderConditional
                          condition={values.formaPagamento === Constants.AVERBADO}
                        >
                          <DS.Grid.Item xs={1} lg={1 / 2}>
                            <Field
                              name="formaPagamentoCuidado"
                              label={Constants.TIPO_PLANO_EMPRESARIAL}
                              component={DS.Select}
                              placeholder={Constants.SELECIONE}
                              value={values.formaPagamentoCuidado}
                              error={errors.formaPagamentoCuidado}
                              errorMessage={errors.formaPagamentoCuidado}
                              onChange={({
                                target: { value },
                              }: React.ChangeEvent<{
                                text: string;
                                value: string;
                              }>) => {
                                setFieldValue('formaPagamentoCuidado', value);
                                formFop.selectFormaPagamentoCuidado(value);
                              }}
                              onBlur={handleBlur}
                            >
                              {Constants.selectOptionsPagamentoAverbado.map(
                                (optFormaPagamentoCuidado: ISelectOption) => (
                                  <DS.Select.Item
                                    key={optFormaPagamentoCuidado.key}
                                    value={optFormaPagamentoCuidado.key}
                                    text={optFormaPagamentoCuidado.value}
                                    selected={
                                      optFormaPagamentoCuidado.key ===
                                      values.formaPagamentoCuidado
                                    }
                                  />
                                ),
                              )}
                            </Field>
                          </DS.Grid.Item>
                        </RenderConditional>
                        <RenderConditional
                          condition={values.formaPagamento !== Constants.AVERBADO}
                        >
                          <DS.Grid.Item xs={1} lg={1 / 2}>
                            <Field
                              name="formaPagamentoCuidado"
                              label={Constants.TIPO_PLANO_EMPRESARIAL}
                              component={DS.Select}
                              placeholder={Constants.SELECIONE}
                              value={values.formaPagamentoCuidado}
                              error={errors.formaPagamentoCuidado}
                              errorMessage={errors.formaPagamentoCuidado}
                              onChange={({
                                target: { value },
                              }: React.ChangeEvent<{
                                text: string;
                                value: string;
                              }>) => {
                                setFieldValue('formaPagamentoCuidado', value);
                                formFop.selectFormaPagamentoCuidado(value);
                                formFop.setTextOutraFormaPagamentoEmpresaCuidadoExtra(
                                  '',
                                )
                                setFieldValue(
                                  'valorContribuicaoEmpresaCuidadoExtra',
                                  '',
                                );
                                setFieldValue(
                                  'valorContribuicaoFuncionarioCuidadoExtra',
                                  '',
                                );
                                formFop.setTextOutraFormaPagamentoFuncionarioCuidadoExtra('')
                                setFieldValue(
                                  'valorPercentualFuncionarioCuidadoExtra',
                                  '',
                                );
                                setFieldValue(
                                  'valorFixoFuncionarioCuidadoExtra',
                                  ''
                                );
                                setFieldValue(
                                  'valorContribuicaoCuidadoExtra',
                                  '',
                                );
                              }}
                              onBlur={handleBlur}
                            >
                              {Constants.selectOptionsPagamento.map(
                                optFormaPagamentoCuidado => (
                                  <DS.Select.Item
                                    key={optFormaPagamentoCuidado.key}
                                    value={optFormaPagamentoCuidado.key}
                                    text={optFormaPagamentoCuidado.value}
                                    selected={
                                      optFormaPagamentoCuidado.key ===
                                      values.formaPagamentoCuidado
                                    }
                                  />
                                ),
                              )}
                            </Field>
                          </DS.Grid.Item>
                        </RenderConditional>
                      </>
                      <RenderConditional
                        condition={
                          formFop.openSelectValorContribuicaoCuidadoExtra
                        }
                      >
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="valorContribuicaoCuidadoExtra"
                            label={Constants.VALOR_CONTRIBUICAO}
                            component={DS.Select}
                            placeholder={Constants.SELECIONE}
                            value={values.valorContribuicaoCuidadoExtra}
                            error={errors.valorContribuicaoCuidadoExtra}
                            errorMessage={errors.valorContribuicaoCuidadoExtra}
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<{
                              text: string;
                              value: string;
                            }>) => {
                              setFieldValue(
                                'valorContribuicaoCuidadoExtra',
                                value,
                              );
                              setFieldValue(
                                'valorFixoCuidadoExtra',
                                "",
                              );
                              setFieldValue(
                                'valorPercentualCuidadoExtra',
                                "",
                              );
                              formFop.setTextOutraFormaPagamentoCuidadoExtra(
                                "",
                              );
                              formFop.selectValorContribuicaoCuidadoExtra(
                                value,
                              );
                            }}
                            onBlur={handleBlur}
                          >
                            {Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.map(
                              optValorContribuicaoCuidadoExtra => (
                                <DS.Select.Item
                                  key={optValorContribuicaoCuidadoExtra.key}
                                  value={optValorContribuicaoCuidadoExtra.key}
                                  text={optValorContribuicaoCuidadoExtra.value}
                                  selected={
                                    optValorContribuicaoCuidadoExtra.key ===
                                    values.valorContribuicaoCuidadoExtra
                                  }
                                />
                              ),
                            )}
                          </Field>
                        </DS.Grid.Item>
                        <RenderConditional
                          condition={formFop.openSelectValorFixoCuidadoExtra}
                        >
                          <DS.Grid.Item xs={1} lg={1 / 2}>
                            <Field
                              name="valorFixoCuidadoExtra"
                              label={Constants.VALOR_FIXO}
                              component={DS.TextField}
                              value={values.valorFixoCuidadoExtra}
                              maxLength={16}
                              error={errors.valorFixoCuidadoExtra}
                              errorMessage={errors.valorFixoCuidadoExtra}
                              onChange={({
                                target: { value },
                              }: React.ChangeEvent<HTMLInputElement>) => {
                                setFieldValue(
                                  'valorFixoCuidadoExtra',
                                  masks.currencyInput.mask(value),
                                );
                              }}
                              onBlur={handleBlur}
                            />
                          </DS.Grid.Item>
                        </RenderConditional>
                        <RenderConditional
                          condition={
                            formFop.openSelectValorPercentualCuidadoExtra
                          }
                        >
                          <DS.Grid.Item xs={1} lg={1 / 2}>
                            <Field
                              name="valorPercentualCuidadoExtra"
                              label={Constants.PORCENTAGEM_SALARIO}
                              component={DS.TextField}
                              value={values.valorPercentualCuidadoExtra}
                              error={errors.valorPercentualCuidadoExtra}
                              errorMessage={errors.valorPercentualCuidadoExtra}
                              onChange={({
                                target: { value },
                              }: React.ChangeEvent<HTMLInputElement>) => {
                                setFieldValue(
                                  'valorPercentualCuidadoExtra',
                                  masks.percentage.mask(value),
                                );
                              }}
                              onBlur={handleBlur}
                            />
                          </DS.Grid.Item>
                        </RenderConditional>
                        <RenderConditional
                          condition={
                            formFop.openSelectOutraFormaPagamentoCuidadoExtra
                          }
                        >
                          <DS.Grid.Item xs={1} lg={1 / 2}>
                            <Label>{Constants.CUSTEIO_PAGAMENTO}</Label>
                            <S.TextAreaFop
                              maxLength={150}
                              spellCheck
                              value={
                                formFop.textOutraFormaPagamentoCuidadoExtra
                              }
                              onChange={({
                                target: { value },
                              }: React.ChangeEvent<{ value: string }>) => {
                                if (
                                  value.length <= 150
                                ) {
                                  formFop.setTextOutraFormaPagamentoCuidadoExtra(
                                    value,
                                  );
                                }
                              }}
                            />
                            <Label>
                              {TextoCaracteres(
                                150,
                                formFop.textOutraFormaPagamentoCuidadoExtra
                                  .length,
                              )}
                            </Label>
                          </DS.Grid.Item>
                        </RenderConditional>
                      </RenderConditional>
                      <RenderConditional
                        condition={
                          formFop.openSelectPlanoInstituidoCuidadoExtra
                        }
                      >
                        <TitleSection>{Constants.EMPRESA}</TitleSection>
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="valorContribuicaoEmpresaCuidadoExtra"
                            label={Constants.VALOR_CONTRIBUICAO}
                            component={DS.Select}
                            placeholder={Constants.SELECIONE}
                            value={values.valorContribuicaoEmpresaCuidadoExtra}
                            error={errors.valorContribuicaoEmpresaCuidadoExtra}
                            errorMessage={
                              errors.valorContribuicaoEmpresaCuidadoExtra
                            }
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<{
                              text: string;
                              value: string;
                            }>) => {
                              setFieldValue(
                                'valorContribuicaoEmpresaCuidadoExtra',
                                value,
                              );
                              formFop.selectValorContribuicaoEmpresaCuidadoExtra(
                                value,
                              );
                              formFop.setTextOutraFormaPagamentoFuncionarioCuidadoExtra('')

                            }}
                            onBlur={handleBlur}
                          >
                            {Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.map(
                              (optValorContribuicaoEmpresaCuidadoExtra: ISelectOption) => (
                                <DS.Select.Item
                                  key={
                                    optValorContribuicaoEmpresaCuidadoExtra.key
                                  }
                                  value={
                                    optValorContribuicaoEmpresaCuidadoExtra.key
                                  }
                                  text={
                                    optValorContribuicaoEmpresaCuidadoExtra.value
                                  }
                                  selected={
                                    optValorContribuicaoEmpresaCuidadoExtra.key ===
                                    values.valorContribuicaoEmpresaCuidadoExtra
                                  }
                                />
                              ),
                            )}
                          </Field>
                        </DS.Grid.Item>
                        <RenderConditional
                          condition={
                            formFop.openSelectOutraFormaPagamentoEmpresaCuidadoExtra
                          }
                        >
                          <DS.Grid.Item xs={1} lg={1 / 2}>
                            <Label>{Constants.CUSTEIO_PAGAMENTO}</Label>
                            <S.TextAreaFop
                              maxLength={150}
                              spellCheck
                              value={
                                formFop.textOutraFormaPagamentoEmpresaCuidadoExtra
                              }
                              onChange={({
                                target: { value },
                              }: React.ChangeEvent<{ value: string }>) =>
                                formFop.setTextOutraFormaPagamentoEmpresaCuidadoExtra(
                                  value,
                                )
                              }
                            />
                            <Label>
                              {TextoCaracteres(
                                150,
                                formFop
                                  .textOutraFormaPagamentoEmpresaCuidadoExtra
                                  .length,
                              )}
                            </Label>
                          </DS.Grid.Item>
                        </RenderConditional>
                        <DS.Divider />
                        <TitleSection>{Constants.COLABORADOR}</TitleSection>
                        <DS.Grid.Item xs={1} lg={1 / 2}>
                          <Field
                            name="valorContribuicaoFuncionarioCuidadoExtra"
                            label={Constants.VALOR_CONTRIBUICAO}
                            component={DS.Select}
                            placeholder={Constants.SELECIONE}
                            value={
                              values.valorContribuicaoFuncionarioCuidadoExtra
                            }
                            error={
                              errors.valorContribuicaoFuncionarioCuidadoExtra
                            }
                            errorMessage={
                              errors.valorContribuicaoFuncionarioCuidadoExtra
                            }
                            onChange={({
                              target: { value },
                            }: React.ChangeEvent<{
                              text: string;
                              value: string;
                            }>) => {
                              setFieldValue(
                                'valorContribuicaoFuncionarioCuidadoExtra',
                                value,
                              );
                              setFieldValue(
                                'valorFixoFuncionarioCuidadoExtra',
                                "",
                              );
                              setFieldValue(
                                'valorPercentualFuncionarioCuidadoExtra',
                                '',
                              );
                              formFop.setTextOutraFormaPagamentoFuncionarioCuidadoExtra("")

                              formFop.selectValorContribuicaoFuncionarioCuidadoExtra(
                                value,
                              );
                            }}
                            onBlur={handleBlur}
                          >
                            {Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.map(
                              optValorContribuicaoFuncionarioCuidadoExtra => (
                                <DS.Select.Item
                                  key={
                                    optValorContribuicaoFuncionarioCuidadoExtra.key
                                  }
                                  value={
                                    optValorContribuicaoFuncionarioCuidadoExtra.key
                                  }
                                  text={
                                    optValorContribuicaoFuncionarioCuidadoExtra.value
                                  }
                                  selected={
                                    optValorContribuicaoFuncionarioCuidadoExtra.key ===
                                    values.valorContribuicaoFuncionarioCuidadoExtra
                                  }
                                />
                              ),
                            )}
                          </Field>
                        </DS.Grid.Item>
                        <RenderConditional
                          condition={
                            formFop.openSelectValorFixoFuncionarioCuidadoExtra
                          }
                        >
                          <DS.Grid.Item xs={1} lg={1 / 2}>
                            <Field
                              name="valorFixoFuncionarioCuidadoExtra"
                              label={Constants.VALOR_FIXO}
                              component={DS.TextField}
                              value={values.valorFixoFuncionarioCuidadoExtra}
                              error={errors.valorFixoFuncionarioCuidadoExtra}
                              maxLength={16}
                              onChange={({
                                target: { value },
                              }: React.ChangeEvent<HTMLInputElement>) => {
                                setFieldValue(
                                  'valorFixoFuncionarioCuidadoExtra',
                                  masks.currencyInput.mask(value),
                                );
                              }}
                              errorMessage={
                                errors.valorFixoFuncionarioCuidadoExtra
                              }
                              onBlur={handleBlur}
                            />
                          </DS.Grid.Item>
                        </RenderConditional>
                        <></>
                        <RenderConditional
                          condition={
                            formFop.openSelectValorPercentualFuncionarioCuidadoExtra
                          }
                        >
                          <DS.Grid.Item xs={1} lg={1 / 2}>
                            <Field
                              label={Constants.PORCENTAGEM_SALARIO}
                              name="valorPercentualFuncionarioCuidadoExtra"
                              value={masks.percentage.mask(
                                values.valorPercentualFuncionarioCuidadoExtra,
                              )}
                              component={DS.TextField}
                              error={
                                errors.valorPercentualFuncionarioCuidadoExtra
                              }
                              errorMessage={
                                errors.valorPercentualFuncionarioCuidadoExtra
                              }
                              onChange={({
                                target: { value },
                              }: React.ChangeEvent<HTMLInputElement>) => {
                                setFieldValue(
                                  'valorPercentualFuncionarioCuidadoExtra',
                                  value,
                                );
                              }}
                              onBlur={handleBlur}
                            />
                            {/*
                            <p style={{ color: 'red' }}>
                              {Constants.VALOR_SALARIO_AVISO}
                            </p> */}
                          </DS.Grid.Item>
                        </RenderConditional>
                        <RenderConditional
                          condition={
                            formFop.openSelectOutraFormaPagamentoFuncionarioCuidadoExtra
                          }
                        >
                          <DS.Grid.Item xs={1} lg={1 / 2}>
                            <Label>{Constants.CUSTEIO_PAGAMENTO}</Label>
                            <S.TextAreaFop
                              spellCheck
                              value={formFop.textOutraFormaPagamentoFuncionarioCuidadoExtra}
                              maxLength={150}
                              onChange={({
                                target: { value },
                              }: React.ChangeEvent<{ value: string }>) =>
                                formFop.setTextOutraFormaPagamentoFuncionarioCuidadoExtra(value)
                              }
                            />
                            <Label>
                              {TextoCaracteres(
                                150,
                                formFop.textOutraFormaPagamentoFuncionarioCuidadoExtra.length,
                              )}
                            </Label>
                          </DS.Grid.Item>
                        </RenderConditional>
                      </RenderConditional>
                    </RenderConditional>
                  </S.AccordionItem>
                </DS.Accordion>
              </DS.Card.Content>
            </DS.Card>
            <br />
            <DS.Card>
              <DS.Card.Content padding={[4, 4, 4]}>
                <DS.Accordion open>
                  <S.AccordionItem
                    title={Constants.TITULO_INFORMACOES_COMPLEMENTARES}>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Label>{Constants.INFORMACOES_COMPLEMENTARES}</Label>
                      <S.TextAreaFop
                        spellCheck
                        value={formFop.textInformacoesComplementares}
                        maxLength={Constants.LIMITE_CARACTERES}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<{ value: string }>) =>
                          formFop.setTextInformacoesComplementares(value)
                        }
                      />
                      <Label>
                        {TextoCaracteres(
                          Constants.LIMITE_CARACTERES,
                          formFop.textInformacoesComplementares.length,
                        )}
                      </Label>
                    </DS.Grid.Item>
                  </S.AccordionItem>
                </DS.Accordion>
              </DS.Card.Content>
            </DS.Card>
            <br />
            <DS.Card>
              <DS.Card.Content padding={[4, 4, 4]}>
                <DS.Accordion open>
                  <S.AccordionItem title={Constants.TITULO_DADOS_RESPONSAVEL}>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="nomeCompleto"
                        label={enumFop.DadosResponsaveis.nomeCompleto}
                        component={DS.TextField}
                        value={values.nomeCompleto}
                        error={errors.nomeCompleto}
                        maxLength={50}
                        errorMessage={errors.nomeCompleto}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue('nomeCompleto', value);
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="telefone"
                        label={enumFop.DadosResponsaveis.telefone}
                        component={DS.TextField}
                        value={masks.phone.mask(values.telefone)}
                        error={errors.telefone}
                        errorMessage={errors.telefone}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue('telefone', value);
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                      <Field
                        name="email"
                        label={enumFop.DadosResponsaveis.EmailAviso}
                        component={DS.TextField}
                        value={values.email}
                        error={errors.email}
                        errorMessage={errors.email}
                        onChange={({
                          target: { value },
                        }: React.ChangeEvent<HTMLInputElement>) => {
                          setFieldValue('email', value);
                        }}
                        onBlur={handleBlur}
                      />
                    </DS.Grid.Item>
                    <DS.Divider />
                    <DS.Text variant="body01-sm" color="primary" margin>
                      <DownloadButton
                        variant="text"
                        type="button"
                        onClick={() => {
                          const arquivo = dataLocation[0]?.dadosArquivos?.[0];
                          if (!arquivo) {
                            toastError('Erro ao Baixar a planilha');
                            return;
                          }
                          formFop.baixarArquivoFop(
                            arquivo?.codigoIdentificadorUnico?.toString(),
                            arquivo.nomeArquivo
                          );
                        }}
                      >
                        {formFop.loadingDownload && (
                          <DS.Button variant="text" loading />
                        )}
                        {!formFop.loadingDownload && (
                          <>
                            {Constants.INFORMATIVO_DOWNLOAD_FOP} &nbsp;
                            <Icon name="download" />
                          </>
                        )}
                      </DownloadButton>
                    </DS.Text>
                    <DS.Grid.Item xs={1}>
                      <S.InputLabel>{Constants.TEXTO_ANEXO_FOP64}</S.InputLabel>
                      <DescriptionFormatsFilesAllowed fileSize="3" textFormat='Formatos de arquivos suportados: .xlsx e .xlsm' />
                      <DS.Grid.Item xs={1 / 2}>
                        <InputFile
                          link={formFop.arquivoAnexoFop}
                          validationRules={formFop.regraFiles}
                          error={!formFop.arquivoAnexoFop.get()?.errorMsg}
                        />
                      </DS.Grid.Item>
                    </DS.Grid.Item>
                    <DS.Modal
                      show={formFop.openModalFormulario}
                      onClose={formFop.fecharModal}
                      style={{ maxWidth: `90%` }}
                    >
                      <DS.Display justify="end" style={{ flexDirection: "column" }}>
                        <DS.Text variant="body02-md" margin>
                          <strong>{Constants.AVISO_CONFIRA_DADOS}</strong>
                        </DS.Text>
                        <br />
                        <DS.Card.Content>
                          <DS.Accordion open>
                            <DS.Accordion.Item
                              title={Constants.TITULO_INFORMACOES_GERAIS}
                            >
                              <DS.Grid>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {enumFop.InformacoesGerais.nomeEmpresa}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="nomeEmpresa"
                                    >
                                      {values.nomeEmpresa}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {
                                        enumFop.InformacoesGerais
                                          .atividadePrincipal
                                      }
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="atividadePrincipal"
                                    >
                                      {values.atividadePrincipal}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {enumFop.InformacoesGerais.CNPJ}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="cnpj"
                                    >
                                      {values.cnpj}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {enumFop.InformacoesGerais.numeroAgencia}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="numeroAgencia"
                                    >
                                      {user.agenciaVinculada}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {
                                        enumFop.InformacoesGerais
                                          .superintendenciaRegional
                                      }
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="superintendenciaRegional"
                                    >
                                      {values.superintendenciaRegional}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {enumFop.InformacoesGerais.nomeAgencia}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="nomeAgencia"
                                    >
                                      {values.nomeAgencia}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {enumFop.InformacoesGerais.filial}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="filial"
                                    >
                                      {
                                        Enum.SELECT_OPTIONS_FILIAL.find(
                                          x => x.key === values.filial,
                                        )?.value
                                      }
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {
                                        enumFop.InformacoesGerais
                                          .matriculaIndicador
                                      }
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="matriculaIndicador"
                                    >
                                      {values.matriculaIndicador}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {enumFop.InformacoesGerais.nomeIndicador}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="nomeIndicador"
                                    >
                                      {values.nomeIndicador}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                              </DS.Grid>
                            </DS.Accordion.Item>
                          </DS.Accordion>
                          <DS.Accordion open>
                            <DS.Accordion.Item
                              title={Constants.TITULO_DADOS_DO_PLANO}
                            >
                              <DS.Grid>
                                <DS.Grid.Item xs={1} lg={1}>
                                  <TextLabel variant="body02-sm">
                                    {Constants.NUMERO_PARTICIPANTES}
                                  </TextLabel>
                                  <DS.Text
                                    variant="body02-sm"
                                    data-testid="numeroParticipantes"
                                  >
                                    {values.numeroParticipantes}
                                  </DS.Text>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {Constants.REGRA_CALCULO}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="regraParaCalculo"
                                    >
                                      {
                                        Constants.selectOptionsRegra.find(
                                          x =>
                                            x.key === values.regraParaCalculo,
                                        )?.value
                                      }
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <RenderConditional
                                  condition={formFop.openSelectTipoBenficio}
                                >
                                  <DS.Grid.Item xs={1} lg={1 / 2}>
                                    <div>
                                      <TextLabel variant="body02-sm">
                                        {Constants.BENEFICIO_BASICO}
                                      </TextLabel>
                                      <DS.Text
                                        variant="body02-sm"
                                        data-testid="tipoDeBeneficioBasico"
                                      >
                                        {
                                          Enum.SELECT_OPTIONS_BENEFICIO.find(
                                            x =>
                                              x.key ===
                                              values.tipoDeBeneficioBasico,
                                          )?.value
                                        }
                                      </DS.Text>
                                    </div>
                                  </DS.Grid.Item>
                                  <RenderConditional
                                    condition={formFop.openSelectReversivel}
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.PORCENTAGEM_REVERSAO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="selectReversao"
                                        >
                                          {values.selectReversao}
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>
                                  <RenderConditional
                                    condition={formFop.openSelectPrazoBenficio}
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.PRAZO_BENEFICIO_BASICO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="prazoDeBeneficio"
                                        >
                                          {values.prazoDeBeneficio}
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>
                                </RenderConditional>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {Constants.TIPO_PLANO_EMPRESARIAL}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="formaPagamento"
                                    >
                                      {
                                        Constants.selectOptionsPagamento.find(
                                          x => x.key === values.formaPagamento,
                                        )?.value
                                      }
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <RenderConditional
                                  condition={
                                    formFop.openSelectValoresParticipantes
                                  }
                                >
                                  <DS.Grid.Item xs={1} lg={1 / 2}>
                                    <p style={{ color: 'red' }}>
                                      {Constants.AVISO_FOP_64}
                                    </p>
                                  </DS.Grid.Item>
                                </RenderConditional>

                                <RenderConditional
                                  condition={
                                    formFop.openSelectValorContribuicao
                                  }
                                >
                                  <DS.Grid.Item xs={1} lg={1 / 2}>
                                    <div>
                                      <TextLabel variant="body02-sm">
                                        {Constants.VALOR_CONTRIBUICAO}
                                      </TextLabel>
                                      <DS.Text
                                        variant="body02-sm"
                                        data-testid="valorContribuicao"
                                      >
                                        {
                                          Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
                                            x =>
                                              x.key ===
                                              values.valorContribuicao,
                                          )?.value
                                        }
                                      </DS.Text>
                                    </div>
                                  </DS.Grid.Item>

                                  <RenderConditional
                                    condition={formFop.openSelectValorFixo}
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.VALOR_FIXO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="valorFixo"
                                        >
                                          {values.valorFixo}
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>

                                  <RenderConditional
                                    condition={
                                      formFop.openSelectValorPercentual
                                    }
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.PORCENTAGEM_SALARIO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="nomeIndicador"
                                        >
                                          {values.valorPercentual}
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>

                                  <RenderConditional
                                    condition={
                                      formFop.openSelectOutraFormaPagamento
                                    }
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.CUSTEIO_PAGAMENTO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="nomeIndicador"
                                          style={{ wordBreak: "break-word" }}
                                        >
                                          {formFop.textOutraFormaPagamento}
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>
                                </RenderConditional>
                                <RenderConditional
                                  condition={formFop.openSelectPlanoInstituido}
                                >
                                  <DS.Divider />
                                  <DS.Grid.Item xs={1} lg={1 / 2}>
                                    <TitleSection>
                                      {Constants.EMPRESA}
                                    </TitleSection>
                                    <div>
                                      <TextLabel variant="body02-sm">
                                        {Constants.VALOR_CONTRIBUICAO}
                                      </TextLabel>
                                      <DS.Text
                                        variant="body02-sm"
                                        data-testid="valorContribuicaoEmpresa"
                                      >
                                        {
                                          Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
                                            x =>
                                              x.key ===
                                              values.valorContribuicaoEmpresa,
                                          )?.value
                                        }
                                      </DS.Text>
                                    </div>
                                  </DS.Grid.Item>
                                  <RenderConditional
                                    condition={
                                      formFop.openSelectValorFixoEmpresa
                                    }
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <br />
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.VALOR_FIXO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="valorFixoEmpresa"
                                        >
                                          {values.valorFixoEmpresa}
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>
                                  <RenderConditional
                                    condition={
                                      formFop.openSelectValorPercentualEmpresa
                                    }
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <br />
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.PORCENTAGEM_SALARIO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="valorPercentualEmpresa"
                                        >
                                          {masks.percentage.unmask(
                                            values.valorPercentualEmpresa,
                                          )}
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>
                                  <RenderConditional
                                    condition={
                                      formFop.openSelectOutraFormaPagamentoEmpresa
                                    }
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.CUSTEIO_PAGAMENTO}
                                        </TextLabel>
                                        <S.TextAreaFop
                                          spellCheck
                                          value={formFop.textOutraFormaPagamentoEmpresa}
                                          maxLength={Constants.LIMITE_CARACTERES}
                                          onChange={({
                                            target: { value },
                                          }: React.ChangeEvent<{ value: string }>) =>
                                            formFop.setTextOutraFormaPgEmpresa(value)
                                          }
                                        />
                                        <Label>
                                          {TextoCaracteres(
                                            Constants.LIMITE_CARACTERES,
                                            formFop.textInformacoesComplementares.length,
                                          )}
                                        </Label>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>

                                  <DS.Divider />
                                  <DS.Grid.Item xs={1} lg={1 / 2}>
                                    <TitleSection>
                                      {Constants.FUNCIONARIO}
                                    </TitleSection>
                                    <div>
                                      <TextLabel variant="body02-sm">
                                        {Constants.VALOR_CONTRIBUICAO}
                                      </TextLabel>
                                      <DS.Text
                                        variant="body02-sm"
                                        data-testid="valorContribuicaoFuncionario"
                                      >
                                        {
                                          Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
                                            x =>
                                              x.key ===
                                              values.valorContribuicaoFuncionario,
                                          )?.value
                                        }
                                      </DS.Text>
                                    </div>
                                  </DS.Grid.Item>
                                  <RenderConditional
                                    condition={
                                      formFop.openSelectValorFixoFuncionario
                                    }
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <br />
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.VALOR_FIXO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="valorFixoFuncionario"
                                        >
                                          {values.valorFixoFuncionario}
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>
                                  <RenderConditional
                                    condition={
                                      formFop.openSelectValorPercentualFuncionario
                                    }
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <br />
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.PORCENTAGEM_SALARIO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="valorPercentualFuncionario"
                                        >
                                          {values.valorPercentualFuncionario}
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>
                                  <RenderConditional
                                    condition={
                                      formFop.openSelectOutraFormaPagamentoFuncionario
                                    }
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.CUSTEIO_PAGAMENTO}
                                        </TextLabel>
                                        <S.TextAreaFop
                                          spellCheck
                                          value={formFop.textOutraFormaPagamentoFuncionario}
                                          maxLength={Constants.LIMITE_CARACTERES}
                                          onChange={({
                                            target: { value },
                                          }: React.ChangeEvent<{ value: string }>) =>
                                            formFop.setTextOutraFormaPagamentoFuncionario(value)
                                          }
                                        />
                                        <Label>
                                          {TextoCaracteres(
                                            Constants.LIMITE_CARACTERES,
                                            formFop.textOutraFormaPagamentoFuncionario.length,
                                          )}
                                        </Label>
                                      </div>
                                    </DS.Grid.Item>
                                  </RenderConditional>
                                </RenderConditional>
                              </DS.Grid>
                            </DS.Accordion.Item>
                          </DS.Accordion>
                          <DS.Accordion open>
                            <DS.Accordion.Item
                              title={Constants.TITULO_CUIDADO_EXTRA}
                            >
                              <DS.Grid>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {Constants.CUIDADO_EXTRA}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="tipoCuidadoExtra"
                                    >
                                      {
                                        Enum.SELECT_OPTIONS_CUIDADO_EXTRA.find(
                                          x =>
                                            x.key === values.tipoCuidadoExtra,
                                        )?.value
                                      }
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>

                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {Constants.REGRA_CALCULO}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="regraCuidadoExtra"
                                    >
                                      {
                                        Enum.SELECT_OPTIONS_REGRA_CUIDADO.find(
                                          x =>
                                            x.key === values.regraCuidadoExtra,
                                        )?.value
                                      }
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>

                                <RenderConditional
                                  condition={formFop.openSelectPensao}
                                >
                                  <DS.Grid.Item xs={1} lg={1 / 2}>
                                    <div>
                                      <TextLabel variant="body02-sm">
                                        {Constants.PENSAO_BENENFICIARIOS}
                                      </TextLabel>
                                      <DS.Text
                                        variant="body02-sm"
                                        data-testid="anosPensao"
                                      >
                                        {values.anosPensao}
                                      </DS.Text>
                                    </div>
                                  </DS.Grid.Item>
                                  <DS.Grid.Item xs={1} lg={1 / 2}>
                                    <div>
                                      <TextLabel variant="body02-sm">
                                        {Constants.REGRA_CALCULO}
                                      </TextLabel>
                                      <DS.Text
                                        variant="body02-sm"
                                        data-testid="regraCuidadoExtraPensao"
                                      >
                                        {
                                          Enum.SELECT_OPTIONS_REGRA_CUIDADO_PENSAO.find(
                                            x =>
                                              x.key ===
                                              values.regraCuidadoExtraPensao,
                                          )?.value
                                        }
                                      </DS.Text>
                                    </div>
                                  </DS.Grid.Item>
                                </RenderConditional>
                                <RenderConditional
                                  condition={
                                    formFop.openSelectFormaPagamentoCuidado
                                  }
                                >
                                  <DS.Grid.Item xs={1} lg={1 / 2}>
                                    <div>
                                      <TextLabel variant="body02-sm">
                                        {Constants.FORMA_PAGAMENTO}
                                      </TextLabel>
                                      <DS.Text
                                        variant="body02-sm"
                                        data-testid="formaPagamentoCuidado"
                                      >
                                        {
                                          Constants.selectOptionsPagamento.find(
                                            x =>
                                              x.key ===
                                              values.formaPagamentoCuidado,
                                          )?.value
                                        }
                                      </DS.Text>
                                    </div>
                                  </DS.Grid.Item>
                                  <DS.Grid.Item xs={1} lg={1 / 2}>
                                    <div>
                                      <TextLabel variant="body02-sm">
                                        {Constants.VALORES_IGUAIS}
                                      </TextLabel>
                                      <DS.Text
                                        variant="body02-sm"
                                        data-testid="valoresParticipanteCuidado"
                                      >
                                        {values.valoresParticipanteCuidado}
                                      </DS.Text>
                                    </div>
                                  </DS.Grid.Item>
                                  <RenderConditional
                                    condition={
                                      formFop.openSelectValorContribuicaoCuidadoExtra
                                    }
                                  >
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.VALOR_CONTRIBUICAO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="valorContribuicaoCuidadoExtra"
                                        >
                                          {
                                            Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
                                              x =>
                                                x.key ===
                                                values.valorContribuicaoCuidadoExtra,
                                            )?.value
                                          }
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                    <RenderConditional
                                      condition={
                                        formFop.openSelectValorFixoCuidadoExtra
                                      }
                                    >
                                      <DS.Grid.Item xs={1} lg={1 / 2}>
                                        <div>
                                          <TextLabel variant="body02-sm">
                                            {Constants.VALOR_FIXO}
                                          </TextLabel>
                                          <DS.Text
                                            variant="body02-sm"
                                            data-testid="valorFixoCuidadoExtra"
                                          >
                                            {masks.currencyInput.mask(
                                              values.valorFixoCuidadoExtra,
                                            )}
                                          </DS.Text>
                                        </div>
                                      </DS.Grid.Item>
                                    </RenderConditional>
                                    <RenderConditional
                                      condition={
                                        formFop.openSelectValorPercentualCuidadoExtra
                                      }
                                    >
                                      <DS.Grid.Item xs={1} lg={1 / 2}>
                                        <div>
                                          <TextLabel variant="body02-sm">
                                            {Constants.PORCENTAGEM_SALARIO}
                                          </TextLabel>
                                          <DS.Text
                                            variant="body02-sm"
                                            data-testid="valorPercentualCuidadoExtra"
                                          >
                                            {values.valorPercentualCuidadoExtra}
                                          </DS.Text>
                                        </div>
                                      </DS.Grid.Item>
                                    </RenderConditional>
                                    <RenderConditional
                                      condition={
                                        formFop.openSelectOutraFormaPagamentoCuidadoExtra
                                      }
                                    >
                                      <DS.Grid.Item xs={1} lg={1 / 2}>
                                        <div>
                                          <TextLabel variant="body02-sm">
                                            {Constants.CUSTEIO_PAGAMENTO}
                                          </TextLabel>
                                          <DS.Text
                                            variant="body02-sm"
                                            data-testid="textOutraFormaPagamentoCuidadoExtra"
                                            style={{ wordBreak: "break-word" }}
                                          >
                                            {
                                              formFop.textOutraFormaPagamentoCuidadoExtra
                                            }
                                          </DS.Text>
                                        </div>
                                      </DS.Grid.Item>
                                    </RenderConditional>
                                  </RenderConditional>
                                  <RenderConditional
                                    condition={
                                      formFop.openSelectPlanoInstituidoCuidadoExtra
                                    }
                                  >
                                    <DS.Divider />
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <TitleSection>
                                        {Constants.EMPRESA}
                                      </TitleSection>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.VALOR_CONTRIBUICAO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="valorContribuicaoEmpresaCuidadoExtra"
                                        >
                                          {
                                            Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
                                              x =>
                                                x.key ===
                                                values.valorContribuicaoEmpresaCuidadoExtra,
                                            )?.value
                                          }
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                    <RenderConditional
                                      condition={
                                        formFop.openSelectValorFixoEmpresaCuidadoExtra
                                      }
                                    >
                                      <DS.Grid.Item xs={1} lg={1 / 2}>
                                        <br />
                                        <div>
                                          <TextLabel variant="body02-sm">
                                            {Constants.VALOR_FIXO}
                                          </TextLabel>
                                          <DS.Text
                                            variant="body02-sm"
                                            data-testid="valorFixoEmpresaCuidadoExtra"
                                          >
                                            {
                                              values.valorFixoEmpresaCuidadoExtra
                                            }
                                          </DS.Text>
                                        </div>
                                      </DS.Grid.Item>
                                    </RenderConditional>
                                    <RenderConditional
                                      condition={
                                        formFop.openSelectValorPercentualEmpresaCuidadoExtra
                                      }
                                    >
                                      <DS.Grid.Item xs={1} lg={1 / 2}>
                                        <br />
                                        <div>
                                          <TextLabel variant="body02-sm">
                                            {Constants.PORCENTAGEM_SALARIO}
                                          </TextLabel>
                                          <DS.Text
                                            variant="body02-sm"
                                            data-testid="valorPercentualEmpresaCuidadoExtra"
                                          >
                                            {
                                              values.valorPercentualEmpresaCuidadoExtra
                                            }
                                          </DS.Text>
                                        </div>
                                      </DS.Grid.Item>
                                    </RenderConditional>
                                    <RenderConditional
                                      condition={
                                        formFop.openSelectOutraFormaPagamentoEmpresaCuidadoExtra
                                      }
                                    >
                                      <DS.Grid.Item xs={1} lg={1 / 2}>
                                        <br />
                                        <div>
                                          <TextLabel variant="body02-sm">
                                            {Constants.CUSTEIO_PAGAMENTO}
                                          </TextLabel>
                                          <DS.Text
                                            variant="body02-sm"
                                            data-testid="textOutraFormaPagamentoEmpresaCuidadoExtra"
                                          >
                                            {
                                              formFop.textOutraFormaPagamentoEmpresaCuidadoExtra
                                            }
                                          </DS.Text>
                                        </div>
                                      </DS.Grid.Item>
                                    </RenderConditional>
                                    <DS.Divider />
                                    <DS.Grid.Item xs={1} lg={1 / 2}>
                                      <TitleSection>Colaborador</TitleSection>
                                      <div>
                                        <TextLabel variant="body02-sm">
                                          {Constants.VALOR_CONTRIBUICAO}
                                        </TextLabel>
                                        <DS.Text
                                          variant="body02-sm"
                                          data-testid="valorContribuicaoFuncionarioCuidadoExtra"
                                        >
                                          {
                                            Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
                                              x =>
                                                x.key ===
                                                values.valorContribuicaoFuncionarioCuidadoExtra,
                                            )?.value
                                          }
                                        </DS.Text>
                                      </div>
                                    </DS.Grid.Item>
                                    <RenderConditional
                                      condition={
                                        formFop.openSelectValorFixoFuncionarioCuidadoExtra
                                      }
                                    >
                                      <DS.Grid.Item xs={1} lg={1 / 2}>
                                        <br />
                                        <div>
                                          <TextLabel variant="body02-sm">
                                            {Constants.VALOR_FIXO}
                                          </TextLabel>
                                          <DS.Text
                                            variant="body02-sm"
                                            data-testid="valorFixoFuncionarioCuidadoExtra"
                                          >
                                            {
                                              values.valorFixoFuncionarioCuidadoExtra
                                            }
                                          </DS.Text>
                                        </div>
                                      </DS.Grid.Item>
                                    </RenderConditional>
                                    <RenderConditional
                                      condition={
                                        formFop.openSelectValorPercentualFuncionarioCuidadoExtra
                                      }
                                    >
                                      <DS.Grid.Item xs={1} lg={1 / 2}>
                                        <br />
                                        <div>
                                          <TextLabel variant="body02-sm">
                                            {Constants.PORCENTAGEM_SALARIO}
                                          </TextLabel>
                                          <DS.Text
                                            variant="body02-sm"
                                            data-testid="valorPercentualFuncionarioCuidadoExtra"
                                          >
                                            {
                                              values.valorPercentualFuncionarioCuidadoExtra
                                            }
                                          </DS.Text>
                                        </div>
                                      </DS.Grid.Item>
                                    </RenderConditional>
                                    <RenderConditional
                                      condition={
                                        formFop.openSelectOutraFormaPagamentoFuncionarioCuidadoExtra
                                      }
                                    >
                                      <DS.Grid.Item xs={1} lg={1 / 2}>
                                        <br />
                                        <div>
                                          <TextLabel variant="body02-sm">
                                            {Constants.CUSTEIO_PAGAMENTO}
                                          </TextLabel>
                                          <DS.Text
                                            variant="body02-sm"
                                            data-testid="textOutraFormaPagamentoFuncionarioCuidadoExtra"
                                          >
                                            {
                                              formFop.textOutraFormaPagamentoFuncionarioCuidadoExtra
                                            }
                                          </DS.Text>
                                        </div>
                                      </DS.Grid.Item>
                                    </RenderConditional>
                                  </RenderConditional>
                                </RenderConditional>
                              </DS.Grid>
                            </DS.Accordion.Item>
                          </DS.Accordion>
                          <DS.Accordion open>
                            <DS.Accordion.Item
                              title={
                                Constants.TITULO_INFORMACOES_COMPLEMENTARES
                              }
                            >
                              <DS.Grid>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {Constants.INFORMACOES_COMPLEMENTARES}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="textInformacoesComplementares"
                                      style={{ wordBreak: "break-word" }}
                                    >
                                      {formFop.textInformacoesComplementares}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                              </DS.Grid>
                            </DS.Accordion.Item>
                          </DS.Accordion>
                          <DS.Accordion open>
                            <DS.Accordion.Item
                              title={Constants.TITULO_DADOS_RESPONSAVEL}
                            >
                              <DS.Grid>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {enumFop.DadosResponsaveis.nomeCompleto}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="nomeCompleto"
                                    >
                                      {values.nomeCompleto}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {enumFop.DadosResponsaveis.telefone}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="telefone"
                                    >
                                      {values.telefone}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                                <DS.Grid.Item xs={1} lg={1 / 2}>
                                  <div>
                                    <TextLabel variant="body02-sm">
                                      {enumFop.DadosResponsaveis.Email}
                                    </TextLabel>
                                    <DS.Text
                                      variant="body02-sm"
                                      data-testid="email"
                                    >
                                      {values.email}
                                    </DS.Text>
                                  </div>
                                </DS.Grid.Item>
                              </DS.Grid>
                            </DS.Accordion.Item>
                          </DS.Accordion>
                        </DS.Card.Content>
                        <DS.Display justify="center" style={{ marginTop: 20 }}>
                          <DS.Button
                            variant="secondary"
                            onClick={formFop.fecharModal}
                            data-testid="ok"
                          >
                            {enumFop.Botoes.ok}
                          </DS.Button>
                        </DS.Display>
                      </DS.Display>
                    </DS.Modal>
                    <DS.Grid>
                      <DS.Grid.Item xs={1 / 3} lg={1 / 2}>
                        <DS.Display>
                          <DS.Button
                            variant="outlined"
                            type="button"
                            onClick={() => {
                              resetForm()
                              formFop.arquivoAnexoFop.set({
                                isValid: true,
                                errorMsg: '',
                                value: {} as FileList,
                              });
                            }}
                          >
                            {enumFop.Botoes.limpar}
                          </DS.Button>
                          <DS.Button
                            data-testid="fop-visualizar"
                            type="button"
                            onClick={formFop.openModal}
                          >
                            {enumFop.Botoes.visualizar}
                          </DS.Button>
                          <DS.Button
                            data-testid="enviar-fop62"
                            type="submit"
                            onClick={() => {
                              const isArquivoValido = validarArquivoAnexado();
                              if (!isArquivoValido) return;
                            }}
                            loading={loading}
                          >
                            {enumFop.Botoes.enviar}
                          </DS.Button>
                        </DS.Display>
                      </DS.Grid.Item>
                    </DS.Grid>
                  </S.AccordionItem>
                </DS.Accordion>
              </DS.Card.Content>
            </DS.Card>
          </form>
        )}
      </Formik>
    </DS.Display>
  );
};

export default Fop062;
