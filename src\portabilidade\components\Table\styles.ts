import styled from 'styled-components';
import DataTable from 'react-data-table-component';

export const EstiloTable = styled(DataTable)(({ theme: { color, font } }) => ({
  '.rdt_TableCol, .rdt_TableCell': {
    fontSize: font.size.sm,
    fontFamily: font.family.base,
  },
  '.rdt_TableCol': {
    fontWeight: font.weight.lg,
    '&:hover': {
      color: color.brandPrimary.light,
    },
  },
})) as typeof DataTable;

export const Table = styled(EstiloTable)({}) as typeof EstiloTable;

export const TableExtendita = styled(EstiloTable)({}) as typeof DataTable;
