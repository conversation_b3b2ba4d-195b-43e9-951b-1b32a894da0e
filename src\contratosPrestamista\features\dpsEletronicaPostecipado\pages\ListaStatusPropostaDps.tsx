import React, { useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@cvp/design-system/react';
import Table, { setTableType } from 'main/components/Table';
import TableFilters from 'main/components/Table/TableFilter/TableFilter';
import ExpandedComponent from 'contratosPrestamista/features/dpsEletronicaPostecipado/components/ExpandedComponent';
import { useContratosPrestamistaContext } from 'contratosPrestamista/contexts/ContratosPrestamistaContext';
import { ENDPOINT_DPS_ELETRONICA } from 'contratosPrestamista/features/dpseletronica/constants';
import { TIPO_PESSOA_DPS } from 'contratosPrestamista/features/dpsEletronicaPostecipado/constants/MapeamentoStatus';
import { usePecoListarStatusProposta } from 'contratosPrestamista/features/dpsEletronicaPostecipado/hooks/usePecoListarStatusProposta';
import { IRequestListarStatusProposta } from 'contratosPrestamista/features/dpsEletronicaPostecipado/types/IRequestListarStatusProposta';
import { ColunasListaStatusPropostaDps } from 'contratosPrestamista/features/dpsEletronicaPostecipado/constants/ColunasListaStatusPropostaDps';
import {
  IResponseListarStatusPropostaDps,
  IStatusPropostaDpsAgrupadoTabela,
} from 'contratosPrestamista/features/dpsEletronicaPostecipado/types/IResponseListarStatusPropostaDps';
import SkeletonLoading from 'main/components/SkeletonLoading';
import { IFilterOption } from 'main/types/TableFilters/IFilterOption';
import { useLocationPeco } from 'main/hooks/useLocationPeco';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import masks from 'main/utils/masks';

const ListaStatusPropostaDps: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useLocationPeco<IRequestListarStatusProposta>();
  const { setFeatureData } =
    useContratosPrestamistaContext<IRequestListarStatusProposta>();

  const filterOptions: IFilterOption[] = [
    {
      key: 'numCpfCnpjFormatado',
      value: 'CPF/CNPJ',
      type: 'text',
      unmask: masks.numberOnly.mask,
    },
    { key: 'propostaEstip', value: 'Proposta', type: 'text' },
    {
      key: 'dataProposta',
      value: 'Data da Proposta',
      type: 'date',
    },
  ];

  const colunas = useMemo(() => ColunasListaStatusPropostaDps, []);

  const listarStatusPropostaservice = usePecoListarStatusProposta();

  useEffect(() => {
    listarStatusPropostaservice.obterDadosETransformarRetorno(state);
    if (state) {
      setFeatureData(state);
    }
  }, []);

  if (listarStatusPropostaservice.loadingListarStatusProposta)
    return <SkeletonLoading />;

  return (
    <TableFilters
      dataToFilter={listarStatusPropostaservice.resultadoAgrupado}
      filterOptions={filterOptions}
      filterTextPartial
      customButton={
        <Button
          onClick={() => navigate(ENDPOINT_DPS_ELETRONICA.DPS_STATUS_CONSULTA)}
        >
          Nova Consulta
        </Button>
      }
    >
      {dataFiltered => (
        <Table
          highlightOnHover
          striped
          pagination
          paginationPerPage={10}
          paginationRowsPerPageOptions={[10, 20, 30, 50, 100]}
          paginationComponentOptions={{
            rowsPerPageText: 'Items por página',
            rangeSeparatorText: 'de',
          }}
          columns={colunas}
          data={tryGetValueOrDefault(
            [setTableType<IStatusPropostaDpsAgrupadoTabela[]>(dataFiltered)],
            [],
          )}
          expandableRows
          expandableRowDisabled={(row: {
            tipoPessoa: string;
            socios: IResponseListarStatusPropostaDps[];
          }) =>
            row.tipoPessoa === TIPO_PESSOA_DPS.FISICA ||
            tryGetValueOrDefault([row.socios], []).length === 0
          }
          expandableRowsComponent={ExpandedComponent}
          noDataComponent="Não há dados para exibir."
        />
      )}
    </TableFilters>
  );
};

export default ListaStatusPropostaDps;
