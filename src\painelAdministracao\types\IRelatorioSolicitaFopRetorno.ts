import { FormikProps } from 'formik';
import LinkedValue from 'main/features/Validation/types/LinkedValue';
import { IApiResponsePaginadoBFF, IApiResponsePaginado } from 'main/services';
import { ICalendarDateProps } from 'painelAdministracao/types/ICalendarDateProps';
import { IEntradaRelatorioSolicitacao } from 'painelAdministracao/types/IEntradaRelatorioSolicitacao';
import { IRelatorioSolicitacaoFop } from 'painelAdministracao/types/IRelatorioSolicitacaoFop';

export interface IRelatorioSolicitaFopRetorno {
  response: IApiResponsePaginadoBFF<IRelatorioSolicitacaoFop[]> | undefined;
  loading: boolean;
  fetchData: (
    solicitacaoMotivo: IEntradaRelatorioSolicitacao,
  ) => Promise<IApiResponsePaginado<IRelatorioSolicitacaoFop[]>>;
  totalLinhas: number;
  mudarLinhasPorPagina: (
    numeroPorPagina: number,
    pagina: number,
  ) => Promise<void>;
  mudarPagina: (paginaAtual: number) => void;
  fetchMotivos: (paina: number) => Promise<void>;
  formik: FormikProps<{
    filtroGeral: string;
    codigoFop?: number;
    dataInicio: Date | null;
    dataFinal: Date | null;
  }>;
  selectFilter: LinkedValue<string>;
  searchFilterTypeSelected?: string;
  dateLink: LinkedValue<ICalendarDateProps>;
  clearForm: () => void;
  handleDownload: () => void;
}
