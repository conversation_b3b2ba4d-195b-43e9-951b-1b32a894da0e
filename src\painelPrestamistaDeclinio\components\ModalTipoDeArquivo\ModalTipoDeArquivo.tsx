import { Select, Text } from '@cvp/design-system/react';
import Modal from 'main/components/Modal';
import { ButtonPersonalizado } from 'painelPrestamistaDeclinio/components/ModalTipoDeArquivo/styles';

import {
  BUTTON_INFORMAR_TIPO_DE_DADO,
  DATA_SELECT_FORMATOS,
  MODAL_TITULO_EXPORT_DADOS,
} from 'painelPrestamistaDeclinio/constants/constants';
import { IModalProps } from 'painelPrestamistaDeclinio/types/IModalProps';

const ModalTipoDeArquivo: React.FC<IModalProps> = ({
  open,
  handleClose,
  setFormatoArquivo,
  handleChangeFormat,
  loadingExportar,
}) => {
  return (
    <Modal open={open} onClose={handleClose}>
      <Text variant="headline-05" margin>
        {MODAL_TITULO_EXPORT_DADOS}
      </Text>
      <Select
        label=""
        placeholder="Escolha o formato"
        onChange={({
          target: { value },
        }: React.ChangeEvent<HTMLInputElement>) => {
          setFormatoArquivo(Number(value));
        }}
      >
        {DATA_SELECT_FORMATOS.map(item => (
          <Select.Item key={item.value} value={item.value} text={item.text} />
        ))}
      </Select>

      <ButtonPersonalizado
        loading={loadingExportar}
        onClick={handleChangeFormat}
        disabled={loadingExportar}
      >
        {BUTTON_INFORMAR_TIPO_DE_DADO}
      </ButtonPersonalizado>
    </Modal>
  );
};

export default ModalTipoDeArquivo;
