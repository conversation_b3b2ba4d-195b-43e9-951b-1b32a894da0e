import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import MensagemDeclaracaoRegimeTributario from 'previdencia/features/AlteracaoRegimeTributario/components/MensagemDeclaracaoRegimeTributario';
import * as CONSTS from 'previdencia/features/AlteracaoRegimeTributario/constants/constants';
import * as REGIME_TRIBUTARIO_TYPES from 'previdencia/features/AlteracaoRegimeTributario/types/AlteracaoRegimeTributario';

describe('MensagemDeclaracaoRegimeTributario', () => {
  const mockChecarDeclaracao = vi.fn();

  const defaultProps: REGIME_TRIBUTARIO_TYPES.IMensagemDeclaracaoRegimeTributarioProps =
    {
      checarDeclaracao: mockChecarDeclaracao,
    };
  const queryClient = new QueryClient();

  beforeEach(() => {
    render(
      <QueryClientProvider client={queryClient}>
        <RenderPageWithThemeProviderAndRouter>
          <MensagemDeclaracaoRegimeTributario {...defaultProps} />
        </RenderPageWithThemeProviderAndRouter>
      </QueryClientProvider>,
    );
  });

  it('deve renderizar o título com o texto correto', () => {
    const titleElement = screen.getByRole('heading');
    expect(titleElement).toBeInTheDocument();
    expect(titleElement).toHaveTextContent(
      CONSTS.TEXTOS_REGIME_TRIBUTARIO.TITULO_DECLARACAO,
    );
  });

  it('deve renderizar o checkbox', () => {
    const checkboxElement = screen.getByRole('checkbox', { hidden: true });
    expect(checkboxElement).toBeInTheDocument();
  });

  it('deve renderizar o texto associado ao checkbox', () => {
    const checkboxTextElement = screen.getByLabelText('texto-consentimento');
    const textoNormalizadoCheckboxRegex = new RegExp(
      CONSTS.TEXTOS_REGIME_TRIBUTARIO.INFO_CHECKBOX.replace(/\s+/g, ' ').trim(),
    );
    expect(checkboxTextElement).toBeInTheDocument();
    expect(checkboxTextElement).toHaveTextContent(
      textoNormalizadoCheckboxRegex,
    );
  });

  it('deve chamar checarDeclaracao quando o checkbox for clicado', () => {
    const checkboxElement = screen.getByRole('checkbox', { hidden: true });
    fireEvent.click(checkboxElement);
    expect(mockChecarDeclaracao).toHaveBeenCalledTimes(1);
  });
});
