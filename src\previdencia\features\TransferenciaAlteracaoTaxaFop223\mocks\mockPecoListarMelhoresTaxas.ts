import { basePath } from 'config/httpRequest';
import { PECOS } from 'previdencia/config/endpoints';

const { VITE_API_BASE_URL } = import.meta.env;

const defaultPrefixUrl = VITE_API_BASE_URL + basePath;

export default [
  {
    active: false,
    url: defaultPrefixUrl + PECOS.ListarMelhoresTaxas,
    method: 'post',
    data: {
      dados: {
        entidade: {
          familias: [
            {
              familia: 2.0,
              descricao: 'de R$ 10 mil a R$ 49 mil - Tx 2.00',
              efundoDiferenciado: 'N',
            },
            {
              familia: 1.8,
              descricao: 'de R$ 50 mil a R$ 99 mil - Tx 1.80',
              efundoDiferenciado: 'N',
            },
            {
              familia: 1.5,
              descricao: 'de R$ 100 mil a R$ 174 mil - Tx 1.50',
              efundoDiferenciado: 'N',
            },
            {
              familia: 1.3,
              descricao: 'de R$ 174 mil a R$ 249 mil - Tx 1.30',
              efundoDiferenciado: 'N',
            },
            {
              familia: 1.0,
              descricao: 'de R$ 250 mil a R$ 499 mil - Tx 1.00',
              efundoDiferenciado: 'N',
            },
            {
              familia: 0.7,
              descricao: 'de R$ 500 mil a R$ 999 mil - Tx 0.70',
              efundoDiferenciado: 'N',
            },
            {
              familia: 0.6,
              descricao: 'de R$ 1 mi a R$ 2,9 mi - Tx 0.60',
              efundoDiferenciado: 'N',
            },
            {
              familia: 0.5,
              descricao: 'de R$ 3 mi a R$ 5,9 mi - Tx 0.50',
              efundoDiferenciado: 'N',
            },
            {
              familia: 0.4,
              descricao: 'de R$ 6 mi a R$ 9,9 mi - Tx 0.40',
              efundoDiferenciado: 'N',
            },
            {
              familia: 0.3,
              descricao: 'acima de 10 mi - Tx 0.30',
              efundoDiferenciado: 'S',
            },
          ],
          taxaAtual: '2.50%',
        },
        sucesso: true,
        mensagens: [
          {
            codigo: 'INFPSLMT0',
            descricao: 'Melhores taxas listadas com sucesso.',
          },
        ],
      },
    },
  },
];
