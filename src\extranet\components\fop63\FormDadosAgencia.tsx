import { Field } from "formik";
import { TitleSection } from "main/styles/GlobalStyle";
import * as DS from '@cvp/design-system/react';
import * as Enum from '../../types/enum';
import { IFormDadosAgencia } from "extranet/types/InterfacesFop63/IFormDadosAgencia";
import * as S from '../../../extranet/features/fops/pages/styles';
import { FormatarNumerico } from "extranet/hooks/useFormFops";

export const FormDadosAgencia: React.FC<IFormDadosAgencia> = ({
  setFieldValue,
  values,
  handleBlur,
  errors,
  user
}) => {
  return (
    <div>
      <DS.Accordion open>
        <S.AccordionItem
          title={
            <TitleSection>
              {Enum.Titulos.DadosAgencia}
            </TitleSection>}
        >
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="numeroDaAgencia"
              label={Enum.DadosAgencia.NumeroAgencia}
              component={DS.TextField}
              value={String(user.agenciaVinculada)}
              disabled
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1 / 2} lg={1 / 2}>
            <Field
              name="agenciaSr"
              label={Enum.DadosAgencia.SuperintendênciaRegional}
              component={DS.TextField}
              value={FormatarNumerico(values.agenciaSr)}
              error={errors.agenciaSr}
              maxLength={4}
              errorMessage={errors.agenciaSr}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('agenciaSr', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="nomeDaAgencia"
              maxLength={50}
              label={Enum.DadosAgencia.NomeAgência}
              component={DS.TextField}
              value={values.nomeDaAgencia}
              error={errors.nomeDaAgencia}
              errorMessage={errors.nomeDaAgencia}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('nomeDaAgencia', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="agenciaFilial"
              label={Enum.DadosAgencia.Filial}

              component={DS.Select}
              placeholder="Selecione"
              value={values.agenciaFilial}
              error={errors.agenciaFilial}
              errorMessage={errors.agenciaFilial}
              onChange={({
                target: { value },
              }: React.ChangeEvent<{
                text: string;
                value: string;
              }>) => {
                setFieldValue('agenciaFilial', value);
              }}
              onBlur={handleBlur}
            >
              {Enum.SELECT_OPTIONS_FILIAL.map(optFilial => (
                <DS.Select.Item
                  key={optFilial.key}
                  value={optFilial.key}
                  text={optFilial.value}
                  selected={optFilial.key === values.agenciaFilial}
                />
              ))}
            </Field>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1 / 2} lg={1 / 2}>
            <Field
              name="matriculaDoIndicador"
              label={Enum.DadosAgencia.MatrículaIndicador}
              component={DS.TextField}
              maxLength={8}
              value={values.matriculaIndicador}
              error={errors.matriculaIndicador}
              errorMessage={errors.matriculaIndicador}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('matriculaIndicador', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1 / 2}>
            <Field
              name="nomeIndicador"
              maxLength={50}
              label={Enum.DadosAgencia.NomeIndicador}
              component={DS.TextField}
              value={values.nomeIndicador}
              error={errors.nomeIndicador}
              errorMessage={errors.nomeIndicador}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('nomeIndicador', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
        </S.AccordionItem>
      </DS.Accordion>
    </div>)
}
