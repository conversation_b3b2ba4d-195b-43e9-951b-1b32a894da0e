import {
  FilterTypes,
  IFilterOption,
} from 'main/types/TableFilters/IFilterOption';
import masks from 'main/utils/masks';
import { IRangeCalendarioFormatado } from 'main/types/FormFiltroDataProps';
import { SegmentoDetalhesProposta } from 'consultaStatusVendas/features/pesquisaStatusVendas/types/enum';
import { formatarDataFiltrada } from '../utils/formatarDataFiltrada';

export const filterOptions: IFilterOption[] = [
  {
    key: 'cpfCnpj',
    value: 'Nº CPF/CNPJ',
    type: 'text',
    unmask: masks.cpf.unmask,
  },
  {
    key: 'matricula',
    value: 'Indicador',
    type: FilterTypes.TEXT,
  },
  {
    key: 'proposta',
    value: 'Número da Proposta',
    type: FilterTypes.TEXT,
  },
  {
    key: 'nomeSegmento',
    value: 'Segmento',
    type: FilterTypes.TAG,
    checkboxConfig: {
      options: [
        { id: 1, description: SegmentoDetalhesProposta.PREVIDENCIA },
        { id: 2, description: SegmentoDetalhesProposta.PRESTAMISTA },
        { id: 3, description: SegmentoDetalhesProposta.VIDA },
      ],
    },
  },
];

export function requestObterStatusVenda(
  dataInicio?: Date,
  dataFim?: Date,
): IRangeCalendarioFormatado {
  return {
    dataInicial: formatarDataFiltrada(dataInicio ?? new Date()),
    dataFinal: formatarDataFiltrada(dataFim ?? new Date()),
  };
}
