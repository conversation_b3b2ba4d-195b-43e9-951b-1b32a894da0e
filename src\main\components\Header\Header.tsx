import { Display, Hidden, Text } from '@cvp/design-system/react';
import Logo from 'assets/img/caixa_vida_previdencia_2d_vertical_negativa.svg';
import HeaderImage from 'assets/img/header_image.svg';
import { Dropdown } from 'main/components/Dropdown';
import {
  DropdownItem,
  DropdownMenu,
} from 'main/components/Menu/Horizontal/styles';
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

import Matricula from 'assets/icons/document_front.svg?react';
import HomeIcon from 'assets/icons/home.svg?react';
import Avatar from 'assets/icons/user.svg?react';

import { UnderHeaderLineStyle } from 'main/components/Layout/styles';
import { useAuth } from 'main/features/Auth/hooks/useAuth';
import { TEnvironmet } from 'main/types/Environments/EnumEnvironments';
import {
  checkIfAllItemsAreTrue,
  getTernaryResult,
} from 'main/utils/conditional';
import RenderConditional from '../RenderConditional';
import Ribbon from '../Ribbon';
import { ENDPOINT_HEADER } from './const';
import * as S from './styles';

const Header: React.FunctionComponent<{
  showUserData?: boolean;
  children?: React.ReactNode;
}> = ({ children, showUserData = true }) => {
  const [hidden, setHidden] = useState(true);
  const navigate = useNavigate();
  const { logout, user, sessionId } = useAuth();

  const handleLogout = () => {
    logout();
    navigate(ENDPOINT_HEADER.LOGIN);
  };
  const { VITE_ENV } = import.meta.env;

  return (
    <>
      <RenderConditional
        condition={checkIfAllItemsAreTrue([
          !!VITE_ENV,
          VITE_ENV !== 'production',
        ])}
      >
        <Ribbon>{TEnvironmet[VITE_ENV as keyof typeof TEnvironmet]}</Ribbon>
      </RenderConditional>

      <S.HeaderBar>
        <Display alignItems="center" data-testid="header-display-container">
          <Link to="/">
            <img
              src={Logo}
              data-testid="logo-cvp"
              alt="Logo Caixa Vida e Previdência"
              width="180"
              style={{ padding: 10 }}
            />
          </Link>
          <Display.Separator />
          <Hidden only={['xs', 'sm']}>
            <S.HeaderImageStyle src={HeaderImage} alt="Imagem do cabeçalho" />
            <RenderConditional condition={!!user}>
              <S.LoggedUserInfo>
                <Dropdown>
                  <S.Button
                    variant="text-white"
                    onClick={() => setHidden(!hidden)}
                  >
                    <RenderConditional condition={showUserData}>
                      <Avatar /> &nbsp;
                      {user?.nomeUsuario}
                    </RenderConditional>
                  </S.Button>

                  <DropdownMenu
                    fullWidth
                    hidden={hidden}
                    toggle={() => setHidden(!hidden)}
                  >
                    <DropdownItem
                      onClick={() => navigate(ENDPOINT_HEADER.PERFIL)}
                    >
                      Meu perfil
                    </DropdownItem>
                    <DropdownItem onClick={handleLogout}>Sair</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
                <RenderConditional condition={showUserData}>
                  <Text color="white" variant="body02-sm">
                    <Matricula /> Matrícula: {user?.nomeAcesso} | <HomeIcon />{' '}
                    Agência: {user?.agenciaVinculada}
                  </Text>
                  <Text color="white" variant="body02-sm">
                    Sessão: {sessionId}
                  </Text>
                </RenderConditional>
              </S.LoggedUserInfo>
            </RenderConditional>
          </Hidden>
        </Display>
      </S.HeaderBar>
      {getTernaryResult(!showUserData, <UnderHeaderLineStyle />, <></>)}
      {children}
    </>
  );
};

export default Header;
