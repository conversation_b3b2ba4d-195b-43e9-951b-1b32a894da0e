import { cleanup, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ThemeProvider from 'main/components/ThemeProvider';
import ResultadoSinistroProdutos from 'sinistro/features/statusSinistro/components/ResultadoSinistroProdutos';
import mocksStatusSinistroPrevidencia from '../../mocks/mocksStatusSinistroPrevidencia';
import { useObterStatusSinistro } from '../../hooks/useObterStatusSinistro';
import { sinistroProdutosPropsFactory } from '../../factories/sinistroProdutosPropsFactory';
import { RenderPageWithAppContextAndQueryClient } from 'main/utils/testUtils';
import { TipoProduto } from '../../types/enum';
import { normalizarFormatoSeparadorData } from '../../utils/normalizarFormatoSeparadorData';

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(() => ({
    loading: false,
    response: null,
    responseBinary: null,
    error: null,
    fetchData: vi.fn(),
    invocarApiGatewayCvpComToken: vi.fn(),
    invocarApiGatewayCvpComRetornoBinary: vi.fn(),
    setResponse: vi.fn(),
    invalidateCache: vi.fn(),
  })),
}));

afterEach(cleanup);

const { dados } = mocksStatusSinistroPrevidencia[0].data;

vi.mock('../../hooks/useObterStatusSinistro');

vi.mock('/src/assets/icons/document_paper.svg?react', () => ({
  default: () => 'SvgMock',
}));
vi.mock('/src/assets/icons/user.svg?react', () => ({
  default: () => 'SvgMock',
}));
vi.mock('/src/assets/icons/calendar.svg?react', () => ({
  default: () => 'SvgMock',
}));
vi.mock('/src/assets/icons/document_back.svg?react', () => ({
  default: () => 'SvgMock',
}));

describe('<ResultadoSinistroProdutos />', () => {
  const mockProdutoSelecionado = {
    tipoProduto: TipoProduto.PREVIDENCIA,
    descricaoProduto: 'Previdência',
    certificado:
      dados.entidade.dados.andamentosCertificados[0].codigoCertificado,
    numeroAvisoSinistro: String(
      dados.entidade.dados.andamentosCertificados[0].numeroAvisoSinistro,
    ),
    dataSinistro: normalizarFormatoSeparadorData(
      dados.entidade.dados.andamentosCertificados[0].dataSinistro,
    ),
    status: dados.entidade.dados.andamentosCertificados[0].statusAvisoSinitro,
  };

  const mockSetProdutoSelecionado = vi.fn(() => mockProdutoSelecionado);
  const mockExibirSkeleton = vi.fn(() => true);

  const produtos = sinistroProdutosPropsFactory({
    sinistroPrevidencia: dados.entidade.dados as any,
  });

  (useObterStatusSinistro as any).mockReturnValue({
    produtoSelecionado: mockProdutoSelecionado,
    setProdutoSelecionado: mockSetProdutoSelecionado,
    responseStatusSinistroPrevidencia: dados.entidade,
    exibirSkeleton: mockExibirSkeleton,
  });

  const componentePadrao = () => {
    return render(
      <ThemeProvider>
        <RenderPageWithAppContextAndQueryClient>
          <ResultadoSinistroProdutos
            produtoSelecionado={mockProdutoSelecionado}
            setProdutoSelecionado={mockSetProdutoSelecionado}
            produtos={produtos}
          />
        </RenderPageWithAppContextAndQueryClient>
      </ThemeProvider>,
    );
  };

  it('deverá exibir todos os elementos do card de produtos de sinistro', () => {
    componentePadrao();

    const descricaoProduto = screen.queryAllByText(
      produtos[0].descricaoProduto,
    )[0];
    const codigoCertificado = screen.queryAllByText(
      produtos[0].codigoCertificado,
    )[0];
    const numeroAvisoSinistro = screen.queryAllByText(
      String(produtos[0].numeroAvisoSinistro),
    )[0];
    const dataSinistro = screen.queryAllByText(
      String(produtos[0].dataSinistro),
    )[0];
    const status = screen.queryAllByText(String(produtos[0].status))[0];
    const btn = screen.queryAllByTestId('btnDetalhesSinistro')[0];

    expect(descricaoProduto).toBeInTheDocument();
    expect(codigoCertificado).toBeInTheDocument();
    expect(numeroAvisoSinistro).toBeInTheDocument();
    expect(dataSinistro).toBeInTheDocument();
    expect(status).toBeInTheDocument();
    expect(btn).toBeInTheDocument();
  });

  it('deverá chamar a função mockSetProdutoSelecionado ao clicar no botão Detalhes do Sinistro', () => {
    componentePadrao();

    const btn = screen.queryAllByTestId('btnDetalhesSinistro')[0];

    userEvent.click(btn);
    expect(mockSetProdutoSelecionado).toHaveBeenCalled();
  });
});
