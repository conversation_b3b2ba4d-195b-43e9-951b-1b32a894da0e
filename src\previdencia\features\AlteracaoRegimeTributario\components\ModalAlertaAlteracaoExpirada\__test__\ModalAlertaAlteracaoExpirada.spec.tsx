import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import ModalAlertaAlteracaoExpirada from 'previdencia/features/AlteracaoRegimeTributario/components/ModalAlertaAlteracaoExpirada';

vi.mock('/src/assets/icons/rounded-warning.svg?react', () => ({
  default: 'SvgMock',
}));

describe('ModalAlertaAlteracaoExpirada', () => {
  const mensagem = 'Sua alteração expirou. Por favor, tente novamente.';
  const onCloseMock = vi.fn();

  beforeEach(() => {
    onCloseMock.mockClear();
  });
  const queryClient = new QueryClient();

  it('deve renderizar o modal com a mensagem de alteração expirada', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <RenderPageWithThemeProviderAndRouter>
          <ModalAlertaAlteracaoExpirada
            open
            onClose={onCloseMock}
            mensagem={mensagem}
          />
        </RenderPageWithThemeProviderAndRouter>
      </QueryClientProvider>,
    );

    const heading = screen.getByRole('heading');
    const mensagemAlerta = screen.getByRole('document');
    const botao = screen.getByRole('button', { name: /Certo, entendi!/i });

    expect(heading).toBeInTheDocument();
    expect(mensagemAlerta).toBeInTheDocument();
    expect(botao).toBeInTheDocument();
  });

  it('deve chamar onClose ao clicar no botão', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <RenderPageWithThemeProviderAndRouter>
          <ModalAlertaAlteracaoExpirada
            open
            onClose={onCloseMock}
            mensagem={mensagem}
          />
        </RenderPageWithThemeProviderAndRouter>
      </QueryClientProvider>,
    );

    const botao = screen.getByRole('button', { name: /Certo, entendi!/i });
    fireEvent.click(botao);

    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });
});
