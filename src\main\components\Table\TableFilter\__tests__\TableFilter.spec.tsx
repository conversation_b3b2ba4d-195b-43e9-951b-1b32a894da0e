import { render, screen, waitFor } from '@testing-library/react';
import user from '@testing-library/user-event';
import { renderHook } from '@testing-library/react-hooks';
import { IFilterableEntity } from 'main/types/IFilterableEntity';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import TableFilter, {
  useTableFilters,
} from 'main/components/Table/TableFilter';
import { IFilterOption } from 'main/types/TableFilters/IFilterOption';

describe('TableFilter.tsx', () => {
  it('deve certificar se todos os componentes básicos carregaram na tela', () => {
    const dados: IFilterableEntity[] = [
      {
        numeroProposta: '42286110000805567',
        dataHoraEmissaoDaProposta: '2021-08-10T00:00:00',
      },
      {
        numeroProposta: '42286110000803785',
        dataHoraEmissaoDaProposta: '2021-07-26T00:00:00',
      },
      {
        numeroProposta: '42286110000805648',
        dataHoraEmissaoDaProposta: '2021-08-11T00:00:00',
      },
    ];

    render(
      <RenderPageWithThemeProviderAndRouter>
        <TableFilter dataToFilter={dados}>
          <div />
        </TableFilter>
      </RenderPageWithThemeProviderAndRouter>,
    );

    const botaoFiltrar = screen.getByTestId('botao-filtrar');
    const inputPesquisa = screen.getByTestId('input-pesquisa');

    expect(botaoFiltrar).toBeInTheDocument();
    expect(inputPesquisa).toBeInTheDocument();
  });

  it('deve garantir que os select de opções de filtros e o input carregaram na tela', () => {
    const dados: IFilterableEntity[] = [
      {
        numeroProposta: '42286110000805567',
        dataHoraEmissaoDaProposta: '2021-08-10T00:00:00',
      },
      {
        numeroProposta: '42286110000803785',
        dataHoraEmissaoDaProposta: '2021-07-26T00:00:00',
      },
      {
        numeroProposta: '42286110000805648',
        dataHoraEmissaoDaProposta: '2021-08-11T00:00:00',
      },
    ];
    const filterOptions: IFilterOption[] = [
      { key: 'numeroProposta', value: 'Nº proposta', type: 'text' },
      { key: 'dataHoraEmissaoDaProposta', value: 'Data emissão', type: 'date' },
    ];

    render(
      <RenderPageWithThemeProviderAndRouter>
        <TableFilter dataToFilter={dados} filterOptions={filterOptions}>
          <div />
        </TableFilter>
      </RenderPageWithThemeProviderAndRouter>,
    );

    const botaoFiltrar = screen.getByTestId('botao-filtrar');
    const inputPesquisa = screen.getByTestId('input-pesquisa');
    const selectOpcoes = screen.getByTestId('select-options');

    expect(botaoFiltrar).toBeInTheDocument();
    expect(inputPesquisa).toBeInTheDocument();
    expect(selectOpcoes).toBeInTheDocument();
  });

  it('deve certificar que o componente da calendario apareceu', async () => {
    const dados: IFilterableEntity[] = [
      {
        numeroProposta: '42286110000805567',
        dataHoraEmissaoDaProposta: '2021-08-10T00:00:00',
      },
      {
        numeroProposta: '42286110000803785',
        dataHoraEmissaoDaProposta: '2021-07-26T00:00:00',
      },
      {
        numeroProposta: '42286110000805648',
        dataHoraEmissaoDaProposta: '2021-08-11T00:00:00',
      },
    ];
    const filterOptions: IFilterOption[] = [
      { key: 'numeroProposta', value: 'Nº proposta', type: 'text' },
      { key: 'dataHoraEmissaoDaProposta', value: 'Data emissão', type: 'date' },
    ];

    renderHook(() => useTableFilters({ initialData: dados, filterOptions }));

    render(
      <RenderPageWithThemeProviderAndRouter>
        <TableFilter dataToFilter={dados} filterOptions={filterOptions}>
          <div />
        </TableFilter>
      </RenderPageWithThemeProviderAndRouter>,
    );
    const selectOpcoes = screen.getByTestId('select-options');

    user.click(selectOpcoes);

    waitFor(
      () => {
        const optionDate = screen.getByTestId(
          'option-dataHoraEmissaoDaProposta',
        );
        user.click(optionDate);
      },
      {
        timeout: 500,
      },
    );

    waitFor(
      () => {
        const calendarioPesquisa = screen.getByTestId('calendario-pesquisa');

        expect(calendarioPesquisa).toBeInTheDocument();
      },
      {
        timeout: 500,
      },
    );
  });
});
