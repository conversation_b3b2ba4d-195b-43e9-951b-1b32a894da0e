import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import * as path from 'path';
import svgr from 'vite-plugin-svgr';

export default defineConfig({
  plugins: [react(), svgr()],

  base: '/',
  define: {
    process: {
      env: {},
    },
  },

  build: {
    chunkSizeWarningLimit: 5000,
    minify: true,
    outDir: './build',
    rollupOptions: {
      input: 'index.html',
      external: id => {
        // Durante o build, resolve o pacote problemático
        if (id === '@cvp/componentes-posvenda') {
          return false; // Não trate como externo, deixe o Vite resolver
        }
        return false;
      },
    },
    commonjsOptions: {
      include: [/node_modules/],
    },
  },
  server: {
    host: true,
  },

  optimizeDeps: {
    include: ['@cvp/componentes-posvenda'],
  },

  resolve: {
    alias: {
      '@cvp/componentes-posvenda': path.resolve(
        __dirname,
        'node_modules/@cvp/componentes-posvenda/index.js',
      ),
      '@src': path.resolve(__dirname, 'src'),
      assets: path.resolve(__dirname, 'src/assets'),
      atendimento: path.resolve(__dirname, 'src/atendimento'),
      config: path.resolve(__dirname, 'src/config'),
      consultaCliente: path.resolve(__dirname, 'src/consultaCliente'),
      consultaStatusVendas: path.resolve(__dirname, 'src/consultaStatusVendas'),
      contratosPrestamista: path.resolve(__dirname, 'src/contratosPrestamista'),
      dps: path.resolve(__dirname, 'src/dps'),
      evolucaoPatrimonial: path.resolve(__dirname, 'src/evolucaoPatrimonial'),
      extranet: path.resolve(__dirname, 'src/extranet'),
      ferramentas: path.resolve(__dirname, 'src/ferramentas'),
      main: path.resolve(__dirname, 'src/main'),
      painelAdministracao: path.resolve(__dirname, 'src/painelAdministracao'),
      painelDPS: path.resolve(__dirname, 'src/painelDPS'),
      painelInadimplencia: path.resolve(__dirname, 'src/painelInadimplencia'),
      painelPortabilidade: path.resolve(__dirname, 'src/painelPortabilidade'),
      painelPrestamista: path.resolve(__dirname, 'src/painelPrestamista'),
      painelPrestamistaDeclinio: path.resolve(
        __dirname,
        'src/painelPrestamistaDeclinio',
      ),
      painelResgate: path.resolve(__dirname, 'src/painelResgate'),
      painelVidaPu: path.resolve(__dirname, 'src/painelVidaPu'),
      portabilidade: path.resolve(__dirname, 'src/portabilidade'),
      prestamista: path.resolve(__dirname, 'src/prestamista'),
      previdencia: path.resolve(__dirname, 'src/previdencia'),
      propostasVida: path.resolve(__dirname, 'src/propostasVida'),
      registroOcorrenciaASC: path.resolve(
        __dirname,
        'src/registroOcorrenciaASC',
      ),
      relatorios: path.resolve(__dirname, 'src/relatorios'),
      reter: path.resolve(__dirname, 'src/reter'),
      seguros: path.resolve(__dirname, 'src/seguros'),
      sinistro: path.resolve(__dirname, 'src/sinistro'),
      vida: path.resolve(__dirname, 'src/vida'),
      routes: path.resolve(__dirname, 'src/routes'),
    },
  },
});
