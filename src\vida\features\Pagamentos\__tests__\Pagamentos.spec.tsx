import { cleanup, render, screen, waitFor } from '@testing-library/react';
import faker from 'faker';
import { api } from 'main/services/api';
import {
  RenderPageWithAppContextAndQueryClient,
  RenderPageWithThemeProviderAndRouter,
} from 'main/utils/testUtils';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';

import Pagamentos from 'vida/features/Pagamentos/pages/Pagamentos';
import { IResponseHistoricoPagamentosTableProps } from 'vida/features/Pagamentos/types/HistoricoPagamentosTypes';

vi.mock('/src/assets/icons/rounded-warning.svg?react', () => ({
  default: 'SvgMock',
}));

vi.mock('/src/assets/icons/user.svg?react', () => ({
  default: 'SvgMock',
}));

vi.mock('/src/assets/icons/document_front.svg?react', () => ({
  default: 'SvgMock',
}));
vi.mock('/src/assets/icons/document_paper.svg?react', () => ({
  default: 'SvgMock',
}));
vi.mock('/src/assets/icons/document_back.svg?react', () => ({
  default: 'SvgMock',
}));
vi.mock('/src/assets/icons/calendar.svg?react', () => ({
  default: 'SvgMock',
}));
vi.mock('/src/assets/icons/information.svg?react', () => ({
  default: 'SvgMock',
}));

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

afterAll(() => {
  vi.clearAllMocks();
  cleanup();
});

const mockApiObterInformacoesPagamentoSeguroVida = () => {
  const fakeHistorico: IResponseHistoricoPagamentosTableProps = {
    numeroParcela: faker.datatype.number({ min: 2, max: 20 }),
    situacao: faker.lorem.word(),
    opcaoPagamento: faker.lorem.word(),
    vencimento: faker.datatype.datetime().toLocaleDateString(),
    valor: faker.datatype.float({ min: 50, max: 200, precision: 2 }).toString(),
  };

  const mockResponse = {
    sucessoBFF: true,
    entidade: [fakeHistorico],
    mensagens: [],
  };

  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

  // Mock do useApiGatewayCvpInvoker
  mockUseApiGatewayCvpInvoker.mockReturnValue({
    loading: false,
    response: mockResponse,
    responseBinary: null,
    invocarApiGatewayCvpComToken: vi.fn().mockResolvedValue(mockResponse),
    invocarApiGatewayCvpComRetornoBinary: vi.fn(),
    setResponse: vi.fn(),
    invalidateCache: vi.fn(),
  });

  return { fakeHistorico };
};

describe('<Pagamentos />', () => {
  it('Deve renderizar tabela', async () => {
    const { fakeHistorico } = mockApiObterInformacoesPagamentoSeguroVida();
    render(
      <RenderPageWithAppContextAndQueryClient>
        <ComponentesPosVendaProvider>
          <ApiGatewayCvpProvider
            configure={{
              defaultAxiosClient: api,
              authMode: 'DEFAULT_GI',
              usernameKey: '@portal-eco:nomeAcesso',
              storageMode: 'LOCAL',
              operationPath: 'PortalEconomiario/',
            }}
          >
            <RenderPageWithThemeProviderAndRouter>
              <Pagamentos />
            </RenderPageWithThemeProviderAndRouter>
          </ApiGatewayCvpProvider>
        </ComponentesPosVendaProvider>
      </RenderPageWithAppContextAndQueryClient>,
    );

    const { numeroParcela, situacao, opcaoPagamento } = fakeHistorico;

    await waitFor(() => {
      [numeroParcela, situacao, opcaoPagamento].forEach(item =>
        expect(screen.queryAllByText(item)[0]).toBeInTheDocument(),
      );
      expect(screen.getByTestId('botao-voltar')).toBeInTheDocument();
    });
  });
});
