import { Grid, Accordion, Card, Skeleton } from '@cvp/design-system/react';
import RenderConditional from 'main/components/RenderConditional';
import SkeletonLoading from 'main/components/SkeletonLoading';
import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
} from 'main/utils/conditional';
import { useRegistrarOcorrenciaContext } from 'registroOcorrenciaASC/features/registrarOcorrencia/contexts/RegistrarOcorrenciaContext';
import FormDadosOcorrencia from 'registroOcorrenciaASC/features/registrarOcorrencia/components/FormDadosOcorrencia';
import { useRenderizarCardOcorrencia } from 'registroOcorrenciaASC/features/registrarOcorrencia/hooks';
import * as CONSTS from 'registroOcorrenciaASC/features/registrarOcorrencia/constants/constants';
import * as S from 'registroOcorrenciaASC/features/registrarOcorrencia/components/CardDadosOcorrencia/styles';
import SelectSearch from 'main/components/SelectSearch';
import { CardContainer } from 'registroOcorrenciaASC/styles/styles';

const CardDadosOcorrencia = (): React.ReactElement => {
  const { formDadosCliente } = useRegistrarOcorrenciaContext();

  const {
    assuntoOcorrencia,
    setAssuntoOcorrencia,
    setMacroassuntoOcorrencia,
    setProdutoOcorrencia,
    dadosConsultaAssunto,
    loadingDadosConsultaAssunto,
    produtoOcorrencia,
    macroassuntoOcorrencia,
    dadosConsultaSubAssunto,
    loadingDadosConsultaSubAssunto,
    validaOpcoesOcorrencia,
  } = useRenderizarCardOcorrencia();

  const listaProduto = CONSTS.LISTA_PRODUTO.map(item => ({
    value: item.id,
    label: item.produto,
  }));

  const produtoSelecionado = listaProduto.find(
    item => item.value === produtoOcorrencia,
  );

  const listaMacroassunto = dadosConsultaAssunto?.entidade?.assuntos?.map(
    item => ({
      value: item.codigoAssunto,
      label: item.rotulo,
    }),
  );

  const macroassuntoSelecionado = listaMacroassunto?.find(
    item => item.value === macroassuntoOcorrencia,
  );

  const listaAssunto = dadosConsultaSubAssunto?.entidade?.assuntos?.map(
    item => ({
      value: item.codigoAdminAssunto,
      label: item.rotulo,
    }),
  );

  const assuntoSelecionado = listaAssunto?.find(
    item => item.value === assuntoOcorrencia,
  );

  return (
    <RenderConditional
      condition={checkIfSomeItemsAreTrue([
        formDadosCliente.camposObrigatorios.pessoaFisica,
        formDadosCliente.camposObrigatorios.pessoaJuridica,
      ])}
    >
      <CardContainer>
        <Card.Content>
          <Accordion open data-testid="accordionDadosOcorrencia">
            <S.AccordionItem title={CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.TITULO}>
              <S.SelectGridWrapper spacingFooter={!validaOpcoesOcorrencia}>
                <Grid.Item xs={1} lg={5 / 10} xl={3 / 10}>
                  <SelectSearch
                    label={CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.PRODUTO.LABEL}
                    tooltip={CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.PRODUTO.TOOLTIP}
                    placeholder={
                      CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.PRODUTO.PLACEHOLDER
                    }
                    data-testid="inputSelectProdutoOcorrencia"
                    options={listaProduto}
                    selectedItem={produtoSelecionado}
                    setSelectedItem={item => setProdutoOcorrencia(item.value)}
                  />
                </Grid.Item>

                <Grid.Item xs={1} lg={5 / 10} xl={4 / 10}>
                  <RenderConditional condition={loadingDadosConsultaAssunto}>
                    <div style={{ maxWidth: '100px', marginTop: '-8px' }}>
                      <Skeleton lines={1} />
                    </div>
                    <SkeletonLoading blocks={1} lines={1} />
                  </RenderConditional>

                  <RenderConditional
                    condition={checkIfAllItemsAreTrue([
                      !loadingDadosConsultaAssunto,
                      !!dadosConsultaAssunto?.entidade,
                      !!produtoSelecionado,
                    ])}
                  >
                    <SelectSearch
                      label={
                        CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.MACROASSUNTO.LABEL
                      }
                      tooltip={
                        CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.MACROASSUNTO.TOOLTIP
                      }
                      placeholder={
                        CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.MACROASSUNTO
                          .PLACEHOLDER
                      }
                      data-testid="inputSelectMacroassuntoOcorrencia"
                      options={listaMacroassunto}
                      selectedItem={macroassuntoSelecionado}
                      setSelectedItem={item =>
                        setMacroassuntoOcorrencia(item.value)
                      }
                    />
                  </RenderConditional>
                </Grid.Item>

                <Grid.Item xs={1} lg={5 / 10} xl={3 / 10}>
                  <RenderConditional condition={loadingDadosConsultaSubAssunto}>
                    <div style={{ maxWidth: '100px', marginTop: '-8px' }}>
                      <Skeleton lines={1} />
                    </div>
                    <SkeletonLoading blocks={1} lines={1} />
                  </RenderConditional>

                  <RenderConditional
                    condition={checkIfAllItemsAreTrue([
                      !loadingDadosConsultaSubAssunto,
                      !!dadosConsultaSubAssunto?.entidade,
                      !!produtoSelecionado,
                      !!macroassuntoSelecionado,
                    ])}
                  >
                    <SelectSearch
                      label={CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.ASSUNTO.LABEL}
                      tooltip={
                        CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.ASSUNTO.TOOLTIP
                      }
                      placeholder={
                        CONSTS.CARD_DADOS_OCORRENCIA_TEXTS.ASSUNTO.PLACEHOLDER
                      }
                      data-testid="inputSelectAssuntoOcorrencia"
                      options={listaAssunto}
                      selectedItem={assuntoSelecionado}
                      setSelectedItem={item => setAssuntoOcorrencia(item.value)}
                    />
                  </RenderConditional>
                </Grid.Item>
              </S.SelectGridWrapper>

              <RenderConditional
                condition={checkIfAllItemsAreTrue([
                  !!dadosConsultaSubAssunto?.entidade,
                  validaOpcoesOcorrencia,
                ])}
              >
                <FormDadosOcorrencia />
              </RenderConditional>
            </S.AccordionItem>
          </Accordion>
        </Card.Content>
      </CardContainer>
    </RenderConditional>
  );
};

export default CardDadosOcorrencia;
