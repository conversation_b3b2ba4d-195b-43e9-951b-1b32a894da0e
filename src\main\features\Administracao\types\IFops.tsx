import { TPayloadRegistrarSolicitacaoFop } from 'extranet/types/RegistrarSolicitacao';
import { IUser } from 'main/features/Auth/interfaces/IUser';
import LinkedValue from 'main/features/Validation/types/LinkedValue';
import ValidationFunction from 'main/features/Validation/types/ValidationFunction';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import { IRequestGIBase } from 'main/types/IRequestGIBase';

export type RequestListarFops = IRequestGIBase;

export type DadosArquivo = {
  nomeArquivo?: string;
  indicadorUsoDoArquivo?: string;
  codigoIdentificadorUnico?: string;
  dataHoraVersao?: Date;
  dataUltimaAlteracao?: string;
  descricaoTipoArquivo?: string;
};

export interface IResponseObterListaFopsAtivos {
  codigo?: number;
  numeroVersao?: number;
  tipo?: string;
  nome?: string;
  dataUltimaAlteracao?: string;
  dataVersao?: string;
  motivoSolicitacaoAtivo?: boolean;
  descricaoMotivoSolicitacao?: string;
  dadosArquivos?: DadosArquivo[];
}

export type TListaFopsProps = {
  tipoFop?: string;
  dataToList: Array<IResponseObterListaFopsAtivos> | undefined;
  fopAtivo?: IResponseObterListaFopsAtivos;
  abrirFop: (fop: IResponseObterListaFopsAtivos) => void;
  baixarArquivoFop: (fop?: IResponseObterListaFopsAtivos) => void;
};

export interface IArquivoDownload {
  sucesso: true;
  mensagem: string;
  stackTrace: string;
  dados: string;
}

export interface IUseFormFops {
  openMotivo?: IResponseObterListaFopsAtivos;
  handleFopAtivo: (value?: IResponseObterListaFopsAtivos) => void;
  handleOpenMotivo: (value?: IResponseObterListaFopsAtivos) => void;
  openSelectTipoBenficio: boolean;
  openSelectReversivel: boolean;
  openSelectPlanoInstituido: boolean;
  openSelectValorContribuicao: boolean;
  openSelectValorContribuicaoCuidadoExtra: boolean;
  openSelectValorFixo: boolean;
  openSelectValorFixoCuidadoExtra: boolean;
  openSelectValorFixoEmpresa: boolean;
  openSelectValorFixoEmpresaCuidadoExtra: boolean;
  openSelectValorFixoFuncionario: boolean;
  openSelectValorPercentualCuidadoExtra: boolean;
  openSelectValorFixoFuncionarioCuidadoExtra: boolean;
  openSelectValorPercentual: boolean;
  openSelectValorPercentualEmpresa: boolean;
  openSelectValorPercentualEmpresaCuidadoExtra: boolean;
  openSelectValorPercentualFuncionario: boolean;
  openSelectValorPercentualFuncionarioCuidadoExtra: boolean;
  openSelectOutraFormaPagamento: boolean;
  openSelectPlanoInstituidoCuidadoExtra: boolean;
  openSelectPrazoBenficio: boolean;
  openSelectOutraFormaPagamentoCuidadoExtra: boolean;
  openSelectValoresParticipantes: boolean;
  openSelectPeculio: boolean;
  openSelectPensao: boolean;
  openSelectFormaPagamentoCuidado: boolean;
  openSelectOutraFormaPagamentoEmpresa: boolean;
  openSelectOutraFormaPagamentoEmpresaCuidadoExtra: boolean;
  openSelectOutraFormaPagamentoFuncionario: boolean;
  openSelectOutraFormaPagamentoFuncionarioCuidadoExtra: boolean;
  openSelectTipoModalidade: boolean;
  openSelectTipoModalidadePGBL: boolean;
  openSelectTipoModalidadeVGBL: boolean;
  openSelectLiberacaoReserva: boolean;
  openSelectSugestaoLiberacao: boolean;
  openSelectOutraFormaLiberacao: boolean;
  openSelectTipoAporte: boolean;
  openSelectPortabilidade: boolean;
  openSelectComAporte: boolean;
  openSelectIdadeAposentadoria: boolean;
  openSelectPrazoContribuicao: boolean;
  openSelectAporteUnico: boolean;
  openSelectDiaVencimento: boolean;
  openSelectDebitoEmConta: boolean;
  loadingDownload: boolean;
  loadingDownloadArquivo: boolean;
  setLoadingDownloadArquivo: React.Dispatch<React.SetStateAction<boolean>>;
  loadingFops: boolean;
  responseFops?: IHandleReponseResult<IResponseObterListaFopsAtivos[]>;
  textOutraFormaPagamento: string;
  textOutraFormaPagamentoEmpresa: string;
  textOutraFormaPgEmpresa: string;
  textOutraFormaPagamentoEmpresaCuidadoExtra: string;
  textOutraFormaPagamentoFuncionario: string;
  textOutraRegraLiberacaoDaReserva: string;
  textOutraFormaPagamentoFuncionarioCuidadoExtra: string;
  textOutraFormaPagamentoCuidadoExtra: string;
  textInformacoesComplementares: string;
  openModalFormulario: boolean;
  arquivoAnexoFop: LinkedValue<FileList>;
  arquivoAnexoFop63: LinkedValue<FileList>;
  regraFiles: ValidationFunction<FileList>[];
  baixarArquivoFop: (
    codigoIdentificadorUnico?: string,
    nomeArquivo?: string,
  ) => Promise<string | number>;
  fecharModal: () => void;
  setTextInformacoesComplementares: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamentoEmpresa: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPgEmpresa: React.Dispatch<React.SetStateAction<string>>;
  setTextOutraFormaPagamentoCuidadoExtra: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraRegraLiberacaoDaReserva: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamentoFuncionarioCuidadoExtra: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamentoFuncionario: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamentoEmpresaCuidadoExtra: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamento: React.Dispatch<React.SetStateAction<string>>;
  toastSuccess: (
    message?: string,
    onClose?: (props: unknown) => void,
  ) => string | number;
  selectRegraCalculo: (valor: string) => void;
  selectFormaPagamento: (valor: string) => void;
  selectFormaPagamentoCuidado: (valor: string) => void;
  selectBeneficio: (valor: string) => void;
  selectValoresParticipantes: (valor: string) => void;
  selectValorContribuicao: (valor: string) => void;
  selectValorContribuicaoCuidadoExtra: (valor: string) => void;
  selectCuidadoExtra: (valor: string) => void;
  selectValorContribuicaoEmpresa: (valor: string) => void;
  selectValorContribuicaoEmpresaCuidadoExtra: (valor: string) => void;
  selectValorContribuicaoFuncionario: (valor: string) => void;
  selectValorContribuicaoFuncionarioCuidadoExtra: (valor: string) => void;
  selectFormaPagamentoFop63: (valor: string) => void;
  selecLiberacaoReserva: (valor: string) => void;
  selectRegraAporte: (valor: string) => void;
  selectTipoConcessao: (valor: string) => void;
  selecTipoPagamentoFatura: (valor: string) => void;
  selectDadosCobranca: (valor: string) => void;
  selectRegraCalculoFOP62: (valor: string) => void;
  obterArquivoFOP: (
    codigoIdentificadorUnico: string,
    nomeArquivo: string,
  ) => Promise<IArquivoDownload | undefined>;
  openModal: () => void;
  listarFopsProps: Omit<TListaFopsProps, 'tipoFop'>;
  openSelectFormaPagamentoRegra: boolean;

  openFieldRecursoInstituidora: boolean;
  openSelectRegraPagamentoCusteio: boolean;
  selectFormaPagamentoRegra: (valor: string) => void;
  openFieldInstituidoraValor: (penalidade: string) => void;
  setselectFormaPagamentoCusteio: (FormaCusteioModalidadePlano: string) => void;


}

export interface IUseFormFopsPrevidencia {
  openMotivo?: IResponseObterListaFopsAtivos;
  handleOpenMotivo: (value?: IResponseObterListaFopsAtivos) => void;
  loadingFops: boolean;
  goToFOP63: () => void;
  goToFOP62: () => void;
  listarFopsProps: Omit<TListaFopsProps, 'tipoFop'>;
}

export type TModalFopProps = {
  open?: boolean;
  src?: string;
  onClose?: VoidFunction;
  onConfirm?: VoidFunction;
};

export enum TIPOS_SEGMENTO {
  PREVIDENCIA = 'PREVIDENCIA',
  VIDA = 'VIDA',
  PRESTAMISTA = 'PRESTAMISTA',
}

export type TMotivo = {
  matriculaResponsavel: string;
  descricaoMotivo: string;
  segmento: TIPOS_SEGMENTO;
};

export type TModalMotivoFop = {
  fop?: IResponseObterListaFopsAtivos;
  segmento: TIPOS_SEGMENTO;
  onClose: (value?: IResponseObterListaFopsAtivos) => void;
  onConfirm: (item?: IResponseObterListaFopsAtivos) => void;
};

export type TMotivoInitialValues = {
  descricaoMotivo: string;
};

export type TSubmitMotivo = {
  values: TMotivoInitialValues;
  user: IUser;
  segmento: TIPOS_SEGMENTO;
  fop?: IResponseObterListaFopsAtivos;
  salvarMotivo: (
    payload: TPayloadRegistrarSolicitacaoFop,
  ) => Promise<IHandleReponseResult<unknown> | undefined>;
  onConfirm: (item?: IResponseObterListaFopsAtivos) => void;
  resetMotivo: VoidFunction;
};
