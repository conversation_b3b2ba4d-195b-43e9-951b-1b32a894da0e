import React from 'react';
import { Text } from '@cvp/design-system/react';
import { LinkFop } from 'main/styles/GlobalStyle';
import masks from 'main/utils/masks';
import {
  IResponseObterListaFopsAtivos,
  TListaFopsProps,
} from 'main/features/Administracao/types/IFops';
import { ModalBannerFop } from './ModalBannerFop';
import Fop59 from '../../img/card_fop_059.jpg';
import { useListarFops } from 'extranet/hooks/useListarFops';

const ListarFops: React.FC<TListaFopsProps> = ({
  tipoFop,
  dataToList,
  fopAtivo,
  abrirFop,
  baixarArquivoFop,
}) => {
  const { listaFops, handleFopClick } = useListarFops({
    tipoFop,
    dataToList,
    abrirFop,
  });

  return (
    <>
      <ModalBannerFop
        open={!!fopAtivo}
        onClose={() => {
          baixarArquivoFop(fopAtivo);
        }}
        src={Fop59}
      />

      {listaFops.map((item: IResponseObterListaFopsAtivos) => (
        <Text variant="body01-sm" color="primary" margin key={item.codigo}>
          <LinkFop
            variant="text"
            key={item.codigo}
            onClick={() => handleFopClick(item)}
          >
            <b>
              FOP {`000${item.codigo}`.slice(-3)}{' '}
              {`00${item.numeroVersao}`.slice(-2)}{' '}
            </b>
            - {masks.dateFop.mask(item.dataVersao)} - {item.nome}
          </LinkFop>
        </Text>
      ))}
    </>
  );
};

export default ListarFops;
