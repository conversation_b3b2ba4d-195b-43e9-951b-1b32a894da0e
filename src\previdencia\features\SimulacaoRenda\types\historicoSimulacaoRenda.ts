export type ResponseHistoricoSimulacaoRenda = THistoricoSimulacaoRenda[];

export type THistoricoSimulacaoRenda = {
  nomTipoPagamento: string | React.ReactElement;
  vlrReserva: number;
  vlrIRRF: number;
  vlrBeneficio: number;
  vlrBeneficioLiquido: number | string;
  dthDiaSimulacaoFormatada: string;
  dthHoraSimulacaoFormatada: string;
  seqSimulacao: string;
  descBeneficiarioRecebe: string;
  descPeridoBeneficiarioRecebe: string;
  tipoTributacao: string;
};
