import React from 'react';
import { Text, Divider } from '@cvp/design-system/react';
import Table from 'main/components/Table';
import { tryGetMonetaryValueOrDefault } from 'main/utils/money';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import { ResultadoSolicitacaoTabelasProps } from 'previdencia/features/AlterarValorContribuicao/types/AlterarValorContribuicaoComponentProps';
import { COLUNAS_TABELA_COMPROVANTE_CONFIRMACAO_VALOR_CONTRIBUICAO } from 'previdencia/features/AlterarValorContribuicao/constants/constants';
import { Fundo } from 'previdencia/types/Fundo.type';

const ResultadoSolicitacaoTabelas: React.FC<
  ResultadoSolicitacaoTabelasProps
> = ({ fundosAtualizados, fundosAntigos }) => {
  const tabelaFundosAntigos = (): Fundo[] => {
    return fundosAntigos?.map(fundo => ({
      ...fundo,
      valorContribuicao: tryGetMonetaryValueOrDefault(fundo.contribuicaoAtual),
    }));
  };

  const tabelaFundosAtualizados = (): Fundo[] => {
    return fundosAtualizados?.map(fundo => ({
      ...fundo,
      valorContribuicao: tryGetMonetaryValueOrDefault(fundo.valor),
    }));
  };

  return (
    <>
      <Text variant="body01-sm" fontWeight="bold" marginBottom={5}>
        Como era:
      </Text>
      <Table
        data={tryGetValueOrDefault([tabelaFundosAntigos()], [])}
        columns={COLUNAS_TABELA_COMPROVANTE_CONFIRMACAO_VALOR_CONTRIBUICAO}
        noDataComponent="Não há dados para exibir."
        striped
        highlightOnHover
      />

      <Divider margin="30px 0 !important" />

      <Text variant="body01-sm" fontWeight="bold" marginBottom={5}>
        Como ficou:
      </Text>
      <Table
        columns={COLUNAS_TABELA_COMPROVANTE_CONFIRMACAO_VALOR_CONTRIBUICAO}
        data={tryGetValueOrDefault([tabelaFundosAtualizados()], [])}
        noDataComponent="Não há dados para exibir."
        striped
        highlightOnHover
      />
    </>
  );
};

export default ResultadoSolicitacaoTabelas;
