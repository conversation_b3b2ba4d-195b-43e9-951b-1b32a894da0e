import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import * as multiFundoPrevidenciaApi from 'previdencia/features/AlteracaoPrazoDiferimento/services/multiFundoPrevidencia.api';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { ResponseMultiFundoPrevidencia } from 'previdencia/features/AlteracaoPrazoDiferimento/types/ResponseMultiFundoPrevidencia';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useObterMultiFundoPrevidencia = (
  userName: string,
): UseQueryResult<ResponseMultiFundoPrevidencia | undefined> => {
  const { toastError } = useToast();
  return useQueryCallbacks({
    queryKey: ['prev-multifundoprevidencia', userName],
    queryFn: () =>
      multiFundoPrevidenciaApi.obterMultiFundoPrevidencia(userName),
    staleTime: reactQueryCacheDuration(),
    retry: false,
    onError: (error: Error) => toastError(error.message),
  });
};

export default useObterMultiFundoPrevidencia;
