import { But<PERSON>, Display, Switch, Text } from '@cvp/design-system/react';
import {
  GridPersonalizado,
  Modal,
  TextPersonalizado,
} from 'painelAdministracao/components/ModalEditarMotivo/styles';
import RichTextEditor from 'main/components/RichTextEditor/RichTextEditor';
import {
  checkIfSomeItemsAreTrue,
  getTernaryResult,
} from 'main/utils/conditional';
import {
  BUTTON_TEXT,
  INFO_MODAL_MOTIVO,
  LABEL_MOTIVO,
} from 'painelAdministracao/constants/constants';
import ParseHTML from 'main/components/ParseHTML/ParseHTML';
import { MOTIVO_USO_TEXTS } from 'extranet/constants/motivoUsoFopLabelsConstants';
import { IModalEditarMotivoProps } from 'painelAdministracao/types/IEditarMotivoFop';
import RenderConditional from 'main/components/RenderConditional';
import { extrairTextoEditavel } from 'painelAdministracao/utils/utils';

export const ModalEditarMotivo = ({
  fopEditar,
  formik,
  handleAtualizarMotivo,
  handleChangeMotivoAtivo,
  handleDescricao,
  loading,
  onClose,
}: IModalEditarMotivoProps): React.ReactElement => {
  return (
    <Modal show onClose={onClose}>
      <Text variant="headline-04" color="primary" margin>
        {MOTIVO_USO_TEXTS.ATENCAO}
      </Text>

      <Display alignItems="center">
        <Text variant="body02-md" align="left" margin>
          <b>{LABEL_MOTIVO}</b>
        </Text>
        <div>
          <Display type="inline-block">
            <Switch
              onClick={() =>
                handleChangeMotivoAtivo(formik.values.motivoSolicitacaoAtivo)
              }
              checked={!!Number(fopEditar?.descricaoMotivoSolicitacao?.length)}
            />
          </Display>
        </div>
      </Display>

      <RenderConditional
        condition={checkIfSomeItemsAreTrue([
          !!Number(fopEditar?.descricaoMotivoSolicitacao?.length),
          formik.values.motivoSolicitacaoAtivo,
        ])}
      >
        <TextPersonalizado variant="caption-02" color="primary">
          {INFO_MODAL_MOTIVO}
        </TextPersonalizado>
        <RichTextEditor
          handleDescricao={handleDescricao}
          motivo={extrairTextoEditavel(
            String(fopEditar?.descricaoMotivoSolicitacao),
          )}
        />
        <TextPersonalizado variant="caption-02" color="primary">
          Pré Visualizar
        </TextPersonalizado>
      </RenderConditional>
      <GridPersonalizado>
        <ParseHTML
          html={getTernaryResult(
            formik.values.descricaoMotivoSolicitacao !== 'null',
            formik.values.descricaoMotivoSolicitacao,
            '',
          )}
        />
      </GridPersonalizado>
      <Display justify="center">
        <Button onClick={() => handleAtualizarMotivo()} loading={loading}>
          {BUTTON_TEXT}
        </Button>
      </Display>
    </Modal>
  );
};
