import styled from 'styled-components';
import {
  Accordion as AccordionBase,
  Card as CardBase,
  Display as DisplayBase,
} from '@cvp/design-system/react';

import InputSelect from 'main/components/form/InputSelect';

export const ContainerAccordion = styled.div`
  padding: 4px;
`;

const AccordionItemStyle = AccordionBase.Item;

export const Accordion = styled(AccordionBase)(() => ({
  padding: 0,
  marginBottom: '20px',
}));

export const AccordionItem = styled(AccordionItemStyle)(
  ({ theme: { color }, open }) => ({
    padding: 0,
    background: 'transparent',
    border: open
      ? `2px solid ${color.brandPrimary.light}`
      : `1px solid ${color.neutral['04']}`,
    div: {
      color: color.brandPrimary.light,
      paddingTop: open ? '1px' : 0,
      paddingBottom: open ? '1px' : 0,
    },

    p: {
      span: {
        div: {
          background: 'white',
          marginTop: open && '-5px',
        },
      },
    },

    'p &:after': {
      content: 'none',
    },
    'p &:before': {
      content: 'none',
    },
  }),
);

export const Card = styled(CardBase)(() => ({
  minHeight: 'auto',
}));

export const Form = styled.form`
  margin-bottom: '120px';
  inline-size: 1 red;
  width: 100%;
`;

export const DivLayout = styled.div`
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: space-between;
  gap: 1rem;
  width: auto;
`;
export const DivInput = styled.div<{ width?: string }>`
  display: flex;
  flex-direction: column;
  width: ${({ width = 'auto' }) => width};
`;

export const DivCamposFiltro = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  width: 100%;
  gap: 50px;
`;

export const DivBotoes = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: baseline;
  gap: 1rem;
  width: 100%;
`;

export const DisplayPersonalizado = styled(DisplayBase)`
  margin-bottom: 60px;
`;

export const Label = styled.label({
  display: 'inline-block',
  marginBottom: '8px',
  fontFamily: 'CAIXA Std',
  fontSize: '16px',
  fontWeight: 400,
  lineHeight: '150%',
});

export const InputSelectPersonal = styled(InputSelect)`
  width: 100%;
  max-width: 308px;
`;

export const InputBoxPersonal = styled.div`
  width: 100%;
  max-width: 307px;
`;
