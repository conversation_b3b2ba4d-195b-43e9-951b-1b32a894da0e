import { Display } from '@cvp/design-system/react';
import {
  FiltroContasBancarias,
  TabelaContasBancarias,
} from 'ferramentas/features/consultarContas/components';
import { useObterContasBancarias } from 'ferramentas/features/consultarContas/hooks/useObterContasBancarias';
import { ContainerTabelaContas } from 'ferramentas/features/consultarContas/styles/styles';

export const ConsultarContas: React.FC = () => {
  const { obterContas, loading, dados, resetarResponse, iniciado } =
    useObterContasBancarias();

  return (
    <Display type="display-block">
      <FiltroContasBancarias onSubmit={obterContas} onReset={resetarResponse} />

      <ContainerTabelaContas>
        <TabelaContasBancarias
          dados={dados}
          loading={loading}
          iniciado={iniciado}
        />
      </ContainerTabelaContas>
    </Display>
  );
};
