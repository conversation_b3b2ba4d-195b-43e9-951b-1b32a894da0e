import React from 'react';
import { Display, Text, Tooltip } from '@cvp/design-system/react';
import * as S from 'consultaCliente/features/listaCardProduto/components/CardsProduto/styles';
import { CardDadosBasicoProps } from 'consultaCliente/features/listaCardProduto/types/typesPrestamista';
import Icon from 'main/components/Icon';
import RenderConditional from 'main/components/RenderConditional';
import { TOOLTIP_NOME_SOCIAL } from 'main/constants/nomeSocialConstants';
import {
  checkIfAllItemsAreTrue,
  getTernaryResult,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import {
  LISTA_CARD_PRODUTO,
  NAME_LENGTH,
} from 'consultaCliente/features/listaCardProduto/consts/constsListaCardProduto';

const NomeCertificadoCardItem: React.FunctionComponent<
  CardDadosBasicoProps
> = ({
  nomeCliente,
  numCertificado,
  tipoProduto,
  taxaAtual,
  classificacaoDoPlano,
  classificacaoCard,
}) => {
  return (
    <>
      <RenderConditional condition={numCertificado !== '0'}>
        <div>
          <S.IconContainer>
            <Icon name="documentPaper" />
          </S.IconContainer>
          <Display
            justify="space-between"
            style={{ width: '100%', flexWrap: 'nowrap' }}
          >
            <div>
              <Text variant="caption-02">
                <RenderConditional
                  condition={tipoProduto === LISTA_CARD_PRODUTO.PRESTAMISTA}
                >
                  Contrato
                </RenderConditional>
                <RenderConditional
                  condition={tipoProduto !== LISTA_CARD_PRODUTO.PRESTAMISTA}
                >
                  Certificado {tryGetValueOrDefault([classificacaoDoPlano], '')}
                </RenderConditional>
              </Text>
              <Text variant="body03-md">{numCertificado}</Text>
            </div>
            <div>
              <RenderConditional
                condition={checkIfAllItemsAreTrue([
                  classificacaoCard === LISTA_CARD_PRODUTO.PREVIDENCIA,
                  !!parseFloat(tryGetValueOrDefault([taxaAtual], '')),
                ])}
              >
                <Text variant="caption-02">TX. de Adm.</Text>
                <Text variant="body03-md">{taxaAtual} %</Text>
              </RenderConditional>
            </div>
          </Display>
        </div>
      </RenderConditional>
      <div>
        <S.IconContainer>
          <Icon name="user" />
        </S.IconContainer>
        <span className="nameClient">
          <Text variant="caption-02">
            Nome
            <Tooltip text={TOOLTIP_NOME_SOCIAL} variant="gray">
              <Icon name="warning" />
            </Tooltip>
          </Text>
          <S.TextClientName
            statusLength={getTernaryResult(
              nomeCliente?.length === NAME_LENGTH.MAX_LENGTH,
              NAME_LENGTH.MIN_LENGTH,
              NAME_LENGTH.DEFAULT_LENGTH,
            )}
            title={nomeCliente}
          >
            {nomeCliente}
          </S.TextClientName>
        </span>
      </div>
    </>
  );
};

export default NomeCertificadoCardItem;
