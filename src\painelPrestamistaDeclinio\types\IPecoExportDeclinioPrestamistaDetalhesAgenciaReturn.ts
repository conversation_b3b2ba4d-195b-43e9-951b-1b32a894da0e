import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import { IExportarDeclinioPrestamistaDetalhesAgenciaPayload } from 'painelPrestamistaDeclinio/types/IExportarDeclinioPrestamistaDetalhesAgenciaPayload';
import { TExportarDeclinioPrestamistaDetalhesAgenciaResponse } from 'painelPrestamistaDeclinio/types/TExportarDeclinioPrestamistaDetalhesAgenciaResponse';

export interface IPecoExportDeclinioPrestamistaDetalhesAgenciaReturn {
  responseExportar: unknown;
  loadingExportar: boolean;
  fechDataExportar: (
    dynamicPayload?: IExportarDeclinioPrestamistaDetalhesAgenciaPayload,
  ) => Promise<unknown | undefined>;
  setResponseExportar: React.Dispatch<
    React.SetStateAction<
      | IHandleReponseResult<TExportarDeclinioPrestamistaDetalhesAgenciaResponse>
      | undefined
    >
  >;
}
