export interface IPaginacao {
  paginaAtual: number;
  itensPorPagina: number;
  totalItens: number;
  totalPaginas: number;
}
export interface Mensagem {
  codigo?: string;
  descricao?: string;
}

export interface IPaginacao {
  paginaAtual: number;
  itemsPorPagina: number;
  totalItems: number;
  totalPaginas: number;
}

export interface IApiResponseBFF<TEntity> {
  sucesso?: boolean;
  entidade?: TEntity;
  mensagens?: Mensagem[];
}

export interface IApiResponsePaginadoBFF<TEntity>
  extends IApiResponseBFF<TEntity> {
  paginacao?: IPaginacao;
}

export interface IApiResponse<TEntity> {
  sucesso?: boolean;
  dados: IApiResponseBFF<TEntity>;
  mensagem?: string;
}

export interface IApiResponsePaginado<TEntity> {
  sucesso?: boolean;
  dados: IApiResponsePaginadoBFF<TEntity>;
  mensagem?: string;
}
