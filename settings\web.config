<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <system.webServer>
        <staticContent>
            <!-- Using for file apple-app-site-association: -->
            <mimeMap fileExtension="." mimeType="application/json" />
        </staticContent>
        <rewrite>
            <rules>
                <rule name="DynamicContent">
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="True" />
                    </conditions>
                    <action type="Rewrite" url="index.html" />
                </rule>
            </rules>
        </rewrite>
        <defaultDocument>
            <files>
                <clear />
                <add value="index.html" />
                <add value="Default.htm" />
                <add value="Default.asp" />
                <add value="index.htm" />
                <add value="iisstart.htm" />
                <add value="default.aspx" />
            </files>
        </defaultDocument>
        <httpProtocol>
            <customHeaders>
            <remove name="X-Powered-By" />
            <add name="Content-Security-Policy" value="default-src 'self' 'unsafe-inline' https://*.caixavidaeprevidencia.com.br https://*.powerbi.com https://*.privacytools.com.br; img-src 'self' https://*.caixavidaeprevidencia.com.br https://*.privacytools.com.br data:; object-src 'none'; media-src 'self'; frame-ancestors 'self'; form-action 'self'; block-all-mixed-content; upgrade-insecure-requests;"/>
            <add name="Access-Control-Allow-Origin" value="*" />
            <add name="X-Frame-Options" value="SAMEORIGIN" />
            <add name="X-Content-Type-Options" value="nosniff" />
            <add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
            <add name="Referrer-Policy" value="origin-when-cross-origin" />
            <add name="X-XSS-Protection" value="1; mode=block" />
            <add name="Cache-Control" value="no-cache" />
            </customHeaders>
        </httpProtocol>
        <security>
            <requestFiltering removeServerHeader="true" />
        </security>
    </system.webServer>
    <system.web>
        <httpRuntime enableVersionHeader="false" />
    </system.web>
</configuration>