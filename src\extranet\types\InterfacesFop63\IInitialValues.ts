interface ValoresPlano {
  tempoMeses: string;
  porcentagemReversao: string;
}

interface TabelaCuidadoExtra {
  descricao: string;
  valorContribuicao: string;
  empresaPorcentagem: string;
  participantePorcentagem: string;

  empresaPorcentagemPensaoPrazoCerto: string;
  participantePorcentagemPensaoPrazoCerto: string;
}

export interface ValoresContribuicao {
  descricaoGrupo: string;
  participante: string;
  empresa: string;
  valorContribuicao: string;
}

export interface IInitialValuesType {
  linkCusteioPagamento: string;
  nomeDaEmpresa: string;
  atividadePrincipalEmpresa: string;
  cnpjEmpresa: string;
  faturamento: string;
  emailInstitucional: string;
  logradouro: string;
  bairro: string;
  cidade: string;
  cep: string;
  uf: string;
  agenciaFilial: string;
  agenciaSr: string;
  numeroDaAgencia: string;
  nomeDaAgencia: string;
  matriculaIndicador: string;
  nomeIndicador: string;
  nomeCompletoResponsavel: string;
  emailResponsavel: string;
  telefoneResponsavel: string;
  numeroDeParticipantes: string;
  regraCalculo: string;
  formaDePagamento: string;
  formaDePagamentoCuidado: string;
  linkSelectRegraCuidadoExtraPeculio: string;
  tipoBeneficioBasico: string;
  prazoBeneficio: string;
  reversao: string;
  optionsValoresParticipantes: string;
  valoresParticipantesCuidado: string;
  valorFixoContribuicao: string;
  valorPercentualContribuicao: string;
  valorFixoEmpresaContribuicao: string;
  linkValorContribuicao: string;
  linkSelectTipoConcessao: string;
  linkSelectTipoPagamentoFatura: string;
  linkSelectDadosCobranca: string;
  linkSelectCuidadoExtra: string;
  linkValorContribuicaoCuidadoExtra: string;
  linkValorContribuicaoEmpresa: string;
  linkValorContribuicaoEmpresaCuidadoExtra: string;
  linkValorContribuicaoFuncionario: string;
  linkValorContribuicaoFuncionarioCuidadoExtra: string;
  linkSelectTipoFundo: string;
  linkSelectVencimentoFatura: string;
  linkSelectDadosOperacao: string;
  linkSelectModalidade: string;
  linkSelectModalidadePGBL: string;
  linkSelectModalidadeVGBL: string;
  linkSelectRegraCuidadoExtraPensao: string;
  linkSelectAnosPensao: string;
  valorFixoEmpresaCuidadoExtraContribuicao: string;
  valorFixoFuncionarioContribuicao: string;
  valorFixoFuncionarioCuidadoExtraContribuicao: string;
  valorPercentualEmpresaContribuicao: string;
  valorPercentualEmpresaCuidadoExtraContribuicao: string;
  valorPercentualFuncionarioContribuicao: string;
  valorPercentualFuncionarioCuidadoExtraContribuicao: string;
  valorFixoCuidadoExtraContribuicao: string;
  valorPercentualCuidadoExtraContribuicao: string;
  nomeRepresentante: string;
  emailRepresentante: string;
  cargoRepresentante: string;
  aporteInicial: string;
  valorAporteInicial: string;
  liberacaoDaReserva: string;
  valorPortabilidade: string;
  prazoContribuicao: string;
  idadeAposentadoria: string;
  aporteUnico: string;
  agencia: string;
  conta: string;
  nomeRepresentanteLegal: string;
  cpfRepresentanteLegal: string;
  emailRepresentanteLegal: string;
  nomePrimeiraTestemunha: string;
  cpfPrimeiraTestemunha: string;
  emailPrimeiraTestemunha: string;
  formaCusteioModalidadePlano: string;
  pagamentoContribuicaoParticipante: string;
  PagamentoContribuicaoEmpresa: string;
  tipoPagamentoContribuicao: string;
  valorContribuicaoParticipante: string;
  valorContribuicaoEmpresa: string;
  diaPagamento: string;
  formaPagamentoRegraContratual: string;
  agenciaPagamentoConta: string;
  contaPagamentoConta: string;
  operacaoPagamentoConta: string;
  perdaVinculo: string;
  demisaoJustaCausa: string;
  penalidades: string;
  recursosInstituidora: string;
  distribuicaoContaColetiva: string;
  tempoMinimoPlano: string;
  descricaoFundo: string;
  textInformacoesComplementares: string;
  valortempo: string;
  pagamentoContribuicao: string;
  contribuicaoTabela: string;
  tempoMesesReversao: string;
  valoresPlano: ValoresPlano[];
  tipoDeContribuicaoCuidadoExtra: string;
  tabelaCuidadoExtra: TabelaCuidadoExtra[];
  valoresContribuicao: ValoresContribuicao[];
}
