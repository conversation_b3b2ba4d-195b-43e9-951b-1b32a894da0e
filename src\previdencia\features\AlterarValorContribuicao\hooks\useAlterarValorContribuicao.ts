import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import { AppContext } from 'main/contexts/AppContext';
import { ResponseMultiFundo } from 'previdencia/features/AlterarValorContribuicao/types/ResponsesAlterarValorContribuicao';
import * as AlterarValorContribuicaoApi from 'previdencia/features/AlterarValorContribuicao/services/alterarValorContribuicao.api';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useAlterarValorContribuicao = (
  valor: string,
): UseQueryResult<ResponseMultiFundo | undefined> => {
  const { toastError } = useToast();
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-alterar-valor-contribuicao', numCertificado],
    queryFn: () =>
      AlterarValorContribuicaoApi.alterarValorContribuicao(
        cpfCnpj,
        numCertificado,
        valor,
      ),
    refetchOnWindowFocus: false,
    retry: false,
    onError: () => toastError(),
  });
};

export default useAlterarValorContribuicao;
