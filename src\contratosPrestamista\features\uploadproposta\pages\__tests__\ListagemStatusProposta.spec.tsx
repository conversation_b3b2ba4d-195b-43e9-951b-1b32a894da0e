import { fireEvent, render, screen } from '@testing-library/react';
import mock from 'contratosPrestamista/features/uploadproposta/mocks/mockListagemStatusProposta';
import * as UsePeco from 'main/hooks/usePeco';
import ListagemStatusProposta from '../ListagemStatusProposta';
import { createMemoryRouter, RouterProvider } from 'react-router-dom';
import ThemeProvider from 'main/components/ThemeProvider';

const mockState = {
  cpf: '5336071000183',
  dataInicial: '',
  dataFinal: '',
};

const router = createMemoryRouter([
  {
    index: true,
    Component() {
      return <div>Pagina inicial</div>;
    },
  },
  {
    path: 'pagina-2',
    Component: ListagemStatusProposta,
  },
]);

describe('<ListagemStatusProposta />', () => {
  beforeEach(() => {
    vi.spyOn(UsePeco, 'usePeco').mockReturnValue({
      loading: false,
      response: {
        sucessoBFF: true,
        sucessoGI: true,
        mensagens: [],
        entidade: mock[0].data.dados.entidade,
      },
      responseBinary: null,
      error: null,
      fetchData: vi.fn(),
      baseFetchBinary: vi.fn(),
      invalidateCache: vi.fn(),
      setResponse: vi.fn(),
    });
  });

  afterAll(() => vi.clearAllMocks());

  it('Deve renderizar tabela com dados de proposta', async () => {
    render(
      <ThemeProvider>
        <RouterProvider router={router} />
      </ThemeProvider>,
    );

    router.navigate('/pagina-2', {
      state: mockState,
    });

    const numeroContratoElement = await screen.findAllByText(/5336071000183/i);

    expect(numeroContratoElement.length).toBeGreaterThan(0);
    expect(numeroContratoElement[0]).toBeInTheDocument();
  });

  it('Deve voltar para rota anterior ao clicar em nova consulta', async () => {
    render(
      <ThemeProvider>
        <RouterProvider router={router} />
      </ThemeProvider>,
    );

    router.navigate('/');

    router.navigate('/pagina-2', {
      state: mockState,
    });

    const botaoVoltar = screen.getByText(/nova consulta/i);

    fireEvent.click(botaoVoltar);

    expect(screen.getByText('Pagina inicial')).toBeInTheDocument();
  });
});
