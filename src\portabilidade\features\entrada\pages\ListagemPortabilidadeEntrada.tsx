import React, { useMemo } from 'react';
import { Card } from '@cvp/design-system/react';
import Table from 'main/components/Table';
import { TableSkeleton } from 'portabilidade/components/Table';
import { Linked } from 'portabilidade/features/retencao/components';
import TableFilter from 'main/components/Table/TableFilter/TableFilter';
import {
  FilterTypes,
  IFilterOption,
} from 'main/types/TableFilters/IFilterOption';
import { HorizontalProgressBar } from 'main/components/ProgressBar/HorizontalProgressBar/HorizontalProgressBar';
import masks from 'main/utils/masks';
import { useListarPortabilidadesEntrada } from '../hooks/usePortabilidadeEntrada';
import { converterRetornoParaListagem } from '../factories/listagemPortabilidadeEntradaFactory';
import RenderConditional from 'main/components/RenderConditional';
import { TableColumn } from 'react-data-table-component';
import { IListaPortabilidadeEntrada } from '../types/IListaPortabilidadeEntrada';

const ListagemPortabilidadeEntrada: React.FunctionComponent = () => {
  const { response, loading } = useListarPortabilidadesEntrada();

  const filterOptions: IFilterOption[] = [
    {
      key: 'numeroCpf',
      value: 'CPF/CNPJ',
      type: FilterTypes.TEXT,
      unmask: masks.cpf.unmask,
    },
    {
      key: 'numeroPortabilidade',
      value: 'Portabilidade',
      type: FilterTypes.TEXT,
    },
    { key: 'indicador', value: 'Indicador', type: FilterTypes.TEXT },
  ];
  const retornoConvertido = converterRetornoParaListagem(response);

  const colunas: TableColumn<IListaPortabilidadeEntrada>[] = useMemo(
    () => [
      {
        name: 'Portabilidade',
        selector: row => row.numeroPortabilidade,
        cell: ({ numeroPortabilidade }) => (
          <Linked
            to={`/portabilidades-entrada/${numeroPortabilidade}/detalhes`}
          >
            {numeroPortabilidade}
          </Linked>
        ),
        width: '200px',
      },
      {
        name: 'Nome do cliente',
        selector: row => row.nomeCliente,
        width: '190px',
      },
      {
        name: 'CPF do Cliente',
        selector: row => row.numeroCpf,
        width: '150px',
      },
      {
        name: 'Valor',
        selector: row => row.valorPortabilidadeFormatado,
        width: '150px',
      },
      {
        name: 'Agência',
        selector: row => row.agencia,
        width: '100px',
      },
      {
        name: 'Indicador',
        selector: row => row.indicador,
      },
      {
        name: 'Status',
        selector: row => row.status,
        width: '120px',
      },
      {
        selector: row => row.statusConclusao,
        name: '% de Conclusão',
        cell: ({ statusConclusao }) => (
          <HorizontalProgressBar width={statusConclusao} />
        ),
        width: '180px',
      },
    ],
    [],
  );

  return (
    <Card style={{ minHeight: 'auto' }}>
      <Card.Content>
        <RenderConditional condition={loading}>
          <TableSkeleton colunas={colunas} />
        </RenderConditional>

        <RenderConditional condition={!loading}>
          <TableFilter
            dataToFilter={retornoConvertido ?? []}
            filterTextPartial
            filterOptions={filterOptions}
          >
            {filteredData => (
              <Table
                noHeader
                responsive
                data={filteredData}
                columns={colunas}
                noDataComponent="Não há dados para exibir."
                pagination
                paginationPerPage={10}
                paginationComponentOptions={{
                  rowsPerPageText: 'Items por página',
                  rangeSeparatorText: 'de',
                }}
              />
            )}
          </TableFilter>
        </RenderConditional>
      </Card.Content>
    </Card>
  );
};

export default ListagemPortabilidadeEntrada;
