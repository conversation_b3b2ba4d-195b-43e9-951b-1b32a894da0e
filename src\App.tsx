import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
} from '@cvp/componentes-posvenda';
import { registerAppVersion } from 'config/app';
import { registrarInterceptors } from 'config/http/interceptors';
import {
  menuAtendimento,
  menuConsulta,
  menuExternoPrevidencia,
  menuExternoVida,
  menuPosicaoConsolidada,
  menuContratosPrestamista,
  menuPrevidencia,
  menuVida,
  menuRelatoriosInadimplencia,
  menuPrestamista,
  menuEvolucaoPatrimonial,
  menuAdministracao,
  menuDPSEletronica,
} from 'config/menus';
import AlertaModal from 'main/components/Notifications/AlertaModal';
import ThemeProvider from 'main/components/ThemeProvider';
import AppContextProvider from 'main/contexts/AppContext';
import { AuthProvider } from 'main/features/Auth/contexts/AuthContext';
import { api } from 'main/services';
import { ToastContainer } from 'main/styles/GlobalStyle';
import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { BrowserRouter as Router } from 'react-router-dom';
import 'react-toastify/dist/ReactToastify.css';
import Routes from './routes';

registrarInterceptors();
registerAppVersion();

const App: React.FunctionComponent = () => {
  const queryClient = new QueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      <ComponentesPosVendaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: api,
            authMode: 'DEFAULT_GI',
            usernameKey: '@portal-eco:nomeAcesso',
            storageMode: 'LOCAL',
            operationPath: 'PortalEconomiario/',
          }}
        >
          <ThemeProvider>
            <AppContextProvider
              menus={[
                menuConsulta,
                menuExternoPrevidencia,
                menuContratosPrestamista,
                menuExternoVida,
                menuAtendimento,
                menuPosicaoConsolidada,
                menuRelatoriosInadimplencia,
                menuEvolucaoPatrimonial,
                menuAdministracao,
                menuDPSEletronica,
              ]}
              contextualMenus={[menuPrevidencia, menuVida, menuPrestamista]}
            >
              <AuthProvider>
                <Router>
                  <Routes>
                    <AlertaModal />
                  </Routes>
                  <ToastContainer />
                </Router>
              </AuthProvider>
            </AppContextProvider>
          </ThemeProvider>
          <ReactQueryDevtools
            initialIsOpen={import.meta.env.NODE_ENV === 'development'}
          />
        </ApiGatewayCvpProvider>
      </ComponentesPosVendaProvider>
    </QueryClientProvider>
  );
};

export default App;
