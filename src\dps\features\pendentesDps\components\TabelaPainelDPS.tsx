import React from "react";
import { TableColumn } from "react-data-table-component";
import RenderConditional from "main/components/RenderConditional";
import Table, { setTableType, Skeleton } from "main/components/Table";
import TableFilter from "main/components/Table/TableFilter";
import { tryGetValueOrDefault } from "main/utils/conditional";
import { FILTRO_DPS } from "dps/features/pendentesDps/constants/constants";
import { ITabelaPainelDPS } from "dps/features/pendentesDps/types/ITabelaPainelDPS";
import { IAgenciaDPS } from "dps/features/pendentesDps/types/IBuscarBuscarAgenciaDPSResponse";
import { GridWrapper } from "dps/features/pendentesDps/styles/style";
import { IBuscarClientesDPS } from "dps/features/pendentesDps/types/IBuscarClientesDPSResponse";

export const TabelaPainelDPS: React.FC<ITabelaPainelDPS> = ({
  dados,
  colunas,
  loading,
  agencia,
}) => {
  return (
    <>
      <RenderConditional condition={loading}>
        <Skeleton
          colunas={colunas}
          data-testid="tableSkeleton"
        />
      </RenderConditional>
      <RenderConditional condition={!loading}>
        <RenderConditional condition={agencia}>
          <TableFilter
            dataToFilter={dados as IAgenciaDPS[]}
            filterOptions={FILTRO_DPS}
            filterTextPartial
          >
            {(dataFiltered) => (
              <GridWrapper>
                <Table
                  columns={setTableType<TableColumn<IBuscarClientesDPS>[]>(
                    colunas
                  )}
                  data={tryGetValueOrDefault(
                    [setTableType<IBuscarClientesDPS[]>(dataFiltered)],
                    []
                  )}
                  noHeader
                  responsive
                  striped
                  highlightOnHover
                  noDataComponent="Não há dados para exibir."
                  pagination
                  paginationRowsPerPageOptions={[5, 10, 25, 50, 100]}
                  paginationPerPage={5}
                  paginationComponentOptions={{
                    rowsPerPageText: "Itens por página",
                    rangeSeparatorText: "de",
                  }}
                />
              </GridWrapper>
            )}
          </TableFilter>
        </RenderConditional>
        <RenderConditional condition={!agencia}>
          <Table
            columns={colunas}
            data={tryGetValueOrDefault([dados], [])}
            noHeader
            responsive
            striped
            highlightOnHover
            noDataComponent="Não há dados para exibir."
            pagination
            paginationRowsPerPageOptions={[5, 10, 25, 50, 100]}
            paginationPerPage={5}
            paginationComponentOptions={{
              rowsPerPageText: "Itens por página",
              rangeSeparatorText: "de",
            }}
          />
        </RenderConditional>
      </RenderConditional>
    </>
  );
};
