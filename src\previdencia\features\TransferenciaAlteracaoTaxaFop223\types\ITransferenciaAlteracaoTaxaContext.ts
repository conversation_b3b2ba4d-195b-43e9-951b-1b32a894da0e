import { TPerfilFundo } from './TableColumns';
import { IDadosCobertura } from './TransferenciaConsultar';
import { IFundo } from './VGBLListarOfertas';

export interface ITransferenciaAlteracaoTaxaProps {
  certificado: string;
  codigoProduto: string;
  saldoReserva: number;
  faixaReserva: number;
  fundosSelecionados: IFundo[];
  valorContribuicao: number;
  valorPeculio: number;
  valorPensao: number;
  prazoPensao: number;
  familiaFundoEscolhida: number;
  fundoDiferenciadoSelecionado?: string;
  possuiFundosDiferenciados: boolean;
  coberturas: IDadosCobertura[];
  coberturasNaoComerciaizadas: IDadosCobertura[];
  numTransferencia: string;
  perfilFundoAtual: TPerfilFundo;
  perfilNovoFundo: TPerfilFundo;
}

export interface ITransferenciaAlteracaoTaxaContext
  extends Partial<ITransferenciaAlteracaoTaxaProps> {}
