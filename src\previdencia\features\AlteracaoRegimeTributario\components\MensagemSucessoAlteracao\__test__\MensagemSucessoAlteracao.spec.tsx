import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import MensagemSucessoAlteracao from 'previdencia/features/AlteracaoRegimeTributario/components/MensagemSucessoAlteracao';
import * as usePrevNavigation from 'previdencia/hooks/usePrevNavigation';
import * as CONSTS from 'previdencia/features/AlteracaoRegimeTributario/constants/constants';

describe('MensagemSucessoAlteracao', () => {
  let goDadosPlanoMock: any;

  beforeEach(() => {
    goDadosPlanoMock = vi.fn();

    vi.spyOn(usePrevNavigation, 'default').mockReturnValue({
      navigateTo: vi.fn(),
      navigateToVida: vi.fn(),
      goHomeVida: vi.fn(),
      goHome: vi.fn(),
      goDadosPlano: goDadosPlanoMock,
    });
    const queryClient = new QueryClient();
    render(
      <QueryClientProvider client={queryClient}>
        <RenderPageWithThemeProviderAndRouter>
          <MensagemSucessoAlteracao />
        </RenderPageWithThemeProviderAndRouter>
      </QueryClientProvider>,
    );
  });

  it('deve renderizar o texto de sucesso corretamente', () => {
    const mensagemSucesso = screen.getByRole('status');
    expect(mensagemSucesso).toBeInTheDocument();
    expect(
      screen.getByText(CONSTS.TEXTOS_REGIME_TRIBUTARIO_MSG.SUCESSO),
    ).toBeInTheDocument();
  });

  it('deve renderizar o botão Voltar corretamente', () => {
    const botaoVoltar = screen.getByRole('button', { name: /Voltar/i });
    expect(botaoVoltar).toBeInTheDocument();
  });

  it('deve chamar goDadosPlano ao clicar no botão Voltar', () => {
    const botaoVoltar = screen.getByRole('button', { name: /Voltar/i });
    fireEvent.click(botaoVoltar);
    expect(goDadosPlanoMock).toHaveBeenCalledTimes(1);
  });
});
