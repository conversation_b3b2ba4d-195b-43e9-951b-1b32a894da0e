{"name": "PortalEconomiario-CaixaVidaePrevidencia", "private": true, "version": "1.0.0", "type": "module", "dependencies": {"@cvp/design-system": "8.0.0", "@lexical/html": "^0.13.1", "@lexical/react": "^0.13.1", "@cvp/componentes-posvenda": "^1.9.3", "@radix-ui/react-switch": "^1.2.2", "@tanstack/react-query": "^5.51.9", "@tanstack/react-query-devtools": "^5.81.2", "@testing-library/user-event": "12.1.10", "@types/faker": "5.5.9", "@types/history": "4.7.8", "@types/react-text-mask": "5.4.9", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "apexcharts": "^4.0.0", "axios": "1.7.9", "cpf-cnpj-validator": "1.0.3", "cross-env": "7.0.2", "date-fns": "2.23.0", "formik": "^2.4.6", "html-react-parser": "^3.0.16", "iframe-resizer-react": "^1.1.0", "javascript-obfuscator": "^4.1.1", "jwt-decode": "^3.1.2", "lexical": "^0.13.1", "miragejs": "^0.1.43", "powerbi-client": "^2.21.1", "powerbi-client-react": "^1.3.5", "powerbi-report-authoring": "^1.1.3", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-data-table-component": "^7.6.2", "react-datepicker": "8.3.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-iframe": "^1.8.0", "react-router-dom": "^6.28.1", "react-tiny-fab": "^4.0.3", "react-toastify": "^7.0.4", "sanitize-html": "^2.17.0", "styled-components": "5.3.11", "uuid": "^11.1.0", "uuidv4": "^6.2.8", "web-vitals": "^1.0.1", "yup": "^0.32.9"}, "devDependencies": {"@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/react-hooks": "^7.0.1", "@types/jest": "^26.0.24", "@types/node": "^20.8.2", "@types/react": "^18.2.20", "@types/react-datepicker": "^4.1.7", "@types/react-dom": "^18.2.19", "@types/react-query": "^1.2.9", "@types/react-router-dom": "^5.1.7", "@types/sanitize-html": "^2.16.0", "@types/styled-components": "^5.1.9", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitest/coverage-v8": "^3.1.2", "@vitest/ui": "^3.1.2", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "dotenv": "^16.4.7", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.2", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "faker": "^5.5.3", "globals": "^16.0.0", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transformer-svg": "^2.1.0", "jsdom": "^26.0.0", "lint-staged": "^13.2.2", "prettier": "^3.5.3", "react-error-overlay": "6.0.9", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-eslint": "^8.30.1", "vite": "^6.1.0", "vite-plugin-eslint": "^1.6.0", "vite-plugin-magical-svg": "^1.6.0", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.1.2"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "postcss": "sass ./src/assets/scss/style.scss ./src/assets/css/style.css && postcss ./src/assets/css/style.css -o ./src/assets/css/style.css", "sass": "sass ./src/assets/scss/:./src/assets/css/", "sass-min": "sass ./src/assets/scss/:./src/assets/css/ --style compressed", "build:dev": "tsc && vite build --mode development", "postbuild:dev": "node tools/copyIISConfig.js", "build:hom": "tsc && vite build  --mode homologation", "postbuild:hom": "node tools/copyIISConfig.js", "build:pre-prd": "tsc && vite build --mode pre-production", "postbuild:pre-prd": "node tools/copyIISConfig.js", "build:prd": "node tools/removeMocks.js && vite build --mode production", "postbuild:prd": "node tools/copyIISConfig.js", "test": "vitest", "test:coverage": "vitest --coverage", "test-ui": "vitest --ui", "test:watch": "vitest --watch", "prepare": "husky install", "commit": "npm run lint-staged && npm run test && cz", "lint-staged": "lint-staged", "check-types": "tsc --noEmit"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": ["prettier --write"]}}, "husky": {"hooks": {"prepare-commit-msg": "exec < /dev/tty && npx cz --hook || true"}}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": ["prettier --write"]}}