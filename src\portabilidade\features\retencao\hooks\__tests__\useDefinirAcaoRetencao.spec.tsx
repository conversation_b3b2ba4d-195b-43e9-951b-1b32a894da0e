import { waitFor } from '@testing-library/react';
import { act, renderHook } from '@testing-library/react-hooks';
import faker from 'faker';
import { api } from 'main/services';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { IResponseDefinirRetencao } from '../../types/IResponseDefinirRetencao';
import { useDefinirAcaoRetencao } from '../useDefinirAcaoRetencao';

// Mock do getAuthData
vi.mock('../../../../../main/features/Auth/utils/auth', () => ({
  default: () => ({
    user: {
      marcadorControle: 'TEST_MARCADOR_CONTROLE',
    },
  }),
}));

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

afterEach(() => {
  vi.clearAllMocks();
});

describe('useDefinirAcaoRetencao.spec', () => {
  const queryClient = new QueryClient();

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ComponentesPosVendaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: api,
            authMode: 'DEFAULT_GI',
            usernameKey: '@portal-eco:nomeAcesso',
            storageMode: 'LOCAL',
            operationPath: 'PortalEconomiario/',
          }}
        >
          {children}
        </ApiGatewayCvpProvider>
      </ComponentesPosVendaProvider>
    </QueryClientProvider>
  );

  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

  it('deve definir uma ação de retenção com erro', async () => {
    const mockErrorResponse = {
      sucessoBFF: false,
      entidade: null,
      mensagens: [{ descricao: faker.lorem.lines() }],
    };

    // Mock do useApiGatewayCvpInvoker com erro
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockErrorResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi
        .fn()
        .mockResolvedValue(mockErrorResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });
    const { result } = renderHook(() => useDefinirAcaoRetencao(), {
      wrapper,
    });

    const idRetencao = faker.datatype.number().toString();
    const idAcao = '5';

    let successOperation:
      | IHandleReponseResult<IResponseDefinirRetencao>
      | undefined;
    await act(async () => {
      successOperation = await result.current.defirnirAcaoRetencao(
        idRetencao,
        idAcao,
      );
    });

    await waitFor(() => {
      expect(
        mockUseApiGatewayCvpInvoker().invocarApiGatewayCvpComToken,
      ).toBeCalled();
      expect(successOperation?.entidade).toBeFalsy();
    });
  });

  it('deve definir uma ação de retenção com sucesso', async () => {
    const mockSuccessResponse = {
      sucessoBFF: true,
      entidade: {
        urlCallBackAssinatura: faker.internet.url(),
      },
      mensagens: [{ descricao: faker.lorem.lines() }],
    };

    // Mock do useApiGatewayCvpInvoker
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockSuccessResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi
        .fn()
        .mockResolvedValue(mockSuccessResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });
    const idRetencao = faker.datatype.number().toString();
    const idAcao = '5';

    const { result } = renderHook(() => useDefinirAcaoRetencao(), {
      wrapper,
    });

    let successOperation:
      | IHandleReponseResult<IResponseDefinirRetencao>
      | undefined;

    await act(async () => {
      successOperation = await result.current.defirnirAcaoRetencao(
        idRetencao,
        idAcao,
      );
    });

    await waitFor(() => {
      expect(
        mockUseApiGatewayCvpInvoker().invocarApiGatewayCvpComToken,
      ).toBeCalled();
      expect(successOperation).toBeTruthy();
      expect(successOperation?.entidade?.urlCallBackAssinatura).toBeTruthy();
    });
  });
});
