import { Button, Text as Texto } from '@cvp/design-system/react';
import { EnumStatusCardProduto } from 'consultaCliente/types/ICardsApresentacao';
import {
  checkIfSomeItemsAreTrue,
  getTernaryResult,
} from 'main/utils/conditional';
import styled from 'styled-components';

export const Text = styled.div<{ status: string }>(
  ({ theme: { color, font, line }, status }) => ({
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    textTransform: 'capitalize',
    overflow: 'hidden',
    color: getTernaryResult(
      checkIfSomeItemsAreTrue([
        status === EnumStatusCardProduto.Ativo,
        status === EnumStatusCardProduto.Suspenso,
      ]),
      color.line.primary,
      color.neutral['04'],
    ),
    fontSize: font.size.md,
    fontWeight: font.weight.md,
    lineHeight: line.height.lg,
  }),
);

export const TextClientName = styled(Texto).attrs({
  variant: 'body03-md',
})(() => ({
  textOverflow: 'ellipsis',
  overflow: 'hidden',
  whiteSpace: 'nowrap',
}));

export const Container = styled.div`
  span {
    margin: 4px 2px;
  }
  button {
    margin: 4px 2px;
  }
  hr {
    margin: 4px;
  }
`;

export const ContainerMargin = styled.div`
  margin: 4px 2px;
  div {
    margin: 4px 0px;
  }
`;

export const IconContainer = styled.span`
  width: auto;
  svg {
    height: 20px;
    width: auto;
  }
`;
export const ContainerCard = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 1rem;
  border-radius: 12px;
  box-shadow:
    rgba(35, 31, 32, 0.05) 0px -2px 2px,
    rgba(35, 31, 32, 0.16) 0px 6px 8px;

  > span {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    justify-content: end;
    align-items: end;

    > div {
      width: 100%;
      max-width: 100%;
    }

    > span {
      width: fit-content;
    }
  }
`;

export const ContentData = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  align-items: center;

  > div {
    display: flex;
    padding-left: 8px;
    width: 100%;
    flex-direction: row;
    gap: 0.5rem;
    align-items: center;
  }
  .nameClient {
    max-width: 80%;
  }
`;

export const ContainerListProducts = styled.div`
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
`;

export const CardMobilidade = styled.span`
  display: flex;
  background-color: ${props => props.color};
  border-radius: 0.5rem;
  width: 100%;
  flex-direction: row;
  gap: 0.7rem;
  padding-left: 8px;
  align-items: center;
`;

export const ButtonCard = styled(Button)`
  width: 100%;
  margin-top: 1rem;
`;
