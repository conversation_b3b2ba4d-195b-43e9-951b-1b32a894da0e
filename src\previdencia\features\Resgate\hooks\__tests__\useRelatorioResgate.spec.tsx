import { act, cleanup, renderHook } from '@testing-library/react-hooks';
import faker from 'faker';
import { api } from 'main/services';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import * as REQUEST_TYPES from 'previdencia/features/Resgate/types/RelatorioResgateRequest';
import * as RESPONSE_TYPES from 'previdencia/features/Resgate/types/RelatorioResgateResponse';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { useRelatorioResgate } from '../useRelatorioResgate';

vi.mock('main/features/Auth/utils/auth', () => ({
  default: vi.fn(() => ({
    user: {
      marcadorControle: 'test-marcador-controle',
    },
    tokenInfo: { expiresIn: '' },
    sessionId: '',
    digitalAgency: false,
  })),
}));

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

afterAll(cleanup);

const mockResponseData: () => RESPONSE_TYPES.IRelatorioResgateResponse[] = () =>
  Array.from({ length: 10 }, () => ({
    codigoAgencia: faker.datatype.number().toString(),
    codigoCertificado: faker.datatype.number().toString(),
    codigoFonte: faker.datatype.number().toString(),
    codigoSureg: faker.datatype.number().toString(),
    cpfCnpjCliente: faker.datatype.number().toString(),
    dataGeracao: faker.date.soon().toDateString(),
    dataInicioVigencia: faker.date.soon().toDateString(),
    nomeAgencia: faker.random.alpha(),
    nomeCliente: `${faker.name.firstName()} ${faker.name.middleName()} ${faker.name.lastName()}`,
    nomeFilial: faker.random.alpha(),
    nomeSureg: faker.random.alpha(),
    numeroMatricula: faker.datatype.number().toString(),
    numeroProposta: faker.datatype.number().toString(),
    tipoResgate: faker.random.alpha(),
    valorResgate: faker.datatype.number().toString(),
  }));

describe('useRelatorioResgate', () => {
  const queryClient = new QueryClient();

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ComponentesPosVendaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: api,
            authMode: 'DEFAULT_GI',
            usernameKey: '@portal-eco:nomeAcesso',
            storageMode: 'LOCAL',
            operationPath: 'PortalEconomiario/',
          }}
        >
          {children}
        </ApiGatewayCvpProvider>
      </ComponentesPosVendaProvider>
    </QueryClientProvider>
  );

  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;
  it('Requisições: Deve fazer consultar api de PECO_RelatorioResgate', async () => {
    const fakeResponse = mockResponseData();
    const payload: REQUEST_TYPES.IRelatorioResgatePayload = {
      codAgencia: faker.datatype.number().toString(),
      dataInicio: faker.date.soon().toISOString(),
      dataFim: faker.date.soon().toISOString(),
    };

    const mockApiResponse = {
      sucessoBFF: true,
      entidade: fakeResponse,
    };

    // Mock do useApiGatewayCvpInvoker
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockApiResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi.fn().mockResolvedValue(mockApiResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    const { result } = renderHook(() => useRelatorioResgate(), { wrapper });

    let resultApi:
      | IHandleReponseResult<RESPONSE_TYPES.IRelatorioResgateResponse[]>
      | undefined;

    await act(async () => {
      resultApi = await result.current.fetchData(payload);
    });

    expect(resultApi?.sucessoBFF).toBeTruthy();
    expect(resultApi?.entidade?.length).toBe(10);
    expect(
      mockUseApiGatewayCvpInvoker().invocarApiGatewayCvpComToken,
    ).toBeCalled();
  });
});
