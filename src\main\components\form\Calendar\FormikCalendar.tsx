import React from 'react';
import { Text } from '@cvp/design-system/react';
import DatePicker, { registerLocale } from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import ptBR from 'date-fns/locale/pt-BR';
import Icon from 'main/components/Icon';
import CalendarInput from './CalendarInput';
import { ICalendarProps } from 'main/types/Forms/Calendar';
import * as S from './styles';
import {
  checkIfAllItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import RenderConditional from 'main/components/RenderConditional';

registerLocale('ptBR', ptBR);

const FormikCalendar: React.FC<ICalendarProps> = ({
  values,
  placeholder,
  maxDate,
  maxDateRange,
  range = false,
  requiredInitialDate = false,
  requiredFinalDate = false,
  errorInitialDate,
  errorInitialDateMessage,
  errorFinalDate,
  errorFinalDateMessage,
  onChange,
}) => {
  const { initialDate, finalDate } = values;

  const startInput = (
    <CalendarInput
      data-testid="calendar-input-initial"
      hasError={errorInitialDate}
    />
  );

  const endInput = (
    <CalendarInput
      data-testid="calendar-input-final"
      hasError={errorFinalDate}
    />
  );

  return (
    <>
      <S.FormCalendarContainer>
        <S.FormCalendarInputContainer>
          {range && (
            <Text
              variant="body01-md"
              color={errorInitialDate ? 'error' : 'inherit'}
            >
              De{requiredInitialDate && '*'}
            </Text>
          )}
          <DatePicker
            selected={initialDate}
            placeholderText={placeholder}
            onChange={(date) =>
              onChange({ initialDate: date, finalDate })
            }
            locale="ptBR"
            dateFormat="dd/MM/yyyy"
            dayClassName={() => 'calendar-day'}
            startDate={initialDate}
            endDate={finalDate}
            maxDate={maxDate}
            selectsStart
            customInput={startInput}
          />
        </S.FormCalendarInputContainer>
        {range && (
          <S.FormCalendarInputContainer>
            <Text
              variant="body01-md"
              color={errorFinalDate ? 'error' : 'inherit'}
            >
              Até{requiredFinalDate && '*'}
            </Text>
            <DatePicker
              selected={finalDate}
              placeholderText={placeholder}
              onChange={(date) =>
                onChange({ initialDate, finalDate: date })
              }
              locale="ptBR"
              dateFormat="dd/MM/yyyy"
              dayClassName={() => 'calendar-day'}
              startDate={initialDate}
              endDate={finalDate}
              minDate={initialDate ?? undefined}
              maxDate={maxDateRange}
              selectsEnd
              customInput={endInput}
            />
          </S.FormCalendarInputContainer>
        )}
      </S.FormCalendarContainer>

      <RenderConditional
        condition={checkIfAllItemsAreTrue([
          !!errorInitialDateMessage,
          !!errorFinalDateMessage,
        ])}
      >
        <S.ErrorMsg color="error">
          <Icon name="information" />
          &nbsp;{' '}
          {tryGetValueOrDefault(
            [errorInitialDateMessage],
            errorFinalDateMessage,
          )}
        </S.ErrorMsg>
      </RenderConditional>
    </>
  );
};

export default FormikCalendar;
