import * as DS from '@cvp/design-system/react';
import { TextArea } from 'main/components/form';
import styled from 'styled-components';

export const InputLabel = styled.span({
  marginBottom: 8,
  display: 'flex',
  lineHeight: '150%',
  fontSize: 16,
});

export const ErrorMessage = styled(DS.Text)({
  marginTop: '5px',
  svg: {
    width: '16px',
    height: '16px',
    color: '#900000',
    marginRight: '5px',
  },
});

export const InputValidation = styled.div`
  input.error {
    border: #900000 solid 1px;
  }
`;
export const TextAreaGrupos = styled.textarea(({ theme }) => ({
  '-webkit-transition': '0.3s',
  transition: ' 0.3s',
  resize: 'none',
  boxSizing: 'border-box',
  border: '1px solid #A4A5A9',
  borderRadius: '4px',
  background: theme.color.neutral['08'],
  height: '78%',
  outline: 'none',
  width: '100%',
  maxWidth: '100%',
  color: '#414042',
  paddingLeft: '12px',
  paddingRight: '12px',
  fontFamily: 'CAIXA Std',
  fontWeight: ' 400',
  lineHeight: '150%',
  '-webkit-letter-spacing': 'auto',
  '-moz-letter-spacing': 'auto',
  '-ms-letter-spacing': ' auto',
  'letter-spacing': 'auto',
  fontSize: 16,
  minHeight: 56,
}));

export const WraperLabelParticipante = styled.label({});

export const LabelParticipante = styled.div({
  fontSize: 16,
  color: '#414042',
  marginBottom: 8,
});

export const AccordionItem = styled(DS.Accordion.Item)`
  padding-bottom: 0px;
  border-bottom: none;
`;

export const TextAreaFop = styled(TextArea)`
  resize: none;
  width: 100%;
  height: 160px;
`;
