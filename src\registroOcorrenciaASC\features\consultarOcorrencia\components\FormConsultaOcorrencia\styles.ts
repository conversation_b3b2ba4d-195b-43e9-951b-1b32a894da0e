import styled from 'styled-components';
import { InputFormik, SelectFormik } from 'main/components/form';

export const ContainerInputGroup = styled.div`
  width: 100%;
  margin: 0;
  margin-bottom: 25px;
`;

const applyPlaceholderColor = ({
  theme: { color },
}: {
  theme: { color: { neutral: { [key: string]: string } } };
}) => ({
  '& input::placeholder': {
    color: color.neutral['03'],
  },
});

export const FormikInput = styled(InputFormik)(applyPlaceholderColor);

export const FormikSelect = styled(SelectFormik)(applyPlaceholderColor);
