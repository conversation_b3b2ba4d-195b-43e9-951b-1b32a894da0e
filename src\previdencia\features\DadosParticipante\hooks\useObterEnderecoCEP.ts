import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { AppContext } from 'main/contexts/AppContext';
import * as DadosEnderecoCEPApi from 'previdencia/features/DadosParticipante/services/enderecoCEP.api';
import { DadosCEP } from '../types/DadosParticipante';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useObterEnderecoCEP = (
  cep: string,
): UseQueryResult<DadosCEP | undefined> => {
  const { toastError } = useToast();
  const {
    cliente: { cpfCnpj },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-dados-cep', cpfCnpj],
    queryFn: () => DadosEnderecoCEPApi.obterDadosCEP(cpfCnpj, cep),
    staleTime: reactQueryCacheDuration(),
    retry: false,
    enabled: false,
    onError: (erro: Error) => toastError(erro.message),
  });
};

export default useObterEnderecoCEP;
