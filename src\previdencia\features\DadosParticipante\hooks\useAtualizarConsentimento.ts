import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import * as AtualizarConsentimentoApi from 'previdencia/features/DadosParticipante/services/atualizarConsentimento.api';
import { IApiResponse } from 'main/services';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useAtualizarConsentimento = (
  cpf: string | undefined,
  produtosCaixaVidaPrevidencia: boolean,
  consentimento: boolean | undefined,
  onCancelar: () => void,
): UseQueryResult<IApiResponse<undefined> | undefined> => {
  const { toastError, toastSuccess } = useToast();

  return useQueryCallbacks({
    queryKey: ['prev-atualizar-consentimento', cpf],
    queryFn: () =>
      AtualizarConsentimentoApi.atualizarConsentimento(
        cpf,
        produtosCaixaVidaPrevidencia,
        consentimento,
      ),
    staleTime: reactQueryCacheDuration(),
    retry: false,
    enabled: false,
    onError: (erro: Error) => toastError(erro.message),
    onSuccess: data => {
      if (data?.dados?.mensagens && !produtosCaixaVidaPrevidencia) {
        onCancelar();
        toastSuccess(String(data.dados.mensagens[0].descricao));
      }
    },
  });
};

export default useAtualizarConsentimento;
