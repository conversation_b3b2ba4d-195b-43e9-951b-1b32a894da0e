import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import * as AtualizarCadastroApi from 'previdencia/features/DadosParticipante/services/atualizarCadastro.api';
import { AppContext } from 'main/contexts/AppContext';
import { DadosAlteracao } from '../types/DadosParticipante';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useAtualizarCadastro = (
  request: DadosAlteracao | undefined,
): UseQueryResult<undefined> => {
  const { toastError } = useToast();
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-atualizar-cadastro', cpfCnpj],
    queryFn: () =>
      AtualizarCadastroApi.atualizarCadastro(cpfCnpj, numCertificado, request),
    staleTime: reactQueryCacheDuration(),
    retry: false,
    enabled: false,
    onError: (erro: Error) => toastError(erro.message),
  });
};

export default useAtualizarCadastro;
