import React from 'react';
import { But<PERSON>, Display, TextField } from '@cvp/design-system/react';
import { Field, FieldProps, Formik } from 'formik';
import { validaCpfCnpj } from 'main/utils/cpf_cnpj';
import masks from 'main/utils/masks';
import { IFormSearchClient } from 'consultaCliente/types/ICardsApresentacao';

const FormularioConsultarCliente: React.FC<IFormSearchClient> = ({
  handleSearchSubmit,
}) => {
  return (
    <Formik
      initialValues={{ search: '' }}
      onSubmit={values => handleSearchSubmit(values.search)}
    >
      {({ handleSubmit, handleBlur, values, setFieldValue, errors }) => (
        <form onSubmit={handleSubmit}>
          <Field name="search" validate={validaCpfCnpj}>
            {({ field, meta }: FieldProps) => {
              return (
                <TextField
                  {...field}
                  error={meta.touched && meta.error}
                  errorMessage={meta.touched && meta.error}
                  onChange={({
                    target: { value },
                  }: React.ChangeEvent<HTMLInputElement>) => {
                    setFieldValue('search', masks.cpfCnpj.unmask(value));
                  }}
                  value={masks.cpfCnpj.mask(values.search)}
                  onBlur={handleBlur}
                />
              );
            }}
          </Field>

          <Display justify="center">
            <Button
              variant="primary"
              type="submit"
              disabled={!!Object.keys(errors).length}
            >
              Consultar
            </Button>
          </Display>
        </form>
      )}
    </Formik>
  );
};

export default FormularioConsultarCliente;
