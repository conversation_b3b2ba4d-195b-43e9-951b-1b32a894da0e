import React, { PropsWithChildren } from 'react';
import { useLocation } from 'react-router-dom';
import { Middle, Main, Container } from 'main/components/Menu/Sidebar';
import { Hidden } from '@cvp/design-system/react';
import Menu from './AppMenu/Desktop/Sidebar';
import BottomNav from 'main/components/Layout/AppMenu/Mobile/CustomBottomNav';
import {
  InitialImageWrapper,
  MiddleAppWrapper,
  UnderHeaderLineStyle,
} from './styles';
import { getTernaryResult } from 'main/utils/conditional';
import { PATHS } from 'main/constants/paths';

const AppMiddle: React.FC<PropsWithChildren> = ({ children }) => {
  const { pathname } = useLocation();

  const isHomePath = pathname === PATHS.CLIENTE;

  return (
    <MiddleAppWrapper fundoAzul={isHomePath}>
      <Middle>
        <Hidden only={['sm', 'xs']}>
          <Menu />
        </Hidden>
        <Hidden only={['md', 'lg', 'xl']}>
          <BottomNav />
        </Hidden>
        <Main>
          <InitialImageWrapper consulta={isHomePath}>
            <UnderHeaderLineStyle />
            {getTernaryResult(
              isHomePath,
              children,
              <Container>{children}</Container>,
            )}
          </InitialImageWrapper>
        </Main>
      </Middle>
    </MiddleAppWrapper>
  );
};

export default AppMiddle;
