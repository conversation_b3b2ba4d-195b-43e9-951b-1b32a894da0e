import React from 'react';
import masks from 'main/utils/masks';
import { IInputProps } from 'main/types/Forms/Input';
import * as S from './styles';
import RenderConditional from 'main/components/RenderConditional';
import { checkIfAllItemsAreTrue } from 'main/utils/conditional';

const FormikInput: React.FC<IInputProps> = ({
  value,
  disabled,
  inputMask: { mask, unmask } = masks.none,
  legend,
  hidden,
  error,
  errorMsg,
  onKeyPress,
  onChange,
  ...props
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    onChange(unmask(inputValue));
  };

  return (
    <S.Container>
      <S.Input
        {...props}
        value={mask(value)}
        disabled={disabled}
        hidden={hidden}
        error={error}
        errorMessage={errorMsg}
        onKeyPress={onKeyPress}
        onChange={handleChange}
      />
      <RenderConditional
        condition={checkIfAllItemsAreTrue([!hidden, !!legend])}
      >
        <S.Legend isValid={!error}> {legend} </S.Legend>
      </RenderConditional>
    </S.Container>
  );
};

export default FormikInput;
