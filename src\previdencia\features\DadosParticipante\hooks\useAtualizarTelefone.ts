import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { IApiResponse } from 'main/services';
import * as AtualizarTelefonelApi from 'previdencia/features/DadosParticipante/services/atualizarTelefone.api';
import { AppContext } from 'main/contexts/AppContext';
import { RequestAlterarTelefone } from '../types/DadosParticipante';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useAtualizarTelefone = (
  request: RequestAlterarTelefone[] | undefined,
  onCancelar: () => void,
): UseQueryResult<IApiResponse<undefined> | undefined> => {
  const { toastError, toastSuccess } = useToast();
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-atualizar-telefone', cpfCnpj],
    queryFn: () =>
      AtualizarTelefonelApi.atualizarTelefone(cpfCnpj, numCertificado, request),

    staleTime: reactQueryCacheDuration(),
    retry: false,
    enabled: false,
    onError: (erro: Error) => toastError(erro.message),
    onSuccess: data => {
      if (data?.dados?.mensagens) {
        onCancelar();
        toastSuccess(String(data.dados.mensagens[0].descricao));
      }
    },
  });
};

export default useAtualizarTelefone;
