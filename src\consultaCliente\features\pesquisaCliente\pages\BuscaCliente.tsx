import { Card, Display, Grid, Text } from '@cvp/design-system/react';
import { TooltipWrapper } from 'main/components/Wrappers';
import { AppContext } from 'main/contexts/AppContext';
import { PREV_PERMISSIONS } from 'main/features/Auth/config/userProfiles';
import { useAuth } from 'main/features/Auth/hooks';
import masks from 'main/utils/masks';
import React, { useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import FormularioConsultarCliente from 'consultaCliente/features/pesquisaCliente/components/FormularioConsultarCliente';
import { TEXTOS_BUSCA_CLIENTE } from 'consultaCliente/features/pesquisaCliente/constants/BuscaCliente';
import { ENDPOINT_PESQUISA_CLIENTE } from 'consultaCliente/features/pesquisaCliente/constants/endpoint';
import { StyledGrid } from 'consultaCliente/features/pesquisaCliente/styles/styles';

const BuscaCliente: React.FC = () => {
  const navigate = useNavigate();
  const { setCliente, resetarCliente } = useContext(AppContext);
  const { removeCustomPermission } = useAuth();

  const handleSearchSubmit = (value: string) => {
    const unmaskedValue = masks.numberOnly.unmask(value);
    setCliente({ cpfCnpj: unmaskedValue, numCertificado: '' });
    navigate(ENDPOINT_PESQUISA_CLIENTE.PRODUTO);
  };

  useEffect(() => {
    resetarCliente();
    removeCustomPermission(PREV_PERMISSIONS.POSICAO_CONSOLIDADA);
  }, []);

  return (
    <StyledGrid alignItems="center">
      <Grid.Item xs={1}>
        <Text variant="headline-04" color="white" margin align="left">
          {TEXTOS_BUSCA_CLIENTE.BOAS_VINDAS}
        </Text>
      </Grid.Item>

      <Grid.Item xs={1}>
        <Display justify="center">
          <Card>
            <Card.Content align="left">
              <Text margin>Consulte seu cliente</Text>
              <TooltipWrapper
                left={93}
                top={24}
                tooltip="A busca por CNPJ, só permite visualizar funcionalidades para Pessoa Física."
              >
                <Text
                  color="text-light"
                  variant="body02-sm"
                  margin
                  style={{ maxWidth: '350px' }}
                >
                  {TEXTOS_BUSCA_CLIENTE.CONSULTA}
                </Text>
              </TooltipWrapper>

              <FormularioConsultarCliente
                handleSearchSubmit={handleSearchSubmit}
              />
            </Card.Content>
          </Card>
        </Display>
      </Grid.Item>
    </StyledGrid>
  );
};

export default BuscaCliente;
