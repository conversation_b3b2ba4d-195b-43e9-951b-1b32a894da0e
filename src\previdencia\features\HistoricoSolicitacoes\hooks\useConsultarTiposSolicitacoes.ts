import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import * as TiposSolicitacoesApi from 'previdencia/features/HistoricoSolicitacoes/services/tiposSolicitacoes.api';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { AppContext } from 'main/contexts/AppContext';
import { ResponseTiposSolicitacoes } from '../types/HistoricoSolicitacoes';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useConsultarTiposSolicitacoes = (): UseQueryResult<
  ResponseTiposSolicitacoes[] | undefined
> => {
  const { toastError } = useToast();
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-tipos-solicitacoes-', cpfCnpj],
    queryFn: () =>
      TiposSolicitacoesApi.obterTiposSolicitacoes(cpfCnpj, numCertificado),

    gcTime: reactQueryCacheDuration(),
    retry: false,
    onError: (erro: Error) => toastError(erro.message),
  });
};
export default useConsultarTiposSolicitacoes;
