import { Grid } from '@cvp/design-system/react';
import { ENDPOINT_LISTA_CARD_PRODUTO } from 'consultaCliente/features/listaCardProduto/consts/endpoints';
import { ICardProdutoVida } from 'consultaCliente/features/listaCardProduto/interfaces/ICardProdutoVida';
import { ListaSegurosProps } from 'consultaCliente/features/listaCardProduto/types/listaSegurosType';
import GridCardSkeleton from 'main/components/GridCardSkeleton';
import RenderConditional from 'main/components/RenderConditional';
import { AppContext } from 'main/contexts/AppContext';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import React, { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCertificadoSeguroResumo } from 'seguros/hooks/useCertificadoSeguroResumo';
import { CardVida } from './CardVida';

const ListaSeguros: React.FunctionComponent<ListaSegurosProps> = ({
  seguros,
  cpfCnpj,
}) => {
  const navigate = useNavigate();
  const [item, setItem] = useState<ICardProdutoVida>();
  const { setClienteVida } = useContext(AppContext);

  const handleNumeroApolice = () => {
    if (Number(item?.numeroApolice) > 0) return item?.numeroApolice;

    return item?.numeroBilhete;
  };

  const {
    certificadoSeguros: { loading, response, fetchData },
  } = useCertificadoSeguroResumo({
    numeroContrato: tryGetValueOrDefault([item?.numeroBilhete], '0'),
    codigoProduto: tryGetValueOrDefault([item?.codigoProduto], '0'),
  });

  const exibirSeguros = !loading && !!seguros?.length;

  useEffect(() => {
    if (response) {
      setClienteVida({
        cpfCnpj,
        codigoCliente: String(item?.codigoCliente),
        numApolice: handleNumeroApolice(),
        numCertificado: tryGetValueOrDefault([item?.numeroBilhete], '0'),
        nomeSegurado: tryGetValueOrDefault([item?.nomeSegurado], '0'),
        codigoProduto: tryGetValueOrDefault([item?.codigoProduto], '0'),
        tipoCertificado: response?.entidade?.tipoContrato,
      });
      navigate(ENDPOINT_LISTA_CARD_PRODUTO.CLIENTE_PRODUTO_VIDA);
    }
  }, [response]);

  useEffect(() => {
    if (item) {
      fetchData();
    }
  }, [item]);

  return (
    <>
      <RenderConditional condition={loading}>
        <GridCardSkeleton quantidadeColunas={4} />
      </RenderConditional>
      <RenderConditional condition={exibirSeguros}>
        <Grid>
          {seguros?.map((itemHandle: ICardProdutoVida) => (
            <Grid.Item
              key={itemHandle.codigoProduto}
              sm={1}
              md={1 / 2}
              xl={1 / 4}
            >
              <CardVida onClick={() => setItem(itemHandle)} data={itemHandle} />
            </Grid.Item>
          ))}
        </Grid>
      </RenderConditional>
    </>
  );
};

export default ListaSeguros;
