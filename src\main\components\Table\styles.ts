import styled from 'styled-components';

export const ReactDataTable = styled.div(({ theme }) => ({
  maxWidth: '100%',
  flexGrow: 1,
  '.rdt_TableHeader': {
    display: 'none',
  },

  '.rdt_TableCol': {
    textAlign: 'center',
    fontSize: '16px',
    padding: '12px',

    [theme.breakpoint.md()]: {
      backgroundColor: theme.color.neutral['06'],
    },
  },

  '.rdt_TableCell': {
    fontSize: '16px',
    paddingTop: '12px',
    paddingBottom: '12px',
  },

  '.rdt_tablecol_sortable': {
    textAlign: 'center',
  },

  '.cQxDXr': {
    padding: '10px',
  },
}));
