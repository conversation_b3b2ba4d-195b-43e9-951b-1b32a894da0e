import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';

export type TPayloadRegistrarSolicitacaoFop = {
  codigoFop: number;
  numeroVersaoFop: number;
  nomeSolicitante: string;
  emailSolicitante: string;
  enviarEmail: false;
  metadadosSolicitacao: string;
};

export type TResponseRegistrarSolicitacaoFop = {
  response: IHandleReponseResult<unknown> | undefined;
  loading: boolean;
  error: unknown;
  salvarMotivo: (
    payload: TPayloadRegistrarSolicitacaoFop,
  ) => Promise<IHandleReponseResult<unknown> | undefined>;
};
