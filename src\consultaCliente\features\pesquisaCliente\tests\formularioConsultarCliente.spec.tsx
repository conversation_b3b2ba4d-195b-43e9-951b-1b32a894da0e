import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import FormularioConsultarCliente from 'consultaCliente/features/pesquisaCliente/components/FormularioConsultarCliente';
import {
  RenderPageWithAppContextAndQueryClient,
  RenderPageWithThemeProviderAndRouter,
} from 'main/utils/testUtils';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('main/utils/masks', () => ({
  default: {
    cpfCnpj: {
      mask: (v: string) => `###${v}###`,
      unmask: (v: string) => v.replace(/#/g, ''),
    },
  },
}));
vi.mock('main/utils/cpf_cnpj', () => ({
  validaCpfCnpj: vi.fn().mockReturnValue(undefined),
}));

describe('FormularioConsultarCliente', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('botão inicia habilitado (não há erros)', () => {
    render(
      <RenderPageWithAppContextAndQueryClient>
        <RenderPageWithThemeProviderAndRouter>
          <FormularioConsultarCliente handleSearchSubmit={vi.fn()} />
        </RenderPageWithThemeProviderAndRouter>
      </RenderPageWithAppContextAndQueryClient>
    );
    const btn = screen.getByRole('button', { name: /consultar/i });
    expect(btn).toBeEnabled();
  });

  it('mascara valor ao digitar e envia valor desmascarado', async () => {
    const handleSearchSubmit = vi.fn();
    render(
      <RenderPageWithAppContextAndQueryClient>
        <RenderPageWithThemeProviderAndRouter>
          <FormularioConsultarCliente handleSearchSubmit={handleSearchSubmit} />
        </RenderPageWithThemeProviderAndRouter>
      </RenderPageWithAppContextAndQueryClient>
    );

    const input = screen.getByRole('textbox');
    const btn = screen.getByRole('button', { name: /consultar/i });

    await userEvent.type(input, '60040838013');
    expect(input).toHaveValue('###60040838013###');

    expect(btn).toBeEnabled();
  });

  it('exibe erro de validação e mantém botão desabilitado', async () => {

    const onSubmit = vi.fn();
    render(
      <RenderPageWithAppContextAndQueryClient>
        <RenderPageWithThemeProviderAndRouter>
          <FormularioConsultarCliente handleSearchSubmit={onSubmit} />
        </RenderPageWithThemeProviderAndRouter>
      </RenderPageWithAppContextAndQueryClient>
    );

    const input = screen.getByRole('textbox');
    const btn = screen.getByRole('button', { name: /consultar/i });

    await userEvent.type(input, '000');
    userEvent.tab();
    waitFor(
      () => {
        expect(screen.getByText('CPF inválido!')).toBeInTheDocument();
        expect(btn).toBeDisabled();

        userEvent.click(btn);
        expect(onSubmit).not.toHaveBeenCalled();
      },
      { timeout: 3000 },
    );

  });
});
