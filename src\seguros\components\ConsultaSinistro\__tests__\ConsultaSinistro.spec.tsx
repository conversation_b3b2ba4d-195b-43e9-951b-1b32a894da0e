import {
  cleanup,
  fireEvent,
  render,
  screen,
  waitFor,
} from '@testing-library/react';
import { api } from 'main/services';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import ConsultaSinistro from 'seguros/components/ConsultaSinistro/ConsultaSinistro';
import * as CONSTS from 'seguros/constants/ConsultaSinistro';

vi.mock('main/components/RenderConditional', async () => {
  const module = await import(
    'main/components/RenderConditional/RenderConditionalMock'
  );
  return { default: module.default };
});

vi.mock('main/features/Auth/utils/auth', () => ({
  default: vi.fn(() => ({
    user: {
      marcadorControle: 'test-marcador-controle',
    },
    tokenInfo: { expiresIn: '' },
    sessionId: '',
    digitalAgency: false,
  })),
}));

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

afterEach(cleanup);
const queryClient = new QueryClient();

const componentePadrao = () => {
  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

  // Mock do useApiGatewayCvpInvoker
  mockUseApiGatewayCvpInvoker.mockReturnValue({
    loading: false,
    response: { sucessoBFF: true, entidade: [] },
    responseBinary: null,
    invocarApiGatewayCvpComToken: vi
      .fn()
      .mockResolvedValue({ sucessoBFF: true, entidade: [] }),
    invocarApiGatewayCvpComRetornoBinary: vi.fn(),
    setResponse: vi.fn(),
    invalidateCache: vi.fn(),
  });

  render(
    <QueryClientProvider client={queryClient}>
      <ComponentesPosVendaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: api,
            authMode: 'DEFAULT_GI',
            usernameKey: '@portal-eco:nomeAcesso',
            storageMode: 'LOCAL',
            operationPath: 'PortalEconomiario/',
          }}
        >
          <RenderPageWithThemeProviderAndRouter>
            <ConsultaSinistro />
          </RenderPageWithThemeProviderAndRouter>
        </ApiGatewayCvpProvider>
      </ComponentesPosVendaProvider>
    </QueryClientProvider>,
  );
};

describe('<ConsultaSinistro />', () => {
  it('Renderização: deveria renderizar o título do módulo de Consulta de Sinistro', () => {
    componentePadrao();

    const titulo = screen.getByTestId('tituloConsultaSinistro');

    expect(titulo).toBeInTheDocument();
    expect(titulo.textContent).toBe(CONSTS.TEXTOS_CONSULTA_SINISTRO.TITULO);
  });

  it('Interação: deveria chamar a função navigate e direcionar para o root de vida quando o botão for clicado', () => {
    componentePadrao();

    const botaoVoltar = screen.getByTestId('voltarConsultarSinistro');

    fireEvent.click(botaoVoltar);

    expect(window.location.pathname).toBe(
      '/cliente/produtos/vida/dados-segurado',
    );
  });

  it('Requisição: deveria mostrar a tabela com o mockResponse', async () => {
    const mockResponse = [
      {
        numeroContrato: 45749936,
        indiceContrato: 1,
        codigoCausa: 67,
        codigoCobertura: 41,
        dataHoraSinistro: '2016-03-19T00:00:00',
        coberturas: 'SAUDE VIVER',
        causas: 'INVALIDEZ PERMANENTE - EVENTO GERADOR POR ACIDENTE',
        dataHoraAtualizacao: '2018-10-10T00:00:00',
      },
    ];

    const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

    // Mock do useApiGatewayCvpInvoker com os dados do mockResponse
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: { sucessoBFF: true, entidade: mockResponse },
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi
        .fn()
        .mockResolvedValue({ sucessoBFF: true, entidade: mockResponse }),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    render(
      <QueryClientProvider client={queryClient}>
        <ComponentesPosVendaProvider>
          <ApiGatewayCvpProvider
            configure={{
              defaultAxiosClient: api,
              authMode: 'DEFAULT_GI',
              usernameKey: '@portal-eco:nomeAcesso',
              storageMode: 'LOCAL',
              operationPath: 'PortalEconomiario/',
            }}
          >
            <RenderPageWithThemeProviderAndRouter>
              <ConsultaSinistro />
            </RenderPageWithThemeProviderAndRouter>
          </ApiGatewayCvpProvider>
        </ComponentesPosVendaProvider>
      </QueryClientProvider>,
    );

    await waitFor(() => {
      const { getAllByText } = screen;
      const tableData = mockResponse[0];
      expect(
        getAllByText(tableData.numeroContrato.toString())[0],
      ).toBeInTheDocument();
      expect(getAllByText(tableData.coberturas)[0]).toBeInTheDocument();
      expect(getAllByText(tableData.causas)[0]).toBeInTheDocument();
      expect(getAllByText('19/03/2016')[0]).toBeInTheDocument();
      expect(getAllByText('10/10/2018')[0]).toBeInTheDocument();
    });
  });
});
