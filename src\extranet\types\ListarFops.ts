import { IResponseObterListaFopsAtivos } from 'main/features/Administracao/types/IFops';

export interface IUseListarFops {
  tipoFop?: string;
  dataToList?: IResponseObterListaFopsAtivos[];
  abrirFop: (fop: IResponseObterListaFopsAtivos) => void;
}

export interface IUseListarFopsReturn {
  listaFops: IResponseObterListaFopsAtivos[];
  handleFopClick: (item: IResponseObterListaFopsAtivos) => void;
}
