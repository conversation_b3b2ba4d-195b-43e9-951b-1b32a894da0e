export enum EnumselectRegraCalculo {
  aposentadoria = 'aposentadoria',
  contribuicaoMensal = 'contribuicaoMensal',
  contribuicaoAnual = 'contribuicaoAnual',
}

export enum EnumErrorMessage {
  error = 'Atenção: É obrigatório o preenchimento de todos os campos e o envio do FOP 064, constando as informações dos colaboradores e benefícios, conforme informações deste formulário.',
}

export enum EnumFormaPagamento {
  pagamentoAverbado = 'averbado',
  pagamentoInstituido = 'instituido',
  pagamentoPlanoInstituido = 'planoInstituido',
}

export enum EnumConfirmatorio {
  afirmativo = 'sim',
  negativo = 'nao',
}

export enum EnumTipoContribuicao {
  valorFixo = 'valorFixoContribuicao',
  valorBase = 'valorBaseContribuicao',
  outraForma = 'outraFormaContribuicao',
}
export enum EnumTipoBeneficios {
  rendaTemporaria = 'rendaTemporaria',
  prazoMinimo = 'prazoMinimoGarantido',
  PrazoCerto = 'rendaPorPrazoCerto',
  vitalicia = 'vitalicia',
  vitaliciareversivel = 'vitaliciareversivel',
}
export enum EnumCuidadoExtra {
  peculio = 'peculio',
  pensao = 'pensao',
  semCuidadoExtra = 'semCuidadoExtra',
}
export enum EnumliberacaoReserva {
  sugestao = 'sugestao',
  outraRegra = 'outraRegra',
  liberacaoTotal = 'liberacaoTotal',
  semLiberacao = 'semLiberacao',
}
export enum EnumRegraAporte {
  semAporte = 'semAporte',
  comAporte = 'comAporte',
  portabilidade = 'portabilidade',
}
export enum EnumTipoConcessao {
  aposentadoria = 'idadeAposentadoria',
  prazoContribuicao = 'prazoContribuicao',
}
export enum EnumDadosCobranca {
  boleto = 'boleto',
  debito = 'debito',
}
export enum EnumTipoPagamento {
  aporteUnico = 'aporteUnico',
  aporteMensal = 'mensal',
}
export enum EnumErrors {
  buscaArquivo = 'Ocorreu um erro ao buscar o arquivo.',
  download = 'Ocorreu um erro ao baixar o arquivo.',
  tamanoMaximo = 'Tamanho máximo do arquivo excedido.',
  erroFop = 'Erro ao registrar FOP.',
}

export enum InformacoesGerais {
  nomeEmpresa = 'Nome da Empresa',
  atividadePrincipal = 'Atividade Principal',
  CNPJ = 'CNPJ',
  numeroAgencia = 'Número da Agência',
  superintendenciaRegional = 'Superintendência Regional',
  nomeAgencia = 'Nome da Agência',
  filial = 'Filial',
  matriculaIndicador = 'Matrícula do Indicador',
  nomeIndicador = 'Nome do Indicador',
}

export enum DadosResponsaveis {
  nomeCompleto = 'Nome Completo',
  telefone = 'Telefone',
  EmailAviso = 'E-mail (Atenção: esse será o e-mail para o recebimento do estudo)',
  Email = ' E-mail',
}

export enum Botoes {
  ok = 'OK',
  limpar = 'Limpar',
  visualizar = 'Visualizar',
  enviar = 'Enviar',
}
