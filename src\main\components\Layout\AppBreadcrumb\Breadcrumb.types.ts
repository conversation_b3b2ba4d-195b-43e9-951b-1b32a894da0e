export interface IBreadcrumbsItemProps {
  matcher: {
    url: string;
    isExact: boolean;
  };
  name: string;
  mappedRoutes: TMappedRoutes;
  parentProps: {
    ActiveLinkComponent: React.ComponentType<TActiveLink>;
    LinkComponent: React.ComponentType<TBaseLink>;
    routeMatcherRegex?: string;
  };
}

export type TMapRouteFn = (url: string, match: unknown) => string | undefined;
export type TMappedRoutes = Record<string, string | TMapRouteFn>;

export interface IBreadcrumbsProps {
  mappedRoutes: TMappedRoutes;
  WrapperComponent: React.ComponentType<{ children: React.ReactNode }>;
  rootName?: string | ((url: string, match: unknown) => string);
}

export type TActiveLink = {
  light?: boolean;
  children: React.ReactNode;
};

export type TBaseLink = {
  children: React.ReactNode;
  to: string;
};
