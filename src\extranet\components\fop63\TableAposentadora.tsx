import * as DS from '@cvp/design-system/react';
import * as Const from 'extranet/types/ConstFop63';
import { IFop63Base } from 'extranet/types/InterfacesFop63/IFop63Base';
import { ValoresContribuicao } from 'extranet/types/InterfacesFop63/IInitialValues';
import { IValorContribuicao } from 'extranet/types/ITableValorContribuicao';
import Icon from 'main/components/Icon';
import RenderConditional from 'main/components/RenderConditional';
import Table from 'main/components/Table';
import { TextField } from 'main/features/Auth/components';
import masks from 'main/utils/masks';
import { TextAreaGrupos } from '../../features/fops/pages/styles';
import * as Enum from '../../types/enum';

export const TabelaAposentadoria: React.FC<IFop63Base> = ({
  setFieldValue,
  values,
  remove
}) => {
  return (
    <Table
      columns={[
        {
          name: Const.GRUPOS,
          selector: (row: IValorContribuicao) => row.descricaoGrupo,
          wrap: true,
          cell: (_row, index) => (
            <TextAreaGrupos
              placeholder={Const.DESCRICAO_GRUPOS}
              maxLength={38}
              value={values.valoresContribuicao[index]?.descricaoGrupo}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => {
                setFieldValue(
                  `valoresContribuicao.${index}.descricaoGrupo`,
                  e.target.value,
                )
              }}
            />
          ),
          grow: 1,
          center: true,
        },
        {
          width: '300px',
          name: (
            <div>
              <DS.Select
                label={Const.PARTICIPANTE}
                name="pagamentoContribuicaoParticipante"
                placeholder={Const.SELECIONE}
                value={values.pagamentoContribuicaoParticipante}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('pagamentoContribuicaoParticipante', value);
                }}
              >
                {Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO_PARTICIPANTE.map(
                  opcaoParticipante => (
                    <DS.Select.Item
                      key={opcaoParticipante.key}
                      value={opcaoParticipante.key}
                      text={opcaoParticipante.value}
                      selected={
                        opcaoParticipante.key ===
                        values.valorContribuicaoParticipante
                      }
                    />
                  ),
                )}
              </DS.Select>
            </div>
          ),
          selector: (row: IValorContribuicao) => row.tipoPagamentoContribuicao,
          wrap: true,
          cell: (row, index) => (
            <>
              <RenderConditional condition={
                values.pagamentoContribuicaoParticipante === 'valorFixo'}
              >
                <TextField
                  value={row.participante}
                  maxLength={16}
                  onChange={(
                    value: React.ChangeEvent<{
                      text: string;
                      value: string;
                    }>
                  ) => setFieldValue(
                    `valoresContribuicao.${index}.participante`,
                    masks.currencyInput.mask(value.target.value)
                  )} />
              </RenderConditional>
              <RenderConditional condition={
                values.pagamentoContribuicaoParticipante === 'porcentagemSalario'}>
                <TextField
                  value={row.participante}
                  onChange={(
                    value: React.ChangeEvent<{
                      text: string;
                      value: string;
                    }>
                  ) => setFieldValue(
                    `valoresContribuicao.${index}.participante`,
                    masks.percentage.mask(value.target.value)
                  )} />
              </RenderConditional>
            </>
          ),
          grow: 1,
          center: true,
        },
        {
          name: (
            <div
              style={{
                minWidth: '200px',
                whiteSpace: 'normal',
                wordBreak: 'break-word',
              }}
            >
              <DS.Select
                label={Const.EMPRESA}
                name="PagamentoContribuicaoEmpresa"
                placeholder={Const.SELECIONE}
                value={values.PagamentoContribuicaoEmpresa}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('PagamentoContribuicaoEmpresa', value);
                }}
              >
                {Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO_EMPRESA.map(
                  opcaoEmpresa => (
                    <DS.Select.Item
                      key={opcaoEmpresa.key}
                      value={opcaoEmpresa.key}
                      text={opcaoEmpresa.value}
                      selected={
                        opcaoEmpresa.key === values.valorContribuicaoEmpresa
                      }
                    />
                  ),
                )}
              </DS.Select>
            </div>
          ),
          selector: (row: IValorContribuicao) => row.ValorContribuicaoEmpresa,
          wrap: true,
          cell: (row, index) => (
            <>
              <RenderConditional condition={
                values.PagamentoContribuicaoEmpresa === 'porcentagemSalario'}>
                <TextField
                  type="text"
                  value={(row as unknown as ValoresContribuicao).empresa}
                  onChange={(
                    value: React.ChangeEvent<{
                      text: string;
                      value: string;
                    }>,
                  ) =>
                    setFieldValue(
                      `valoresContribuicao.${index}.empresa`,
                      masks.percentage.mask(value.target.value),
                    )
                  }
                />
              </RenderConditional>
              <RenderConditional condition={
                values.PagamentoContribuicaoEmpresa === 'valorFixo'}>
                <TextField
                  value={(row as unknown as ValoresContribuicao).empresa}
                  maxLength={16}
                  onChange={(
                    value: React.ChangeEvent<{
                      text: string;
                      value: string;
                    }>,
                  ) =>
                    setFieldValue(
                      `valoresContribuicao.${index}.empresa`,
                      masks.currency.mask(value.target.value),
                    )
                  }
                />
              </RenderConditional>
              <RenderConditional condition={
                values.PagamentoContribuicaoEmpresa === 'porcentagemContribuicaoParticipante'}>
                <TextField
                  type="text"
                  value={masks.percentage.mask(values.valoresContribuicao[index]?.empresa)}
                  maxLength={16}
                  onChange={(
                    value: React.ChangeEvent<{
                      text: string;
                      value: string;
                    }>,
                  ) =>
                    setFieldValue(
                      `valoresContribuicao.${index}.empresa`,
                      masks.percentage.mask(value.target.value)
                    )
                  }
                />
              </RenderConditional>
            </>
          ),
          grow: 1,
          center: true,
        },
        {
          name: Const.VALOR_MAXIMO_EMPRESA,
          selector: (row: IValorContribuicao) => row.valorMaximoContribuicao,
          wrap: true,
          cell: (row, index) => (
            <>
              <TextField
                type="text"
                value={(row as unknown as ValoresContribuicao).valorContribuicao}
                maxLength={16}
                onChange={(e: any) => {
                  const masked = masks.currencyInput.mask(e.target.value);
                  setFieldValue(
                    `valoresContribuicao.${index}.valorContribuicao`,
                    masked,
                  );
                }}
              />
              <RenderConditional condition={values.valoresContribuicao.length >= 2}>

                <DS.IconButton
                  type="button"
                  variant="outlined"
                  onClick={() => {
                    if (values.valoresContribuicao.length > 1) {
                      if (remove) {
                        remove(index)
                      }
                    }
                  }}
                >
                  <Icon name="trash" />
                </DS.IconButton>
              </RenderConditional>
            </>
          ),
          grow: 1,
          width: '350px',
          center: true,
        },
      ]}
      data={
        values.valoresContribuicao.map((item: any) => ({
          ...item,
          ValorContribuicaoEmpresa: item.ValorContribuicaoEmpresa ?? '',
          valorMaximoContribuicao: item.valorMaximoContribuicao ?? '',
          tipoPagamentoContribuicao: item.tipoPagamentoContribuicao ?? '',
        })) as IValorContribuicao[]
      }
    />
  );
};
