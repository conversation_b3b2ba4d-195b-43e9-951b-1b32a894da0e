import { formatarData } from 'main/utils';
import { IRelatorioSolicitacaoFop } from 'painelAdministracao/types/IRelatorioSolicitacaoFop';
import { IRelatorioSolicitacaoFopFactory } from 'painelAdministracao/types/IRelatorioSolicitacaoFopFactory';

export const mapearRelatorioSolicitacaoFactory = (
  solicitacoes: IRelatorioSolicitacaoFop[],
): IRelatorioSolicitacaoFopFactory[] => {
  return solicitacoes.map(solicitacao => {
    return {
      ...solicitacao,
      dataSolicitacao: formatarData(solicitacao.dataSolicitacao),
      servicoSelecionado: (
        <div>
          {`FOP ${solicitacao.codigoFop} ${solicitacao.numeroVersaoFop} - ${solicitacao.servicoSelecionado}`}
        </div>
      ),
      descricaoMotivo: <div>{solicitacao.descricaoMotivo}</div>,
    };
  });
};
