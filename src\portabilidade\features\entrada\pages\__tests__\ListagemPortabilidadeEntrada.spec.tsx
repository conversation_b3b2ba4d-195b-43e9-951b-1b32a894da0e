import { render, screen } from '@testing-library/react';
import ThemeProvider from 'main/components/ThemeProvider';
import { Card } from '@cvp/design-system/react';
import Table from 'main/components/Table';
import TableFilter from 'main/components/Table/TableFilter/TableFilter';
import {
  FilterTypes,
  IFilterOption,
} from 'main/types/TableFilters/IFilterOption';
import masks from 'main/utils/masks';
import { IListaPortabilidadeEntrada } from '../../types/IListaPortabilidadeEntrada';
import { TableColumn } from 'react-data-table-component';

describe('ListagemPortabilidadeEntrada', () => {
  it('deve renderizar lista de portabilidade de entrada', () => {
    const mockFilterOptions: IFilterOption[] = [
      {
        key: 'numeroCpf',
        value: 'CPF/CNPJ',
        type: FilterTypes.TEXT,
        unmask: masks.cpf.unmask,
      },
      {
        key: 'numeroPortabilidade',
        value: 'Portabilidade',
        type: FilterTypes.TEXT,
      },
      { key: 'indicador', value: 'Indicador', type: FilterTypes.TEXT },
    ];

    const mockRetornoConvertido: IListaPortabilidadeEntrada[] = [
      {
        nomeCliente: 'José Francisco da Silva',
        numeroCpf: '12345678965',
        agencia: '123',
        numeroPortabilidade: '123456',
        indicador: '123456',
        status: 'ATIVO',
        valor: 1456.99,
        valorPortabilidadeFormatado: '1456.99',
        statusConclusao: '60',
      },
    ];

    const mockColunas: TableColumn<IListaPortabilidadeEntrada>[] = [
      {
        name: 'Portabilidade',
        selector: (row: IListaPortabilidadeEntrada) => row.numeroPortabilidade,
        cell: () => [],
        width: '200px',
      },
      {
        name: 'Nome do cliente',
        selector: (row: IListaPortabilidadeEntrada) => row.nomeCliente,
        width: '190px',
      },
      {
        name: 'CPF do Cliente',
        selector: (row: IListaPortabilidadeEntrada) => row.numeroCpf,
        width: '150px',
      },
      {
        name: 'Valor',
        selector: (row: IListaPortabilidadeEntrada) => row.valorPortabilidadeFormatado,
        width: '150px',
      },
      {
        name: 'Agência',
        selector: (row: IListaPortabilidadeEntrada) => row.agencia,
        width: '100px',
      },
      {
        name: 'Indicador',
        selector: (row: IListaPortabilidadeEntrada) => row.indicador,
      },
      {
        name: 'Status',
        selector: (row: IListaPortabilidadeEntrada) => row.status,
        width: '120px',
      },
      {
        name: '% de Conclusão',
        selector: (row: IListaPortabilidadeEntrada) => row.statusConclusao,
        cell: () => [],
        width: '180px',
      },
    ];

    render(
      <ThemeProvider>
        <Card>
          <Card.Content>
            <TableFilter
              dataToFilter={mockRetornoConvertido ?? []}
              filterTextPartial
              filterOptions={mockFilterOptions}
            >
              {filteredData => (
                <Table
                  noHeader
                  responsive
                  data={filteredData}
                  columns={mockColunas}
                  noDataComponent="Não há dados para exibir."
                  pagination
                  paginationPerPage={10}
                  paginationComponentOptions={{
                    rowsPerPageText: 'Items por página',
                    rangeSeparatorText: 'de',
                  }}
                />
              )}
            </TableFilter>
          </Card.Content>
        </Card>
      </ThemeProvider>,
    );

    expect(screen.getByText(/Items por página/i)).toBeInTheDocument();
  });
});
