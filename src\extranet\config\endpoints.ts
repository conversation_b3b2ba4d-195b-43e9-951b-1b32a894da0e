import { basePath } from 'config/httpRequest';

export const obterBasePathArquivosExtranet = (): string =>
  '//statics.caixavidaeprevidencia.com.br/extranet';

export const obterUrlDoArquivoDesempenhoFundosInvestimento = (): string =>
  `${obterBasePathArquivosExtranet()}/Previdencia/Fundos_invest_prev/Relatorio_Gestao.pdf`;

export const obterUrlArquivoCarteiraDiaria = (): string =>
  `${obterBasePathArquivosExtranet()}/Previdencia/Fundos_invest_prev/Carteiras_Diarias_CVP.pdf`;

export const obterUrlArquivoPortifolioFundosInvestimentoPF = (): string =>
  `${obterBasePathArquivosExtranet()}/Previdencia/Fundos_invest_prev/Portfolio_Novos_Fundos.pdf`;

export const obterUrlArquivoPortifolioFundosInvestimentoPJ = (): string =>
  `${obterBasePathArquivosExtranet()}/Previdencia/Fundos_invest_prev/fundos_PJ.pdf`;

export const obterUrlImagemGraficoComposicaoCarteiras = (): string =>
  `${obterBasePathArquivosExtranet()}/Previdencia/Fundos_invest_prev/grafico_composicao_carteiras.jpg`;

export const ENDPOINT_PROTOCOLO_FOP = {
  CONSULTA_OCORRENCIA: `/registro-ocorrencias/consultar-ocorrencia`,
};
export const obterFOP223DataProtocolo = (): string =>
  '/extranet/fop-223/dados-protocolo';

export const obterFOP223AcessoNegado = (): string => '/acesso-negado';


export const ENDPOINT_FOP = {
  REGISTRAR_SOLICITACAO: 'PECO_RegistrarSolicitacaoFOP',
};

export const EnviarArquivoSolicitacaoFOP = (): string => {
  return '/PortalEconomiario/PECO_EnviarArquivoSolicitacaoFOP';
};
const URLS_PECOS_MATRIZ = Object.values(ENDPOINT_FOP).map(ENDPOINT_FOP => basePath + ENDPOINT_FOP);
export const URLS_MATRIZ_INTERCEPTOR = [
  ...URLS_PECOS_MATRIZ,
  EnviarArquivoSolicitacaoFOP(),
];


