import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ModalAlertaAlteracaoTributaria from 'previdencia/features/AlteracaoRegimeTributario/components/ModalAlertaAlteracaoTributaria';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';

vi.mock('/src/assets/icons/document_paper.svg?react', () => ({
  default: () => 'SvgMock',
}));
vi.mock('/src/assets/icons/user.svg?react', () => ({
  default: () => 'SvgMock',
}));
vi.mock('/src/assets/icons/document_back.svg?react', () => ({
  default: () => 'SvgMock',
}));
vi.mock('/src/assets/icons/financial_wallet.svg?react', () => ({
  default: () => 'SvgMock',
}));
vi.mock('/src/assets/icons/financial_moneybag.svg?react', () => ({
  default: () => 'SvgMock',
}));
const mockDados = {
  titular: 'Jhon Doe',
  certificado: '198574',
  modalidade: 'PGBL / Regressivo',
  formaPagamento: 'Mensal',
  valor: 'R$ 4500,00',
};

describe('Renderizar o Componente ModalAlertaAlteracaoTributária', () => {
  const onCloseMock = vi.fn();
  const alteraRegimeMock = vi.fn();
  const tipoTributacaoMock = 'TR';

  beforeEach(() => {
    onCloseMock.mockClear();
  });
  const queryClient = new QueryClient();

  it('deve renderizar o modal com os dados do mock', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <RenderPageWithThemeProviderAndRouter>
          <ModalAlertaAlteracaoTributaria
            open
            onClose={onCloseMock}
            tipoTributacao={tipoTributacaoMock}
            alteraRegime={alteraRegimeMock}
            dadosModal={mockDados}
          />
        </RenderPageWithThemeProviderAndRouter>
      </QueryClientProvider>,
    );

    expect(screen.getByText('Jhon Doe')).toBeInTheDocument();
    expect(screen.getByText('198574')).toBeInTheDocument();
    expect(screen.getByText('PGBL / Regressivo')).toBeInTheDocument();
    expect(screen.getByText('Mensal')).toBeInTheDocument();
    expect(screen.getByText('R$ 4500,00')).toBeInTheDocument();
  });
});
