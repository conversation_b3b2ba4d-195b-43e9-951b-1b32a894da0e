import React from 'react';
import { Breadcrumb } from '@cvp/design-system/react';
import { useNavigate } from 'react-router-dom';
import { BreadcrumbActive } from './styles';
import { TActiveLink, TBaseLink } from './Breadcrumb.types';

export const BreadcrumbBaseLink: React.FC<TBaseLink> = ({ children, to }) => {
  const navigate = useNavigate();
  return (
    <Breadcrumb.Item
      data-testid="breadcrumb-link"
      onClick={() => {
        navigate(to);
      }}
      value={children}
    />
  );
};

export const BreadcrumbActiveLink: React.FC<TActiveLink> = ({
  children,
  light,
}) => (
  <BreadcrumbActive
    data-testid="breadcrumb-active"
    value={children}
    light={light}
  />
);
