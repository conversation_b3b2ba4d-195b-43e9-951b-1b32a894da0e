import { useContext } from 'react';
import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { AppContext } from 'main/contexts/AppContext';
import { ResponseConsultarDadosBasicos } from 'previdencia/features/SimulacaoRenda/types/dadosBasicos';
import * as ConsultarDadosBasicosApi from 'previdencia/features/SimulacaoRenda/services/consultarDadosBasicos.api';

const useConsultarDadosBasicos = (): UseQueryResult<
  ResponseConsultarDadosBasicos | undefined
> => {
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQuery({
    queryKey: ['prev-consultar-dados-basico', numCertificado],
    queryFn: () =>
      ConsultarDadosBasicosApi.consultarDadosBasicos(cpfCnpj, numCertificado),
    gcTime: reactQueryCacheDuration(),
    refetchOnWindowFocus: false,
    retry: false,
  });
};

export default useConsultarDadosBasicos;
