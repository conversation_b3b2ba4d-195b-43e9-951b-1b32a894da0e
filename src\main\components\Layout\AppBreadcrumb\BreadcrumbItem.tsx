import React from 'react';
import { Link } from 'react-router-dom';
import { getTernaryResult, tryGetValueOrDefault } from 'main/utils/conditional';
import { IBreadcrumbsItemProps } from './Breadcrumb.types';
import { isDefined } from './Breadcrumb.utils';
import { breadCrumbItemNameFactory } from './BreadcrumbItem.factory';
import RenderConditional from 'main/components/RenderConditional';
import { PATHS } from 'main/constants/paths';

const BreadcrumbItem: React.FC<IBreadcrumbsItemProps> = ({
  matcher,
  name,
  mappedRoutes,
  parentProps,
}) => {
  const { ActiveLinkComponent, LinkComponent } = parentProps;

  let matchedRouteName: string | null = breadCrumbItemNameFactory(
    matcher.url,
    mappedRoutes,
  ) as string;

  matchedRouteName = getTernaryResult(
    matchedRouteName !== null,
    tryGetValueOrDefault([matchedRouteName], name),
    null,
  );

  if (!isDefined(matchedRouteName)) return null;

  return (
    <>
      <RenderConditional condition={matcher.isExact}>
        <ActiveLinkComponent light={matcher.url === PATHS.CLIENTE}>
          {matchedRouteName}
        </ActiveLinkComponent>
      </RenderConditional>

      <RenderConditional condition={!matcher.isExact}>
        <LinkComponent to={matcher.url}>
          <Link to={tryGetValueOrDefault([matcher.url], '')}>
            {matchedRouteName}
          </Link>
        </LinkComponent>
      </RenderConditional>
    </>
  );
};

export default BreadcrumbItem;
