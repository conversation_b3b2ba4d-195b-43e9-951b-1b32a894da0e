import {
  DadosArquivo,
  IResponseObterListaFopsAtivos,
} from 'main/features/Administracao/types/IFops';
import { TListaAgrupadaPorTipo } from 'painelAdministracao/types/ListagemTypes';

export interface IAdmProps {
  modalEditar: boolean;
  setModalEditar: (value: boolean) => void;
  modalEditarMotivo: boolean;
  setModalEditarMotivo: (value: boolean) => void;
  handleFecharModal: () => void;
  dadosAgrupadosPorTipo: TListaAgrupadaPorTipo[];
  setDadosAgrupadosPorTipo: (value: TListaAgrupadaPorTipo[]) => void;
  fopEditar: IResponseObterListaFopsAtivos | undefined;
  setFopEditar: (value: IResponseObterListaFopsAtivos | undefined) => void;
  documentoEditar?: DadosArquivo;
  handleEditarFop: (
    fop: IResponseObterListaFopsAtivos,
    documento?: DadosArquivo,
  ) => void;
  handleEditarMotivoFop: (
    fop: IResponseObterListaFopsAtivos,
    documento?: DadosArquivo,
  ) => void;
  handleAdicionarFop: (fop: TListaAgrupadaPorTipo) => void;
}

export interface IAdmContextProviderProps {
  children: React.ReactNode | undefined;
}
