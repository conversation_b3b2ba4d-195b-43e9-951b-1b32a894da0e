import { useToast } from 'main/hooks/useToast';
import {
  checkIfAllItemsAreTrue,
  getTernaryResult,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import * as CONSTS from 'painelAdministracao/constants/constants';
import { useSalvarMotivo } from 'painelAdministracao/hooks/useSalvarMotivo';
import { IUseHandleAtualizarFopMotivoProps } from 'painelAdministracao/types/IEditarMotivoFop';
import { IUseHandleAtualizarFopMotivoResponse } from 'painelAdministracao/types/IUseHandleAtualizarFopMotivoResponse';
import { useEffect } from 'react';

export const useHandleAtualizarFopMotivo: IUseHandleAtualizarFopMotivoProps = (
  formikMotivo,
  fopEditar,
  handleFecharModalMotivo,
): IUseHandleAtualizarFopMotivoResponse => {
  const { toastError, toastSuccess } = useToast();
  const { fetchDataMotivoFop, loadingMotivoFop, response } = useSalvarMotivo();

  const validarMotivoSolicitacaoAtivo = (value: boolean): string => {
    if (!value) return '';

    const motivoAtivo = formikMotivo?.values?.motivoSolicitacaoAtivo;
    const descricao = tryGetValueOrDefault(
      [formikMotivo?.values?.descricaoMotivoSolicitacao],
      '',
    );
    const descricaoEditada = tryGetValueOrDefault(
      [fopEditar?.descricaoMotivoSolicitacao],
      '',
    );

    return getTernaryResult(
      motivoAtivo && !!descricao.trim(),
      descricao,
      String(descricaoEditada),
    );
  };

  const atualizarFopMotivo = (): void => {
    fetchDataMotivoFop({
      CodigoFop: tryGetValueOrDefault([Number(fopEditar?.codigo)], 0),
      MotivoSolicitacaoAtivo: formikMotivo.values.motivoSolicitacaoAtivo,
      DescricaoMotivoSolicitacao: validarMotivoSolicitacaoAtivo(
        formikMotivo.values.motivoSolicitacaoAtivo,
      ),
    });
  };

  const validarResposta = (): void => {
    if (
      checkIfAllItemsAreTrue([!!response?.sucessoGI, !!response?.sucessoBFF])
    ) {
      toastSuccess(CONSTS.FEEDBACKMOTIVO.success);
      handleFecharModalMotivo();
      formikMotivo.setValues(CONSTS.INITIAL_FORMIK_MOTIVO_STATE);
    } else if (response?.mensagens?.length) {
      const mensagens = response.mensagens
        .map(item => item.descricao)
        .join(', ');
      toastError(mensagens);
    }
  };

  useEffect(() => {
    validarResposta();
  }, [response]);

  return {
    loadingMotivoFop,
    atualizarFopMotivo,
  };
};
