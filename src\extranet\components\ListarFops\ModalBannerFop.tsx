import React from 'react';
import Modal from 'main/components/Modal';
import * as S from './styles';
import { TModalFopProps } from 'main/features/Administracao/types/IFops';

export const ModalBannerFop: React.FC<TModalFopProps> = ({
  onClose,
  open,
  src,
}) => {
  if (!open) return null;

  return (
    <Modal onClose={() => onClose && onClose()} open={open}>
      <S.Banner src={src} />
    </Modal>
  );
};
