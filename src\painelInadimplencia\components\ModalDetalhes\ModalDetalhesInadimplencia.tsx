import { Display, Grid, Text } from '@cvp/design-system/react';
import React from 'react';

import Icon from 'main/components/Icon';
import RenderConditional from 'main/components/RenderConditional';
import SkeletonLoading from 'main/components/SkeletonLoading';
import { formatarDataAmigavel } from 'main/utils';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import masks from 'main/utils/masks';
import { tryGetMonetaryValueOrDefault } from 'main/utils/money';
import * as CONSTS from 'painelInadimplencia/constants/constants';
import useSegmento from 'painelInadimplencia/hooks/useSegmento';
import { ModalProps } from 'painelInadimplencia/types/ModalDetalhesInadimplencia';
import * as UTILS from 'painelInadimplencia/utils/PainelInadimplencia';
import {
  ColumChildFlex,
  ModalDetalhesInadimplenciaStyled,
  SpanStyled,
} from './styles';

const ModalDetalhesInadimplencia: React.FC<ModalProps> = ({
  open,
  dados,
  pagamentoDetalhado,
  isLoading,
  onClose,
}) => {
  const { validarSegmentoRota } = useSegmento();

  const periodicidadePagamento = UTILS.getPeriodicidadePagamentoLabel(
    tryGetValueOrDefault([dados?.opcaoPagamento], ''),
  );

  return (
    <ModalDetalhesInadimplenciaStyled show={open} onClose={onClose}>
      <RenderConditional condition={!!isLoading}>
        <SkeletonLoading blocks={1} lines={10} />
      </RenderConditional>

      <RenderConditional condition={!isLoading}>
        <Grid>
          <Grid.Item xs={1 / 2}>
            <Display alignItems="center">
              <Icon name="user" />
              <span>
                <Text variant="body02-md" align="left" color="text-dark">
                  Nome
                </Text>
                <Text variant="body02-xs" align="left">
                  {tryGetValueOrDefault([dados?.nomeCliente], '-')}
                </Text>
              </span>
            </Display>
          </Grid.Item>
          <Grid.Item xs={1 / 2}>
            <Display alignItems="center">
              <Icon name="documentFront" />
              <span>
                <Text variant="body02-md" align="left" color="text-dark">
                  CPF
                </Text>
                <Text variant="body02-xs" align="left">
                  {masks.cpf.mask(dados?.cpf?.toString())}
                </Text>
              </span>
            </Display>
          </Grid.Item>
          <Grid.Item xs={1 / 2}>
            <Display alignItems="center">
              <Icon name="documentPaper" />
              <span>
                <Text variant="body02-md" align="left" color="text-dark">
                  Nome do Produto
                </Text>
                <Text variant="body02-xs" align="left">
                  {tryGetValueOrDefault([dados?.produto], '-')}
                </Text>
              </span>
            </Display>
          </Grid.Item>
          <Grid.Item xs={1 / 2}>
            <Display alignItems="center">
              <Icon name="waiting" />
              <span>
                <Text variant="body02-md" align="left" color="text-dark">
                  Periodicidade do produto
                </Text>
                <Text variant="body02-xs" align="left">
                  {tryGetValueOrDefault([periodicidadePagamento], '-')}
                </Text>
              </span>
            </Display>
          </Grid.Item>
          <Grid.Item xs={1 / 2}>
            <Display alignItems="center">
              <Icon name="financialWallet" />
              <span>
                <Text variant="body02-md" align="left" color="text-dark">
                  Opção de Pagamento
                </Text>
                <Text variant="body02-xs" align="left">
                  {tryGetValueOrDefault([dados?.formaPagamento], '-')}
                </Text>
              </span>
            </Display>
          </Grid.Item>
          <Grid.Item xs={1 / 2}>
            <Display alignItems="center">
              <Icon name="financialMoneyBag" />
              <span>
                <Text variant="body02-md" align="left" color="text-dark">
                  <RenderConditional
                    condition={validarSegmentoRota(CONSTS.SEGMENTO.VIDA)}
                  >
                    Valor de prêmio
                  </RenderConditional>
                  <RenderConditional
                    condition={validarSegmentoRota(CONSTS.SEGMENTO.PREV)}
                  >
                    Valor de Contribuição
                  </RenderConditional>
                </Text>
                <Text variant="body02-xs" align="left">
                  {tryGetValueOrDefault(
                    [tryGetMonetaryValueOrDefault(dados?.valorProposta)],
                    '-',
                  )}
                </Text>
              </span>
            </Display>
          </Grid.Item>
          <Grid.Item xs={1 / 2}>
            <Display alignItems="center">
              <Icon name="calendar" />
              <span>
                <Text variant="body02-md" align="left" color="text-dark">
                  Último mês referência da parcela inadimplente
                </Text>
                <Text variant="body02-xs" align="left">
                  {tryGetValueOrDefault(
                    [formatarDataAmigavel(dados?.ultimaCobranca)],
                    '-',
                  )}
                </Text>
              </span>
            </Display>
          </Grid.Item>
          <Grid.Item xs={1 / 2}>
            <Display alignItems="center">
              <Icon name="waiting" />
              <span>
                <Text variant="body02-md" align="left" color="text-dark">
                  Tempo de vida do contrato (em mês);
                </Text>
                <Text variant="body02-xs" align="left">
                  {tryGetValueOrDefault([dados?.tempoContrato], '-')}
                </Text>
              </span>
            </Display>
          </Grid.Item>
          <Grid.Item xs={1 / 2}>
            <Display alignItems="center">
              <Icon name="filterEqualize" />
              <span>
                <Text variant="body02-md" align="left" color="text-dark">
                  Beneficiários Indicados?
                </Text>
                <Text variant="body02-xs" align="left">
                  {tryGetValueOrDefault([dados?.beneficiariosExistentes], '-')}
                </Text>
              </span>
            </Display>
          </Grid.Item>

          <RenderConditional condition={dados?.segmento === CONSTS.PREV}>
            <Grid.Item xs={1 / 2}>
              <Display alignItems="center">
                <Icon name="filterEqualize" />
                <span>
                  <Text variant="body02-md" align="left" color="text-dark">
                    Comunicabilidade (sim ou não).
                  </Text>
                  <Text variant="body02-xs" align="left">
                    {tryGetValueOrDefault([dados?.comunicabilidade], '-')}
                  </Text>
                </span>
              </Display>
            </Grid.Item>
          </RenderConditional>
        </Grid>

        <RenderConditional
          condition={
            dados?.formaPagamento === CONSTS.TIPOS_FORMA_PAGAMENTO.DEBITO
          }
        >
          <Display alignItems="center">
            <Icon name="bank" />
            <span>
              <Text variant="body02-md" align="left" color="text-dark">
                Agência
              </Text>
              <Text variant="body02-xs" align="left">
                {tryGetValueOrDefault([pagamentoDetalhado?.codigoAgencia], 0)}
              </Text>
            </span>
            <span>
              <Text variant="body02-md" align="left" color="text-dark">
                Operação
              </Text>
              <Text variant="body02-xs" align="left">
                {tryGetValueOrDefault(
                  [pagamentoDetalhado?.codigoOperacaoCobranca],
                  0,
                )}
              </Text>
            </span>
            <span>
              <Text variant="body02-md" align="left" color="text-dark">
                Conta
              </Text>
              <Text variant="body02-xs" align="left">
                {tryGetValueOrDefault(
                  [pagamentoDetalhado?.numeroContaCobranca],
                  '0',
                )}
              </Text>
            </span>
          </Display>
        </RenderConditional>

        <RenderConditional
          condition={
            dados?.formaPagamento === CONSTS.TIPOS_FORMA_PAGAMENTO.CREDITO
          }
        >
          <Display alignItems="center">
            <Icon name="creditCard" />
            <span>
              <Text variant="caption-02" align="left">
                Cartão de Crédito
              </Text>
              <Text variant="body02-xs" align="left">
                xxxx.xxxx.xxxx.
                {tryGetValueOrDefault(
                  [pagamentoDetalhado?.numeroCartaoCredito.toString()],
                  'xxxx',
                )}
              </Text>
            </span>
          </Display>
        </RenderConditional>

        <RenderConditional
          condition={
            dados?.formaPagamento === CONSTS.TIPOS_FORMA_PAGAMENTO.BOLETO
          }
        >
          <Display alignItems="center">
            <Icon name="home" />
            <ColumChildFlex>
              <div>
                <SpanStyled>
                  <Text variant="caption-02" align="left">
                    Endereço
                  </Text>
                  <Text variant="body02-xs" align="left">
                    {tryGetValueOrDefault(
                      [pagamentoDetalhado?.logradouro],
                      '-',
                    )}
                  </Text>
                </SpanStyled>
                <SpanStyled>
                  <Text variant="caption-02" align="left">
                    Bairro
                  </Text>
                  <Text variant="body02-xs" align="left">
                    {tryGetValueOrDefault([pagamentoDetalhado?.bairro], '-')}
                  </Text>
                </SpanStyled>
                <SpanStyled>
                  <Text variant="caption-02" align="left">
                    Cidade
                  </Text>
                  <Text variant="body02-xs" align="left">
                    {tryGetValueOrDefault([pagamentoDetalhado?.cidade], '-')}
                  </Text>
                </SpanStyled>
              </div>
              <div>
                <SpanStyled>
                  <Text variant="caption-02" align="left">
                    UF
                  </Text>
                  <Text variant="body02-xs" align="left">
                    {tryGetValueOrDefault([pagamentoDetalhado?.estado], '-')}
                  </Text>
                </SpanStyled>
              </div>
            </ColumChildFlex>
          </Display>
        </RenderConditional>
      </RenderConditional>
    </ModalDetalhesInadimplenciaStyled>
  );
};

export default ModalDetalhesInadimplencia;
