import {
  FilterTypes,
  IFilterOption,
} from 'main/types/TableFilters/IFilterOption';
import { formatarData } from 'main/utils';
import * as ENUM_TYPES from 'registroOcorrenciaASC/types/enum';
import * as S from 'registroOcorrenciaASC/features/consultarOcorrencia/pages/ConsultarOcorrencia/styles';
import { IrParaDetalhesOcorrenciaParams } from 'registroOcorrenciaASC/features/consultarOcorrencia/types/ConsultarOcorrencia';
import { TableColumn } from 'react-data-table-component';
import { IHistoricoSolicitacaoRetorno } from '../types/ConsultarOcorrenciaResponse';

export const DEFAULT_VALUE = {
  CODIGO_NIVEL: 2,
  DESCRICAO_CANAL: 'Web - CAIXA',
  CLASSIFICACAO: 'Resposta',
};

export const COLUNAS_TABELA_HISTORICO_SOLICITACAO = (
  irParaDetalhesOcorrencia: ({
    numeroSolicitacao,
    cpfCnpj,
  }: IrParaDetalhesOcorrenciaParams) => void,
): TableColumn<IHistoricoSolicitacaoRetorno>[] => [
  {
    name: 'Protocolo',
    selector: row => row.numeroProtocolo,
    center: true,
    minWidth: '170px',
  },
  {
    name: 'Solicitação',
    selector: row => row.numeroSolicitacao,
    center: true,
    minWidth: '150px',
    cell: row => (
      <S.TableLink
        variant="body02-sm"
        onClick={() =>
          irParaDetalhesOcorrencia({
            numeroSolicitacao: row.numeroSolicitacao,
            cpfCnpj: row.numeroCpfCnpjCliente,
            numeroContrato: row.numeroContrato,
          })
        }
      >
        {row.numeroSolicitacao}
      </S.TableLink>
    ),
  },
  {
    name: 'Nome Cliente',
    selector: row => row.nomeCliente,
    center: true,
    wrap: true,
    minWidth: '150px',
  },
  {
    name: 'Situação',
    selector: row => row.statusSolicitacao,
    center: true,
    wrap: true,
  },
  {
    name: 'Assunto',
    selector: row => row.descricaoAssunto,
    center: true,
    wrap: true,
    minWidth: '200px',
  },
  {
    name: 'Segmento',
    selector: row => row.descricaoSegmento,
    center: true,
  },
  {
    name: 'Abertura',
    selector: row => row.dataCriacao,
    center: true,
    minWidth: '130px',
    cell: (row: IHistoricoSolicitacaoRetorno) => formatarData(row.dataCriacao),
  },
];

export const FILTRO_OPCOES_TABELA_HISTORICO_SOLICITACAO: IFilterOption[] = [
  {
    key: 'descricaoSegmento',
    value: 'Segmento',
    type: FilterTypes.TEXT,
  },
  {
    key: 'numeroProtocolo',
    value: 'Protocolo',
    type: FilterTypes.TEXT,
  },
  {
    key: 'numeroSolicitacao',
    value: 'Solicitação',
    type: FilterTypes.TEXT,
  },
];

export const NAO_HA_DADOS_TABELA = 'Não há dados para serem exibidos.';

export const TEXTOS_DETALHES_OCORRENCIA = {
  NUMERO_PROTOCOLO: 'Número de Protocolo',
  NUMERO_SOLICITACAO: 'Número da Solicitação',
  ASSUNTO: 'Assunto',
  CPF_CNPJ: 'CPF/CNPJ do Cliente',
  INTERLOCUTOR: 'Interlocutor',
  DATA_REGISTRO: 'Data de Registro',
  SITUACAO: 'Situação',
  CANAL_ABERTURA: 'Canal de Abertura',
  MENSAGEM: 'Mensagem',
};

export const TEXTOS_COMPLEMENTOS = {
  LABEL_DATA_CADASTRO: 'Data de Cadastro:',
  LABEL_COMPLEMENTO: 'Complemento:',
  MSG_NENHUM_COMPLEMENTO: 'Nenhum complemento adicionado à esta solicitação.',
};

export const TEXTOS_ANEXOS = {
  LABEL_ARQUIVO: 'Arquivo:',
  LABEL_TAMANHO: 'Tamanho:',
  LABEL_DATA: 'Data:',
  MSG_NENHUM_ANEXO: 'Nenhum anexo adicionado à esta solicitação.',
};

export const TEXTOS_ADICIONAR_COMPLEMENTO = {
  DESCRICAO: `Se necessário, preencha o formulário abaixo e clique em
  'Complementar' para adicionar informações à solicitação.`,
  LABEL_TEXTAREA: 'Complemento*',
  CHARACTERES_RESTANTES_TEXTAREA: 'Caracteres restantes:',
  TAMANHO_MAX_ANEXO: 'Tamanho máximo do arquivo excedido.',
  MSG_COMPLEMENTO_ADICIONADO: 'Novo complemento adicionado com sucesso.',
};

export const INITIAL_FORMIK_STATE_DETALHES_OCORRENCIA = {
  conteudoTextarea: '',
  arquivoAnexo: new DataTransfer().files,
  quantidadeCaracteresTextarea:
    ENUM_TYPES.DefaultValue.QTD_MAX_CARACTERES_TEXTAREA_REGISTRAR_COMPLEMENTO,
};

export const VALIDATION_DEFAULT_MESSAGE_SCHEMA = {
  CAMPO_OBRIGATORIO: 'Campo obrigatório.',
};

export const STATUS_SOLICITACAO_LEITURA = [
  'Resolvida',
  'Cancelada',
  'Closed',
  'Canceled',
];

export const STATUS_SOLICITACAO_CONCLUSAO = ['Resolvida', 'Closed'];

export const FORM_CONSULTA_OCORRENCIA_ERRORS = {
  CPF_CNPJ: 'Valor inválido!',
  PROTOCOLO: 'Valor inválido!',
  DATE: 'Data inválida',
};

export const CONFIG_HORA = {
  HORA_INICIAL: 0,
  HORA_FINAL: 23,
  MIN_FINAL: 59,
  SEG_FINAL: 59,
};

export const TEXTOS_OCORRENCIAS = {
  INFORME_PERIODO:
    'Informe um período e o CPF/CNPJ do cliente ou número de protocolo para realizar a consulta',
};
