import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { SalvarHtmlPlugin } from 'main/components/RichTextEditor/SalvarHtmlPlugin';
import {
  ContentEditable,
  EditorWrapper,
} from 'main/components/RichTextEditor/styles';
import { ToolbarPlugin } from 'main/components/RichTextEditor/ToolbarPlugin';
import { editorConfig } from 'main/constants/tipoRichTextEditor';
import { IRichTextEditor } from 'main/types/IRichTextEditor';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import React from 'react';

const RichTextEditor: React.FC<IRichTextEditor> = ({
  handleDescricao,
  motivo,
}) => {
  return (
    <LexicalComposer
      initialConfig={editorConfig(tryGetValueOrDefault([motivo], ''))}
    >
      <EditorWrapper>
        <ToolbarPlugin />
        <RichTextPlugin
          contentEditable={<ContentEditable />}
          placeholder={<></>}
          ErrorBoundary={({ children }) => <>{children}</>}
        />
        <SalvarHtmlPlugin onSave={html => handleDescricao(html)} />
        <HistoryPlugin />
      </EditorWrapper>
    </LexicalComposer>
  );
};

export default RichTextEditor;
