export const IDENTIFICAR_STRING_VALOR_MONETARIO = /R\$\s*\d+(?:\.\d{1,2})?/;
export const STRING_SEM_CARACTERES_ESPECIAIS_E_ESPACOS = /[^a-zA-Z0-9]+/g;
export const FORMATA_CASAS_DECIMAIS = /\B(?=(\d{3})+(?!\d))/g;
export const FORMATA_EMAIL = /[^a-zA-Z0-9._+-@]/g;
export const REMOVE_CARACTERES_EMAIL = /[<>,:;?/]/g;
export const MAPEIA_STRING_COM_PARAMETROS_DINAMICOS = /:[^\s/]+/g;
export const CONCAT_IDENTIFICA_VALOR_EM_URL = '([\\w-]+)';
export const REMOVE_TAG_SPAN = /<span[^>]*>(.*?)<\/span>/g;
export const REMOVE_TAG_B = /<b>(.*?)<\/b>/g;
export const CLEAN_TAG_STRONG_ATTRIBUTES = /<strong[^>]*>(.*?)<\/strong>/g;
export const REMOVE_STYLE_ATTRIBUTE = / style="[^"]*"/g;
export const REMOVE_CLASS_ATTRIBUTE = / class="[^"]*"/g;
export const REMOVE_DIR_ATTRIBUTE = / dir="[^"]*"/g;
export const CAPTURA_TEXTO_ENTRE_ECONOMIARIO_E_PONTO_PARA =
  /Economiário\s+(.+?)\. Para/;
export const SUBSTITUIR_DA_ABERTURA_P = /<p>/g;
export const SUBSTITUIR_DA_FECHAMENTO_P = /<\/p>/g;
