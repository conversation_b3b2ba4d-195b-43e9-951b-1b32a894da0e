import styled from 'styled-components';
import { Modal } from '@cvp/design-system/react';

export const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const ModalContent = styled.div`
  position: relative;
  width: 60%;
`;

export const CloseButton = styled.div`
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
`;

export const ColumChildFlex = styled.div`
  display: flex;
  flex-direction: column;
  > div {
    display: flex;
  }
`;

export const SpanStyled = styled.span`
  min-width: 150px;
  margin: 0 20px 10px 0;
`;

export const ModalDetalhesInadimplenciaStyled = styled(Modal)`
  max-width: 1000px;
`;
