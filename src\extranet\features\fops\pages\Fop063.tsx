import * as DS from '@cvp/design-system/react';
import { FormAssinaturaContrato } from 'extranet/components/fop63/FormAssinaturaContrato';
import { FormBeneficioBasico } from 'extranet/components/fop63/FormBeneficioBasico';
import { FormDadosAgencia } from 'extranet/components/fop63/FormDadosAgencia';
import { FormDadosResponsavel } from 'extranet/components/fop63/FormDadosResponsavel';
import { FormInformacoesComplementares } from 'extranet/components/fop63/FormInformacoesComplementares';
import { FormInformacoGerais } from 'extranet/components/fop63/FormInformacoGerais';
import { RegrasContratuais } from 'extranet/components/fop63/FormRegrasContratuais';
import { useEnviarAberturaFop63 } from 'extranet/hooks/useEnviarAberturaFop63';
import fopsValidation63, {
  validarArquivoDownload,
} from 'extranet/utils/Fop63Validation';
import { Formik } from 'formik';
import InputFile, {
  DescriptionFormatsFilesAllowed,
} from 'main/components/form/InputFile';
import Icon from 'main/components/Icon';
import { DownloadButton } from 'main/features/prospeccao/components/styles';
import React from 'react';
import { ModalFormulario } from '../../../components/fop63/ModalFormulario';
import * as ConstantsFop63 from '../../../types/ConstFop63';
import * as Enum from '../../../types/enum';
import * as S from './styles';
import { useToast } from 'main/hooks/useToast';

const Fop063: React.FC = () => {
  const {
    user,
    handleFormSubmit,
    formFop,
    loading,
    dataLocation
  } = useEnviarAberturaFop63();
  const { toastError } = useToast();

  return (
    <DS.Display type="display-block" key="form-fop063">
      <Formik
        key="form-fop-prev-empresarial-63"
        initialValues={{
          ...ConstantsFop63.InitialValues,
          nomeIndicador: user.nomeUsuario,
          matriculaIndicador: user.nomeAcesso,
          numeroDaAgencia: user.agenciaVinculada,
        }}
        validationSchema={fopsValidation63}
        onSubmit={values => handleFormSubmit(values)}
        validateOnBlur={true}
        validateOnChange
        validateOnMount={false}
      >
        {({
          handleSubmit,
          handleBlur,
          values,
          errors,
          setFieldValue,
          resetForm,
        }) => (
          <form onSubmit={handleSubmit} style={{ height: '100%' }}>
            <DS.Card key="form-header">
              <DS.Card.Content padding={[4, 4, 4]}>
                <DS.Text variant="body-medium3" color="text-light">
                  {Enum.Titulos.Aviso}
                </DS.Text>
                <br />
                <DS.Grid>
                  <DS.Grid.Item xs={1}>
                    <DS.Text
                      variant="headline-05"
                      color="primary"
                      key="formulario-titulo"
                    >
                      {Enum.Titulos.Fop63}
                    </DS.Text>
                  </DS.Grid.Item>
                </DS.Grid>
                <DS.Divider />
                <FormInformacoGerais
                  setFieldValue={setFieldValue}
                  values={values}
                  handleBlur={handleBlur}
                  errors={errors}
                />
                <DS.Divider />
                <FormDadosAgencia
                  errors={errors}
                  handleBlur={handleBlur}
                  setFieldValue={setFieldValue}
                  user={user}
                  values={values}
                />
                <DS.Divider />
                <FormBeneficioBasico
                  errors={errors}
                  formFop={formFop}
                  handleBlur={handleBlur}
                  setFieldValue={setFieldValue}
                  values={values}
                />
                <DS.Divider />
                <RegrasContratuais
                  errors={errors}
                  setFieldValue={setFieldValue}
                  formFop={formFop}
                  handleBlur={handleBlur}
                  values={values}
                />
                <DS.Divider />
                <FormInformacoesComplementares
                  setFieldValue={setFieldValue}
                  values={values}
                />
                <DS.Divider />
                <FormAssinaturaContrato
                  errors={errors}
                  handleBlur={handleBlur}
                  setFieldValue={setFieldValue}
                  values={values}
                />
                <DS.Divider />
                <FormDadosResponsavel
                  errors={errors}
                  handleBlur={handleBlur}
                  setFieldValue={setFieldValue}
                  values={values}
                />
                <DS.Divider />
                <DS.Text variant="body01-sm" color="primary" margin>
                  <DownloadButton
                    variant="text"
                    type="button"
                    onClick={() => {
                      const arquivo = dataLocation[0]?.dadosArquivos?.[0];
                      if (!arquivo) {
                        toastError("Erro ao Baixar a planilha");
                        return;
                      }
                      formFop.baixarArquivoFop(
                        validarArquivoDownload(dataLocation),
                        arquivo.nomeArquivo
                      );
                    }}

                  >
                    {formFop.loadingDownload && (
                      <DS.Button variant="text" loading />
                    )}
                    {!formFop.loadingDownload && (
                      <>
                        {Enum.Titulos.Download} &nbsp;
                        <Icon name="download" />
                      </>
                    )}
                  </DownloadButton>
                </DS.Text>
                <DS.Grid.Item xs={1} lg={1 / 2}>
                  <>
                    <S.InputLabel>{Enum.Titulos.AnexoFop}</S.InputLabel>
                    <DescriptionFormatsFilesAllowed fileSize="3" textFormat='Formatos de arquivos suportados: .xlsx e .xlsm' />
                    <InputFile
                      link={formFop.arquivoAnexoFop63}
                      validationRules={formFop.regraFiles}
                      error={!formFop.arquivoAnexoFop63.get()?.errorMsg}
                    />
                  </>
                </DS.Grid.Item>
                <ModalFormulario
                  setFieldValue={setFieldValue}
                  values={values}
                  formFop={formFop}
                  handleBlur={handleBlur}
                />
                <DS.Grid>
                  <DS.Grid.Item xs={1} lg={1 / 2}>
                    <DS.Display>
                      <DS.Button
                        variant="outlined"
                        type="button"
                        onClick={() => resetForm()}
                      >
                        {Enum.Botoes.Limpar}
                      </DS.Button>
                      <DS.Button
                        data-testid="visualizar-fop63"
                        type="button"
                        onClick={formFop.openModal}
                      >
                        {Enum.Botoes.Visualizar}
                      </DS.Button>
                      <DS.Button
                        data-testid="enviar-fop62"
                        type="submit"
                        disabled={loading}
                        loading={loading}
                      >
                        {Enum.Botoes.Enviar}
                      </DS.Button>
                    </DS.Display>
                  </DS.Grid.Item>
                </DS.Grid>
              </DS.Card.Content>
            </DS.Card>
          </form>
        )}
      </Formik>
    </DS.Display>
  );
};

export default Fop063;
