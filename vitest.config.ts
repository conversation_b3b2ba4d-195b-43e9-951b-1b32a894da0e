import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: './setupTests.ts',
    alias: {
      '@cvp/componentes-posvenda': path.resolve(
        __dirname,
        'node_modules/@cvp/componentes-posvenda/index.js',
      ),
      '@src': path.resolve(__dirname, 'src'),
      assets: path.resolve(__dirname, 'src/assets'),
      atendimento: path.resolve(__dirname, 'src/atendimento'),
      config: path.resolve(__dirname, 'src/config'),
      consultaCliente: path.resolve(__dirname, 'src/consultaCliente'),
      consultaStatusVendas: path.resolve(__dirname, 'src/consultaStatusVendas'),
      contratosPrestamista: path.resolve(__dirname, 'src/contratosPrestamista'),
      dps: path.resolve(__dirname, 'src/dps'),
      evolucaoPatrimonial: path.resolve(__dirname, 'src/evolucaoPatrimonial'),
      extranet: path.resolve(__dirname, 'src/extranet'),
      ferramentas: path.resolve(__dirname, 'src/ferramentas'),
      main: path.resolve(__dirname, 'src/main'),
      painelAdministracao: path.resolve(__dirname, 'src/painelAdministracao'),
      painelDPS: path.resolve(__dirname, 'src/painelDPS'),
      painelInadimplencia: path.resolve(__dirname, 'src/painelInadimplencia'),
      painelPortabilidade: path.resolve(__dirname, 'src/painelPortabilidade'),
      painelPrestamista: path.resolve(__dirname, 'src/painelPrestamista'),
      painelPrestamistaDeclinio: path.resolve(
        __dirname,
        'src/painelPrestamistaDeclinio',
      ),
      painelResgate: path.resolve(__dirname, 'src/painelResgate'),
      painelVidaPu: path.resolve(__dirname, 'src/painelVidaPu'),
      portabilidade: path.resolve(__dirname, 'src/portabilidade'),
      prestamista: path.resolve(__dirname, 'src/prestamista'),
      previdencia: path.resolve(__dirname, 'src/previdencia'),
      propostasVida: path.resolve(__dirname, 'src/propostasVida'),
      registroOcorrenciaASC: path.resolve(
        __dirname,
        'src/registroOcorrenciaASC',
      ),
      relatorios: path.resolve(__dirname, 'src/relatorios'),
      reter: path.resolve(__dirname, 'src/reter'),
      seguros: path.resolve(__dirname, 'src/seguros'),
      sinistro: path.resolve(__dirname, 'src/sinistro'),
      vida: path.resolve(__dirname, 'src/vida'),
      routes: path.resolve(__dirname, 'src/routes'),
      '/src/assets/icons/rounded-warning.svg?react':
        '/src/__mocks__/svgMock.ts',
    },
    coverage: {
      provider: 'v8',
    },
    clearMocks: true,
  },
});
