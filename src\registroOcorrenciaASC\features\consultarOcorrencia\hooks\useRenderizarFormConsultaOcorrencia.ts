import { useEffect } from 'react';
import { useFormik } from 'formik';
import {
  FormConsultaOcorrenciaInitialErrors,
  FormConsultaOcorrenciaInitialValues,
} from '../constants/forms';
import { validarValoresFormConsultaOcorrencia } from '../utils/ConsultarOcorrenciaUtils';
import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import { isCnpj, isCpf } from 'main/utils/cpf_cnpj';
import {
  EnumBuscaPor,
  FormConsultaOcorrenciaProps,
  IRenderizarFormConsultaOcorrencia,
} from '../types/ConsultarOcorrencia';
import { editarData } from 'main/utils';
import { CONFIG_HORA } from '../constants/constants';

export const useRenderizarFormConsultaOcorrencia = ({
  obterHistoricoSolicitacao,
  setListaHistoricoSolicitacao,
}: Pick<
  FormConsultaOcorrenciaProps,
  'setListaHistoricoSolicitacao' | 'obterHistoricoSolicitacao'
>): IRenderizarFormConsultaOcorrencia => {
  const formik = useFormik({
    initialValues: FormConsultaOcorrenciaInitialValues,
    initialErrors: FormConsultaOcorrenciaInitialErrors,
    validate: values => validarValoresFormConsultaOcorrencia(values),
    onReset: () => setListaHistoricoSolicitacao([]),
    onSubmit: () => undefined,
  });

  const isCpfCnpjValido = checkIfSomeItemsAreTrue([
    isCnpj(formik.values.cpfCnpj),
    isCpf(formik.values.cpfCnpj),
  ]);

  const isBuscaPorPreenchido = checkIfSomeItemsAreTrue([
    checkIfAllItemsAreTrue([
      formik.values.selected === EnumBuscaPor.CLIENTE,
      isCpfCnpjValido,
    ]),
    checkIfAllItemsAreTrue([
      formik.values.selected === EnumBuscaPor.PROTOCOLO,
      !!formik.values.protocolo,
    ]),
  ]);

  const isCamposFormConsultaPreenchidos: boolean = checkIfAllItemsAreTrue([
    !!formik.values.inputDate.finalDate,
    !!formik.values.inputDate.initialDate,
    isBuscaPorPreenchido,
  ]);

  const maxDate: Date = tryGetValueOrDefault(
    [formik.values.inputDate.finalDate],
    new Date(),
  );
  const maxDateRange = new Date();

  const handleObterHistoricoSolicitacoes = () => {
    if (
      isCamposFormConsultaPreenchidos &&
      formik.values.inputDate.initialDate &&
      formik.values.inputDate.finalDate
    )
      obterHistoricoSolicitacao({
        dataInicio: editarData({
          dataOriginal: formik.values.inputDate.initialDate,
          novaHora: CONFIG_HORA.HORA_INICIAL,
        }),
        dataFim: editarData({
          dataOriginal: formik.values.inputDate.finalDate,
          novaHora: CONFIG_HORA.HORA_FINAL,
          novoMinuto: CONFIG_HORA.MIN_FINAL,
          novoSegundo: CONFIG_HORA.SEG_FINAL,
        }),
        numeroCpfCnpj: formik.values.cpfCnpj,
        numeroProtocolo: formik.values.protocolo,
      });
  };

  const onChangeSelected = () => {
    switch (formik.values.selected) {
      case EnumBuscaPor.CLIENTE:
        formik.setFieldValue('protocolo', '');
        break;
      case EnumBuscaPor.PROTOCOLO:
        formik.setFieldValue('cpfCnpj', '');
        break;
      default:
        break;
    }
  };

  useEffect(onChangeSelected, [formik.values.selected]);

  return {
    values: formik.values,
    errors: formik.errors,
    touched: formik.touched,
    maxDate,
    maxDateRange,
    isCamposFormConsultaPreenchidos,
    handleChange: formik.setFieldValue,
    handleReset: formik.handleReset,
    handleObterHistoricoSolicitacoes,
  };
};
