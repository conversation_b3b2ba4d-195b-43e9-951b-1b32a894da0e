import React from 'react';

import { IRecuperarHierarquiaAgenciaResponse } from 'main/features/HierarquiaAgencia/types/IRecuperarHierarquiaAgenciaResponse';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import { payloadRequest } from 'painelInadimplencia/types/ConsultarInadimplenciaPayload';
import {
  DadosCliente,
  IDadosInadimplenciaResponse,
} from 'painelInadimplencia/types/ConsultarInadimplenciaResponse';
import { IDadosInadimplenciaPayload } from './ConsultarInadimplenciaRequest';
import { TableColumn } from 'react-data-table-component';

export interface IPainelInadimplenciaContextData {
  listaPagamentosDetalhado?: IPagamentosAdimplenciaResponse[];
  detalhesAdimplenciaAgencia?: DadosCliente;
}

export interface IHeaderPainelProps {
  tipoVisao: string;
  unidade: number | undefined;
  dadosHierarquiaAgencia: IRecuperarHierarquiaAgenciaResponse | undefined;
}

export interface IFiltrosProps {
  tipoVisao: string;
  payloadConsulta: payloadRequest;
  setPayloadConsulta: (
    value: React.SetStateAction<IDadosInadimplenciaPayload>,
  ) => void;
  filterFormaPagamento: () => {
    value: string;
    key: string;
  }[];
  handleBackStep: () => void;
  clearFilters: () => void;
  consultarInadimplencia: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<IDadosInadimplenciaResponse> | undefined>;

  isDisabledBotaoVoltar: boolean;
  naoCotemFiltros: boolean;
}

export interface IPagamentosAdimplenciaResponse {
  segmento: string;
  codigoAgencia: number;
  numeroContrato: number;
  numeroParcela: number;
  valorProposta: number;
  dataVencimentoParcela: string;
  formaPagamento: string;
  opcaoPagamento: string;
  agenciaCobranca: number;
  codigoOperacaoCobranca: number;
  numeroContaCobranca: string;
  idlg: string;
  numeroCartaoCredito: string;
  logradouro: string;
  numerologradouro: string;
  complementoEndereco: string;
  bairro: string;
  cidade: string;
  estado: string;
}

export interface IPagamentosAdimplenciaPayload {
  agencia: string;
  segmento: string;
  numeroContrato: string;
}

export interface IModalDetalhamentoParcelasProps {
  open: boolean;
  onClose: () => void;
  listaPagamentosDetalhado: IPagamentosAdimplenciaResponse[] | undefined;
  colunasDetalhesPagamentos: TableColumn<IColunasDetalhamentoParcelasReturn[]>;
  isLoading: boolean;
}

export interface IValidaReemissaoBoletoPayload {
  numeroCobranca: string;
}

export interface IObterCobrancaFlexivelResponse {
  podeReemitirCobranca: boolean;
}

export interface IObterCobrancaFlexivelPayload {
  sistemaChamador: string;
  numeroCertificado: string;
  numeroParcela: string;
  dataVencimento: string;
}

export interface ISegundaViaBoletoResponse {
  return: string;
}

export interface ISegundaViaBoletoPayload {
  cpfCnpj: string;
  numeroCObranca: string;
}

export interface IArquivoBoletoSegurosPayload {
  numeroCertificado: string;
  parcela: string;
}

export interface IGerarBoletoPrevidenciaParams {
  row: IPagamentosAdimplenciaResponse;
  detalhesAdimplenciaAgencia: DadosCliente | undefined;
  validarReemissaoBoleto: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<unknown> | undefined>;

  obterSegundaVia: (numeroCobranca: string, Cpf: string) => Promise<void>;
  handleToggleModalValidaEmissaoBoleto: () => void;
}

export interface IGerarBoletoVida {
  row: IPagamentosAdimplenciaResponse;
  obterCobrancaFlexivel: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    IHandleReponseResult<IObterCobrancaFlexivelResponse> | undefined
  >;
  obterSegundaViaBoletoVida: (
    numeroCertificado: string,
    parcela: string,
  ) => Promise<void>;
  handleToggleModalValidaEmissaoBoleto: () => void;
}

export interface IColunasDetalhamentoParcelasReturn {
  name: string;
  selector?: string;
  center?: boolean;
  minWidth?: string;
  cell?: (row: IPagamentosAdimplenciaResponse) => string | React.JSX.Element;
}

export interface IModalValidaEmissaoBoletoProps {
  open: boolean;
  onClose: () => void;
}
