export interface IValorContribuicao {
    descricaoGrupo: string;
    participante: string;
    ValorContribuicaoEmpresa:string;
    valorMaximoContribuicao: string;
    tipoPagamentoContribuicao: string;    
}

export interface ITempoContribuicao {
    tempoMesesContribuicao: string;
    porcentagemReversao: string;
}

export interface ICuidadoExtraContribuicao {
    descricao: string,
    valorContribuicaoPeculio: string,
    valorContribuicaoPensao: string;
    PorcentagemEmpresaContribuicao: string,
    PorcentagemParticipanteContribuicao: string,
}