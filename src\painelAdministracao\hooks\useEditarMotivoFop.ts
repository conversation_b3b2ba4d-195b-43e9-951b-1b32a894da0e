import { FormikProps, useFormik } from 'formik';
import { sanitizadorHtml } from 'main/utils/sanitizadorHtml';
import * as CONSTS from 'painelAdministracao/constants/constants';
import { AdmContext } from 'painelAdministracao/context/AdministracaoContext';
import * as FACTORY from 'painelAdministracao/factories/formatarDadosFactory';
import * as EDITAR_MOTIVO_PROPS from 'painelAdministracao/types/IEditarMotivoFop';
import { useContext, useEffect } from 'react';

const useEditarMotivoFop: EDITAR_MOTIVO_PROPS.IUseEditarMotivoFop = () => {
  const { fopEditar } = useContext(AdmContext);
  const formikMotivo: FormikProps<EDITAR_MOTIVO_PROPS.IFormikValuesEditarMotivoFop> =
    useFormik<EDITAR_MOTIVO_PROPS.IFormikValuesEditarMotivoFop>({
      enableReinitialize: true,
      initialValues: CONSTS.INITIAL_FORMIK_MOTIVO_STATE,
      validationSchema: FACTORY.FORMIK_MOTIVO_VALIDATION,
      validateOnMount: true,
      onSubmit: () => undefined,
    });

  const carregarValores = (): void => {
    formikMotivo.setValues({
      ...formikMotivo.values,
      motivoSolicitacaoAtivo: Boolean(fopEditar?.motivoSolicitacaoAtivo),
      descricaoMotivoSolicitacao: String(fopEditar?.descricaoMotivoSolicitacao),
    });
  };

  const handleChangeMotivoAtivo = (value: boolean): void => {
    formikMotivo.setValues({
      ...formikMotivo.values,
      motivoSolicitacaoAtivo: !value,
    });
  };

  const formatHTMLMotivo = (value: string): string =>
    CONSTS.TEXTO_ORIENTACAO_FOP(value);

  const handleDescricao = (textHml: string): void => {
    formikMotivo.setValues({
      ...formikMotivo.values,
      descricaoMotivoSolicitacao: sanitizadorHtml(formatHTMLMotivo(textHml)),
    });
  };

  useEffect(() => {
    carregarValores();
  }, [fopEditar]);

  return {
    formikMotivo,
    handleChangeMotivoAtivo,
    handleDescricao,
  };
};

export default useEditarMotivoFop;
