import { ChangeEvent } from 'react';
import { FormikProps } from 'formik';
import * as Yup from 'yup';
import { IUser } from 'main/features/Auth/interfaces';
import ValidateResult from 'main/features/Validation/types/ValidateResult';
import * as RESPONSE_TYPES from 'registroOcorrenciaASC/features/registrarOcorrencia/types/RegistrarOcorrenciasResponse';
import { TableColumn } from 'react-data-table-component';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import { RequiredStringSchema } from 'yup/lib/string';
import { AnyObject } from 'yup/lib/types';
import * as ENUM_TYPES from 'registroOcorrenciaASC/types/enum';

interface IListaCamposDinamicosListaDominios {
  valorDominio: string;
  nomeDominio: string;
  externalId: string;
}

export interface IListaCamposDinamicos {
  externalId: string;
  dataHoraCriacao: string;
  idAssunto: string;
  idCampoDinamico: string;
  nomeCampo: string;
  tipoCampoApi: string;
  tamanho: number;
  obrigatorio: boolean;
  segmentos: string[];
  dominios: IListaCamposDinamicosListaDominios[];
  placeHolder: string | null;
  mascaraRegEx: null;
  validacaoRegEx: null;
  validacaoMascaraRegEx: null;
  tipoCampoLabel: string;
  valor?: string;
  ordem?: number;
}

export type FormDadosClienteParams = {
  loadingDadosConsultaCliente: boolean;
};

export interface IFormikValuesConsultaCpfCnpj {
  inputconsultaCpfCnpj: string;
}
export interface IFormikValuesDadosCliente {
  inputRazaoSocialOuNome: string;
  inputEmailCorporativoOuEmail: string;
  inputCep: string;
  inputEndereco: string;
  inputBairro: string;
  inputCidade: string;
  inputUf: string;
  inputTelefonePrincipal: string;
  inputTelefoneSecundario: string;
  inputCpfRepresentante: string;
  inputNomeRepresentante: string;
  inputEmailRepresentante: string;
  inputTelefoneRepresentante: string;
}
export interface IFormikValuesDadosOcorrenciaFormComunicadoSinistro {
  nomeDadosSegurado: string;
  cpfDadosSegurado: string;
  dataNascDadosSegurado: string;
  sexoDadosSegurado: string;
  nomeDadosReclamante: string;
  cpfDadosReclamante: string;
  grauParentescoDadosReclamante: string;
  email1DadosReclamante: string;
  email2DadosReclamante: string;
  enderecoDadosReclamante: string;
  cidadeDadosReclamante: string;
  bairroDadosReclamante: string;
  uFDadosReclamante: string;
  dDDTelResidencialDadosReclamante: string;
  telResidencialDadosReclamante: string;
  dDDTelComercialDadosReclamante: string;
  telComercialDadosReclamante: string;
  dDDTelCelularDadosReclamante: string;
  telCelularDadosReclamante: string;
  coberturaPleiteadaDadosSinistro: string;
  dataOcorrenciaDadosSinistro: string;
  historicoDadosSinistro: string;
}

export interface IFormikValuesDadosOcorrencia
  extends IFormikValuesDadosOcorrenciaFormComunicadoSinistro {
  conteudoTextarea: string;
  arquivoAnexo: FileList | null;
  quantidadeCaracteresTextarea: number;
  toggleModalMsgDuplicidadeSolicitacao: boolean;
  toggleModalComunicadoSinistroPrestamista: boolean;
}

export type ConsultaCpfCnpjCliente = {
  cpf: boolean;
  cnpj: boolean;
  valor: string;
};

export type FormDadosCliente = {
  novoCliente: boolean;
  tipoCliente: string;
  razaoSocialOuNome: string;
  emailCorporativoOuEmail: string;
  cep: string;
  endereco: string;
  bairro: string;
  cidade: string;
  uf: string;
  telefonePrincipal: string;
  telefoneSecundario: string;
  cpfRepresentante: string;
  nomeRepresentante: string;
  emailRepresentante: string;
  telefoneRepresentante: string;
  camposObrigatorios: {
    pessoaFisica: boolean;
    pessoaJuridica: boolean;
  };
};

export type IFormDadosOcorrenciaContrato = {
  id: string;
  produto: string;
  contrato: string;
  vigencia: string;
};

export type FormDadosOcorrencia = {
  produto: string;
  macroassunto: string;
  assunto: string;
  mensagem: string;
  listaContratos: IFormDadosOcorrenciaContrato[];
  contratoSelecionado?: IFormDadosOcorrenciaContrato;
  listaCamposDinamicos: IListaCamposDinamicos[];
  camposDinamicosPreenchidos: IDynamicFields[];
  arquivoAnexo: FileList;
  botaoRegistroOcorrenciaHabilitado: boolean;
};

export type RetornoAberturaOcorrencia = {
  ocorrenciaAberta: boolean;
  numeroProtocolo: string;
  status: string;
  dataVencimento: string | null;
  codigoSolicitacao: string;
};

export type CamposComunicadoSinistoPrestamista = {
  NomeSegurado: string;
  CPFSegurado: string;
  DthNascimento: string;
  Sexo: string;
  NomeReclamante: string;
  CPFReclamante: string;
  GrauParentesco: string;
  Email1: string;
  Email2: string;
  Endereco: string;
  Cidade: string;
  Bairro: string;
  Uf: string;
  DDDResidencial: string;
  TelefoneResidencial: string;
  DDDComercial: string;
  TelefoneComercial: string;
  DDDCelular: string;
  TelefoneCelular: string;
  Cobertura: string;
  DthSinistro: string;
  HistoricoSinistro: string;
};

export type FormFop = {
  comunicadoSinistroPrestamista: boolean;
  camposComunicadoSinistoPrestamista: CamposComunicadoSinistoPrestamista;
};

export type RegistrarOcorrenciaData = {
  cpfCnpjCliente: ConsultaCpfCnpjCliente;
  formDadosCliente: FormDadosCliente;
  formDadosOcorrencia: FormDadosOcorrencia;
  aberturaOcorrencia: RetornoAberturaOcorrencia;
  formFop: FormFop;
};

type RegistrarOcorrenciaActions = {
  salvarConsultaCpfCnpj: (cpfCnpjCliente: ConsultaCpfCnpjCliente) => void;
  limparConsultaCpfCnpj: () => void;
  salvarDadosCliente: (formDadosCliente: FormDadosCliente) => void;
  limparDadosCliente: () => void;
  salvarDadosOcorrencia: (formDadosOcorrencia: FormDadosOcorrencia) => void;
  limparDadosOcorrencia: () => void;
  habilitarBotaoEnviarOcorrencia: () => void;
  salvarDadosAberturaOcorrencia: (
    aberturaOcorrencia: RetornoAberturaOcorrencia,
  ) => void;
  limparOcorrenciaAberta: () => void;
  toggleFormularioFop: (comunicadoSinistroPrestamista: boolean) => void;
  salvarCamposComunicadoSinistoPrestamista: (
    camposComunicadoSinistoPrestamista: CamposComunicadoSinistoPrestamista,
  ) => void;
};

export type RegistrarOcorrencia = RegistrarOcorrenciaData &
  RegistrarOcorrenciaActions;

export type IDynamicFields = {
  [key: string]: any;
  nomeCampo: string;
  obrigatorio: boolean;
  idCampoDinamico: string;
  valorCampo: string;
};

export type PayloadAberturaOcorrenciaParams = {
  formDadosOcorrencia: FormDadosOcorrencia;
  formDadosCliente: FormDadosCliente;
  formFop: FormFop;
  cpfCnpjCliente: ConsultaCpfCnpjCliente;
  user: IUser;
};

export type PayloadCriacaoCamposDinamicosParams = {
  formDadosOcorrencia: FormDadosOcorrencia;
  protocoloCriado: RESPONSE_TYPES.ICriarProtocoloResponse;
};

export type ModalDuplicidadeSolicitacaoProps = {
  formik: FormikProps<IFormikValuesDadosOcorrencia>;
  toggleModal: () => void;
};

export type ModalComunicadoSinistroPrestamistaProps = {
  formik: FormikProps<IFormikValuesDadosOcorrencia>;
  toggleModal: () => void;
  cleanModal: () => void;
  disableSaveBtnForm: (values: IFormikValuesDadosOcorrencia) => boolean;
  salvarFormContexto: () => void;
};

export interface IColunasTabelaDadosOcorrencia {
  salvarContratoSelecionadoContexto: (
    e: ChangeEvent<HTMLInputElement>,
  ) => Promise<void>;
  formDadosOcorrencia: FormDadosOcorrencia;
}

export interface IBotaoEnviarOcorrenciaProps {
  validateArquivoOcorrencia: () => ValidateResult;
  habilitarBotaoFormFopPreenchido?: boolean;
}

export interface IValidacaoCamposYupGeneric {
  [key: string]: Yup.StringSchema<string | undefined>;
}

export interface IUseRenderizarFormOcorrencia {
  formik: FormikProps<IFormikValuesDadosOcorrencia>;
  toggleModalComunicadoSinistroPrestamista: () => void;
  toggleModalDuplicidadeSolicitacao: () => void;
  limparFormComunicadoSinistroPrestamista: () => void;
  desabilitarBotaoFormComunicadoSinistroPrestamista: (
    values: IFormikValuesDadosOcorrencia,
  ) => boolean;
  loadingDadosContratos: boolean;
  loadingDadosSolicitacoesDuplicadas: boolean;
  loadingDadosConsultaCamposDinamicos: boolean;
  colunasTabelaContratos: TableColumn<IFormDadosOcorrenciaContrato>[];
  dadosConsultaCamposDinamicos?: IHandleReponseResult<RESPONSE_TYPES.IConsultarCamposDinamicosResponse>;
  salvarMensagemOcorrenciaContexto: (
    e: ChangeEvent<HTMLTextAreaElement>,
  ) => void;
  salvarCamposComunicadoSinistoPrestamistaContexto: () => void;
}

export type IDadosClienteSchema = {
  inputRazaoSocialOuNome: RequiredStringSchema<string | undefined, AnyObject>;
  inputEmailCorporativoOuEmail: Yup.StringSchema<
    string | undefined,
    AnyObject,
    string | undefined
  >;
  inputTelefonePrincipal: RequiredStringSchema<string | undefined, AnyObject>;
  inputCpfRepresentante: Yup.StringSchema<
    string | undefined,
    AnyObject,
    string | undefined
  >;
  inputNomeRepresentante: Yup.StringSchema<
    string | undefined,
    AnyObject,
    string | undefined
  >;
  inputEmailRepresentante: Yup.StringSchema<
    string | undefined,
    AnyObject,
    string | undefined
  >;
  inputTelefoneRepresentante: Yup.StringSchema<
    string | undefined,
    AnyObject,
    string | undefined
  >;
};

export interface IPayloadAberturaOcorrencia {
  unidadeNegocio: string;
  comentario: string;
  canal: ENUM_TYPES.PayloadFixo;
  cliente: {
    endereco: {
      numero: string;
      cep: string;
      cidade: string;
      complemento: string;
      bairro: string;
      estado: string;
      rua: string;
    };
    numeroCelular: string;
    numeroComercial: string;
    email: string;
    primeiroNome: string;
    ultimoNome: string;
    numero: string;
  };
  origemContato: ENUM_TYPES.PayloadFixo;
  numeroContrato?: string;
  numeroDocumento: string;
  apoio: {
    agencia: string;
    numeroDocumento: string;
    email: string;
    primeiroNome: string;
    ultimoNome: string;
    telefone: string;
    matricula: string;
  };
  codigoAdmAssunto: string;
  numeroContratoIndicativo?: string;
  segundoNivel: ENUM_TYPES.PayloadFixo;
  canalOrigem: ENUM_TYPES.PayloadFixo;
  representanteLegal: {
    telefone: string;
    cpfCnpj: string;
    nomeContato: string;
    descricaoEmail: string;
  };
  camposArquivoRtf: {
    dadosArquivoRtf: CamposComunicadoSinistoPrestamista;
  };
}

export interface IAtualizacaoCliente {
  primeiroNomecliente: string;
  sobreNomecliente: string;
  indClientePj: boolean;
  telefoneResidencial: string;
  telefoneEmpresa: string;
  telefone: string;
  cpfCnpj: string;
  cep: string;
  celular: string;
  uf: string;
  endereco: string;
  email: string;
  nomTipo: string;
  cliente: string;
  cidade: string;
  bairro: string;
}

export interface ICriacaoCliente {
  primeiroNomecliente: string;
  sobreNomecliente: string;
  telefoneResidencial: string;
  telefone: string;
  cpfCnpj: string;
  cep: string;
  celular: string;
  uf: string;
  endereco: string;
  complemento: string;
  email: string;
  cidade: string;
  bairro: string;
}

export interface ICriacaoCamposDinamicos {
  idSolicitacao: string;
  campos: { idCampoDinamico: string; valorCampo: string }[];
}
