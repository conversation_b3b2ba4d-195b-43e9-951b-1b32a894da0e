import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import * as ConsultarConsentimentoApi from 'previdencia/features/DadosParticipante/services/consultarConsentimento.api';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { AppContext } from 'main/contexts/AppContext';
import { ResponseConsultarConsentimento } from '../types/DadosParticipante';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useConsultarConsentimento = (
  subProcesso: string,
): UseQueryResult<ResponseConsultarConsentimento | undefined> => {
  const { toastError } = useToast();
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-consultar-consentimento', cpfCnpj],
    queryFn: () =>
      ConsultarConsentimentoApi.consultarConsentimento(
        cpfCnpj,
        numCertificado,
        subProcesso,
      ),
    staleTime: reactQueryCacheDuration(),
    retry: false,
    onError: (erro: Error) => toastError(erro.message),
  });
};

export default useConsultarConsentimento;
