import { <PERSON><PERSON>, <PERSON>, Display, Divider, Text } from '@cvp/design-system/react';
import { ICardProdutoVida } from 'consultaCliente/features/listaCardProduto/interfaces/ICardProdutoVida';
import { EnumStatusCardProduto } from 'consultaCliente/types/ICardsApresentacao';
import Icon from 'main/components/Icon';
import RenderConditional from 'main/components/RenderConditional';
import TagStatusApolice from 'main/components/TagStatusApolice/TagStatusApolice';
import { AppContext } from 'main/contexts/AppContext';
import FeatureAuthorizer from 'main/features/Auth/components/FeatureAuthorizer';
import {
  PRINCIPAL_USERS,
  USER_PROFILES,
} from 'main/features/Auth/config/userProfiles';
import { formatarData } from 'main/utils';
import React, { useContext } from 'react';
import NomeCertificadoCardItem from '../NomeCertificadoCardItem';
import { getTernaryResult, tryGetValueOrDefault } from 'main/utils/conditional';
import * as S from '../styles';

export interface CardProdutoVidaProps {
  data: ICardProdutoVida;
  onClick(): void;
}

export const CardVida: React.FunctionComponent<CardProdutoVidaProps> = ({
  onClick,
  data,
}) => {
  const { nomeSocial } = useContext(AppContext);

  const produtosSemDetalhes = ['9311', '9386', '9711'];
  return (
    <Card>
      <S.Container>
        <Card.Content padding={[1, 5, 0]}>
          <Display alignItems="center">
            <S.Text
              title={data.descricaoProduto}
              status={getTernaryResult(
                data.situacaoRegistro !== null,
                data?.situacaoRegistro?.slice(0, 1),
                EnumStatusCardProduto.Cancelado,
              )}
            >
              {data.descricaoProduto}
            </S.Text>
          </Display>
          <Display justify="end">
            <TagStatusApolice status={data.situacaoRegistro} />
          </Display>
        </Card.Content>
        <Divider />
        <Card.Content padding={[1, 6, 0]}>
          <Display alignItems="center">
            <S.IconContainer>
              <Icon name="creditCardVerse" />
            </S.IconContainer>
            <span>
              <Text variant="caption-02">Apólice</Text>
              <Text variant="body01-sm">{data.numeroApolice}</Text>
            </span>
          </Display>
          <NomeCertificadoCardItem
            nomeCliente={tryGetValueOrDefault([nomeSocial], data.nomeSegurado)}
            numCertificado={data.numeroBilhete}
          />
          <Display alignItems="center">
            <S.IconContainer>
              <Icon name="calendar" />
            </S.IconContainer>
            <span>
              <Text variant="caption-02">Início da Vigência</Text>
              <Text variant="body03-md">
                {formatarData(data.dataInicioVigencia)}
              </Text>
            </span>
          </Display>
        </Card.Content>
        <RenderConditional
          condition={!produtosSemDetalhes.includes(data.codigoProduto)}
        >
          <FeatureAuthorizer
            requiredRoles={[
              ...PRINCIPAL_USERS,
              USER_PROFILES.ANALISTA_CONSULTA,
              USER_PROFILES.ANALISTA_MANUTENCAO,
              USER_PROFILES.ANALISTA_SAIDA,
              USER_PROFILES.ANALISTA_ENTRADA,
              USER_PROFILES.ANALISTA_PJ,
            ]}
          >
            <Card.Content padding={[1, 4, 0]}>
              <Display justify="space-between">
                <Button onClick={onClick} fullWidth small>
                  Detalhes
                </Button>
              </Display>
            </Card.Content>
          </FeatureAuthorizer>
        </RenderConditional>
      </S.Container>
    </Card>
  );
};
