import { TableColumn } from 'react-data-table-component';
import { IContaBancaria } from '../types';
import { getTernaryResult } from 'main/utils/conditional';

export const COLUNAS_CONTAS_BANCARIAS: TableColumn<IContaBancaria>[] = [
  {
    name: 'Agência',
    selector: row => row.agencia,
  },
  {
    name: 'Operação',
    center: true,
    selector: row => row.operacao,
  },
  {
    name: '<PERSON><PERSON>',
    selector: row => row.numeroConta,
  },
  {
    name: 'Dígito verificador',
    center: true,
    selector: row => row.digitoVerificador,
  },
  {
    name: 'Conta salário',
    center: true,
    selector: row => getTernaryResult(row.contaSalario, 'Sim', 'Não'),
  },
];
