import { ColunasTabela } from 'painelAdministracao/types/colunasTabela';
import * as EDITAR_PROPS from 'painelAdministracao/types/IEditarFop';
import * as EDITAR_MOTIVO_PROPS from 'painelAdministracao/types/IEditarMotivoFop';
import { IRelatorioSolicitacaoFop } from 'painelAdministracao/types/IRelatorioSolicitacaoFop';
import { TableColumn } from 'react-data-table-component';

export const SUBTITULO_FOP =
  'Para atualizar, clique no FOP desejado, realize o upload do novo arquivo e atualize a versão.';

export const BOTOES = {
  EDITAR: 'Editar arquivo',
  ADICIONAR: 'Adicionar novo arquivo',
};

export const MODAL = {
  TITULO_EDITAR: 'Informe os dados para atualização: ',
  TITULO_NOVO: 'Informe os dados para cadastrar novo FOP: ',
  BOTAO_ARQUIVO: 'Selecione o arquivo',
  BOTAO_ATUALIZAR: 'Atualizar',
  BOTAO_CADASTRAR: 'Cadastrar',
  BOTAO_REMOVER: 'Remover FOP',
};

export const COLUNAS_TABELA: TableColumn<ColunasTabela>[] = [
  {
    name: 'Simulador',
    selector: row => row.simulador,
  },
  {
    name: 'Previdência',
    selector: row => row.previdencia,
  },
  {
    name: 'Prestamista',
    selector: row => row.prestamista,
  },
  {
    name: 'Vida',
    selector: row => row.vida,
  },
];

export const INITIAL_FORMIK_STATE: EDITAR_PROPS.IFormikValuesEditarFop = {
  nome: '',
  codigo: '',
  versao: '',
  dataVersao: null,
  ativo: true,
  novoArquivo: {} as File,
  numeroProposta: '',
};

export const FORMIK_VALIDATION_MESSAGES = {
  CAMPO_OBRIGATORIO: 'Campo obrigatório',
};

export const MENSAGEM_CONFIRMACAO_EXCLUSAO =
  "O FOP '{nomeFop}' será excluído. Confirma exclusão?";

export const INITIAL_FORMIK_MOTIVO_STATE: EDITAR_MOTIVO_PROPS.IFormikValuesEditarMotivoFop =
  {
    motivoSolicitacaoAtivo: false,
    descricaoMotivoSolicitacao: '',
  };

export const TEXTO_ORIENTACAO_FOP = (value: string): string =>
  `<p>Este serviço está disponível para ser executado de forma digital aqui no Portal do Economiário ${value}. </br> Para seguir com o download, por favor, descreva o motivo para a utilização do FOP.</p>`;

export const FEEDBACKMOTIVO = {
  success: 'Motivo salvo com sucesso!',
};

export const BUTTON_TEXT = 'ENVIAR';

export const LABEL_MOTIVO = 'Ativar solicitação motivo:';

export const INFO_MODAL_MOTIVO =
  'Informe abaixo o caminho para a funcionalidade no autosserviços.';

export const STATUS_FOAP = {
  ATIVO: 'Ativar',
  DESATIVADO: 'Desativar',
};

export const TAMANO_PAGINA_DEFAULT = 15;

export const MOTIVOS_VAZIOS: IRelatorioSolicitacaoFop[] = [];
export const TRADUCAO_TABELA = {
  ITEM_POR_PAGINA: 'Items por página',
  TEXTO_SEPARADO: 'de',
  SEM_DADOS: 'Não há dados para exibir.',
};

export const FILE_NAME_EXPORT = 'Relatório FOP';

export const FILE_NAME_FOP = 'relatorio_foap.csv';

export const BUTTONS_NAME = {
  EXPORT_FOAP: 'Exportar CSV',
  LIMPAR: 'Limpar',
  BUSCAR: 'Buscar',
};
