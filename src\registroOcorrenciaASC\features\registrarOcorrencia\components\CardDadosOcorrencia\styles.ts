import styled from 'styled-components';
import {
  Select as SelectDS,
  Accordion as AccordionDS,
  Grid as GridDS,
} from '@cvp/design-system/react';

export const Select = styled(SelectDS)(() => ({
  '& input, & div': {
    height: '45px',
  },

  '& label ~ div': {
    lineHeight: '45px',
  },

  '& label': {
    marginBottom: '0.2rem',
  },
}));

export const AccordionItem = styled(AccordionDS.Item)`
  .Accordionstyle__Body-sc-6md5od-12 {
    overflow: visible;
    padding-bottom: '0px';
  }
`;

export const SelectGridWrapper = styled(GridDS)(({ spacingFooter }) => ({
  minHeight: spacingFooter ? '300px' : '150px',
}));
