import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import MensagemSemPermissaoAlteracao from 'previdencia/features/AlteracaoRegimeTributario/components/MensagemSemPermissaoAlteracao';
import * as usePrevNavigation from 'previdencia/hooks/usePrevNavigation';
import { ALIQUOTA } from 'previdencia/constants/constants';
import * as REGIME_TRIBUTARIO_TYPES from 'previdencia/features/AlteracaoRegimeTributario/types/AlteracaoRegimeTributario';

describe('MensagemSemPermissaoAlteracao', () => {
  const defaultProps: REGIME_TRIBUTARIO_TYPES.IMensagemSemPermissaoAlteracaoProps =
    {
      opcaoTributacaoIrrfAtual: ALIQUOTA.TRIBUTACAO_PROGRESSIVA,
    };

  let goDadosPlanoMock: any;

  beforeEach(() => {
    goDadosPlanoMock = vi.fn();

    vi.spyOn(usePrevNavigation, 'default').mockReturnValue({
      navigateTo: vi.fn(),
      navigateToVida: vi.fn(),
      goHomeVida: vi.fn(),
      goHome: vi.fn(),
      goDadosPlano: goDadosPlanoMock,
    });
    const queryClient = new QueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <RenderPageWithThemeProviderAndRouter>
          <MensagemSemPermissaoAlteracao {...defaultProps} />
        </RenderPageWithThemeProviderAndRouter>
      </QueryClientProvider>,
    );
  });

  it('deve renderizar o texto com a opção tributária correta', () => {
    const textElement = screen.getByText(
      /Este certificado já optou pelo regime tributário/,
    );
    expect(textElement).toBeInTheDocument();
    expect(textElement).toHaveTextContent(
      `Este certificado já optou pelo regime tributário ${ALIQUOTA.PROGRESSIVO}. Esta opção é irretratável.`,
    );
  });

  it('deve renderizar o botão "Voltar"', () => {
    const buttonElement = screen.getByRole('button', {
      name: /Voltar/i,
    });
    expect(buttonElement).toBeInTheDocument();
  });

  it('deve chamar goDadosPlano quando o botão "Voltar" for clicado', () => {
    const botaoVoltar = screen.getByRole('button', { name: /Voltar/i });
    fireEvent.click(botaoVoltar);
    expect(goDadosPlanoMock).toHaveBeenCalledTimes(1);
  });
});
