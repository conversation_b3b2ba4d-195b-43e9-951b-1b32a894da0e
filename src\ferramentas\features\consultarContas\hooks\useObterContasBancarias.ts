import { obterEndPointConsultarContasBancarias } from 'ferramentas/config/endpoints';
import { obterContasBancarias } from 'ferramentas/features/consultarContas/factories/obterContasBancarias';
import {
  IResponseContasCaixa,
  TUseObterContasBancarias,
} from 'ferramentas/features/consultarContas/types';
import { usePeco } from 'main/hooks/usePeco';
import { tryGetValueOrDefault } from 'main/utils/conditional';

const DEFAULT_VALUE: IResponseContasCaixa[] = [];

export const useObterContasBancarias = (): TUseObterContasBancarias => {
  const { loading, response, fetchData, setResponse } = usePeco<
    { cpfCnpj: string },
    IResponseContasCaixa[]
  >({
    api: { operationPath: obterEndPointConsultarContasBancarias() },
    autoFetch: false,
  });

  const resetarResponse = () => {
    setResponse(
      prev =>
        prev && {
          ...prev,
          entidade: [],
        },
    );
  };

  const obterContas = async (cpfCnpj: string) => {
    await fetchData({ cpfCnpj });
  };

  return {
    loading,
    resetarResponse,
    obterContas,
    iniciado: !!response?.entidade,
    dados: obterContasBancarias(
      tryGetValueOrDefault([response?.entidade], DEFAULT_VALUE),
    ),
  };
};
