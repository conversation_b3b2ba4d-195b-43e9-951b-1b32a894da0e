import { downloadBase64File } from 'main/utils/converterBase64';
import {
  MENSAGEM_DE_ERRO_FORMATACAO,
  MENSAGEM_ERRO_GERACAO_ARQUIVO,
} from 'painelPrestamistaDeclinio/constants/constants';
import { FormatoArquivoDeclinioPrestamistaDetalhesAgenciaFactory } from 'painelPrestamistaDeclinio/factories/formatoArquivoDeclinioPrestamistaDetalhesAgenciaFactory';
import { usePecoExportarDeclinioPrestamistaDetalhesAgencia } from 'painelPrestamistaDeclinio/hook/usePecoExportarDeclinioPrestamistaDetalhesAgencia';
import { useState } from 'react';
import { useToast } from 'main/hooks/useToast';
import { IDownloadFile } from 'painelPrestamistaDeclinio/types/IDownloadFile';

export const useDownloadFile = (unidade: string): IDownloadFile => {
  const [openModalDados, setOpenModalDados] = useState(false);
  const [formatoArquivo, setFormatoArquivo] = useState<number>();
  const { toastError } = useToast();

  const handleClose = (): void => {
    setOpenModalDados(false);
  };

  const handleOpen = (): void => {
    setOpenModalDados(true);
  };

  const { fechDataExportar, loadingExportar } =
    usePecoExportarDeclinioPrestamistaDetalhesAgencia();

  const handleChangeFormat = async (): Promise<void> => {
    if (formatoArquivo) {
      const responseExportar = await fechDataExportar({
        codigo: String(unidade),
        TipoArquivoExportacao: Number(formatoArquivo),
      });

      const dadosFormatados =
        FormatoArquivoDeclinioPrestamistaDetalhesAgenciaFactory(
          Number(formatoArquivo),
        );

      if (responseExportar instanceof ArrayBuffer) {
        const blob = new Blob([responseExportar], {
          type: dadosFormatados.type,
        });

        downloadBase64File({
          blob,
          fileName: dadosFormatados.filename,
        });
      } else {
        toastError(`${MENSAGEM_DE_ERRO_FORMATACAO} ${typeof responseExportar}`);
      }
    } else {
      toastError(MENSAGEM_ERRO_GERACAO_ARQUIVO);
    }
  };

  return {
    openModalDados,
    handleClose,
    handleOpen,
    setFormatoArquivo,
    handleChangeFormat,
    loadingExportar,
  };
};
