import React, { useState, KeyboardEvent } from 'react';
import { SearchField, Tooltip } from '@cvp/design-system/react';
import { required } from 'main/features/Validation/validations';
import { checkIfSomeItemsAreTrue } from 'main/utils/conditional';
import {
  ISelectSearchItem,
  TSelectSearchProps,
} from 'main/types/Forms/SelectSearch';
import * as S from './styles';
import Icon from 'main/components/Icon';
import RenderConditional from 'main/components/RenderConditional';
import { SELECT_SEARCH_TEXTS } from 'main/constants/SelectSearch';

const SelectSearch: React.FC<TSelectSearchProps> = ({
  label,
  tooltip,
  placeholder,
  isRequired,
  selectedItem,
  options = [],
  setSelectedItem,
  ...props
}) => {
  const [showOptions, setShowOptions] = useState(false);
  const [filteredOptions, setFilteredOptions] =
    useState<ISelectSearchItem[]>(options);
  const [value, setValue] = useState('');

  const getSelectedOption = (valueToFilter: string) => {
    return options?.filter(
      item =>
        item?.label?.toLowerCase().includes(valueToFilter.toLowerCase()) ||
        item?.value
          ?.toString()
          .toLowerCase()
          .includes(valueToFilter.toLowerCase()),
    );
  };
  const updateState = (valueToFilter: string) => {
    setValue(valueToFilter);
    setFilteredOptions(getSelectedOption(valueToFilter));

    const valueIsOption = options.find(
      option => option.label.toLowerCase() === valueToFilter.toLowerCase(),
    );

    if (checkIfSomeItemsAreTrue([!valueToFilter, !valueIsOption])) {
      setSelectedItem({ label: '', value: '' });
    } else if (valueIsOption) {
      setSelectedItem(valueIsOption);
      setValue(valueIsOption.label);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const valueToFilter = e.target.value;
    updateState(valueToFilter);
  };

  const handleSelect = (item: ISelectSearchItem) => {
    setSelectedItem(item);
    setValue(item.label);
  };

  const handleOnBlur = () => {
    setFilteredOptions(options);
    setShowOptions(false);
    if (!selectedItem) {
      setValue('');
    }
  };

  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const valueFromInput = (e.target as HTMLInputElement).value;
      updateState(valueFromInput);
    }
  };

  return (
    <S.Container {...props}>
      <S.LabelContainer>
        <RenderConditional condition={!!label}>
          <S.Label variant="body02-sm">{label}</S.Label>
        </RenderConditional>

        <RenderConditional condition={!!tooltip}>
          <Tooltip text={tooltip} variant="gray">
            <Icon name="warning" />
          </Tooltip>
        </RenderConditional>
      </S.LabelContainer>

      <SearchField
        placeholder={placeholder ?? SELECT_SEARCH_TEXTS.O_QUE_PROCURA}
        value={value}
        validationRules={isRequired && [required()]}
        onKeyPress={handleKeyPress}
        onFocus={() => setShowOptions(true)}
        onBlur={handleOnBlur}
        onChange={handleSearch}
      />
      <S.ContainerItens>
        <S.StyledUl show={showOptions}>
          {filteredOptions.map(item => (
            <S.StyledLi key={item.value} onMouseDown={() => handleSelect(item)}>
              {item.label}
            </S.StyledLi>
          ))}
        </S.StyledUl>
      </S.ContainerItens>
    </S.Container>
  );
};

export default SelectSearch;
