export type TCalendarData = {
  initialDate: Date | null;
  finalDate: Date | null;
};

export interface ICalendarProps {
  values: TCalendarData;
  placeholder?: string;
  range?: boolean;
  maxDate?: Date;
  maxDateRange?: Date;
  requiredInitialDate?: boolean;
  requiredFinalDate?: boolean;
  errorInitialDate?: boolean;
  errorInitialDateMessage?: string;
  errorFinalDate?: boolean;
  errorFinalDateMessage?: string;
  onChange: (inputDate: TCalendarData) => void;
}
