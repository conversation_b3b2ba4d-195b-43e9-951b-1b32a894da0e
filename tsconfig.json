{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "lib": ["dom", "esnext"],
    "module": "esnext",
    "skipLibCheck": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,

    /* Bundler mode */
    "moduleResolution": "node",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "types": ["node", "@testing-library/jest-dom","vitest/globals"],
    "baseUrl": ".",
    "outDir": "./build",
    "paths": {
      "@types/*": ["src/@types/*"],
      "assets/*": ["src/assets/*"],
      "atendimento/*": ["src/atendimento/*"],
      "config/*": ["src/config/*"],
      "consultaCliente/*": ["src/consultaCliente/*"],
      "consultaStatusVendas/*": ["src/consultaStatusVendas/*"],
      "contratosPrestamista/*": ["src/contratosPrestamista/*"],
      "dps/*": ["src/dps/*"],
      "evolucaoPatrimonial/*": ["src/evolucaoPatrimonial/*"],
      "extranet/*": ["src/extranet/*"],
      "ferramentas/*": ["src/ferramentas/*"],
      "main/*": ["src/main/*"],
      "painelAdministracao/*": ["src/painelAdministracao/*"],
      "painelDPS/*": ["src/painelDPS/*"],
      "painelInadimplencia/*": ["src/painelInadimplencia/*"],
      "painelPortabilidade/*": ["src/painelPortabilidade/*"],
      "painelPrestamista/*": ["src/painelPrestamista/*"],
      "painelPrestamistaDeclinio/*": ["src/painelPrestamistaDeclinio/*"],
      "painelResgate/*": ["src/painelResgate/*"],
      "painelVidaPu/*": ["src/painelVidaPu/*"],
      "portabilidade/*": ["src/portabilidade/*"],
      "prestamista/*": ["src/prestamista/*"],
      "previdencia/*": ["src/previdencia/*"],
      "propostasVida/*": ["src/propostasVida/*"],
      "registroOcorrenciaASC/*": ["src/registroOcorrenciaASC/*"],
      "relatorios/*": ["src/relatorios/*"],
      "reter/*": ["src/reter/*"],
      "seguros/*": ["src/seguros/*"],
      "sinistro/*": ["src/sinistro/*"],
      "vida/*": ["src/vida/*"],
      "routes/*": ["src/routes/*"],
      "@/*": ["src/*"]
    },
  },
  "include": ["src", "scripts"],
  "references": [
    { "path": "./tsconfig.node.json" }
  ]
}
