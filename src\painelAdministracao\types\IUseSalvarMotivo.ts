import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import { IAtivarMotivoRequest } from 'painelAdministracao/types/IAtivarMotivoRequest';
import { IAtivarMotivoResponse } from 'painelAdministracao/types/IAtivarMotivoResponse';

export interface IUseSalvarMotivo {
  fetchDataMotivoFop: (
    dynamicPayload?: IAtivarMotivoRequest,
  ) => Promise<IHandleReponseResult<IAtivarMotivoResponse> | undefined>;
  loadingMotivoFop: boolean;
  response: IHandleReponseResult<IAtivarMotivoResponse> | undefined;
}
