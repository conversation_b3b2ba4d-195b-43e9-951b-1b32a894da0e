import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import * as HistoricoSolicitacoesApi from 'previdencia/features/HistoricoSolicitacoes/services/historicoSolicitacoes.api';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { AppContext } from 'main/contexts/AppContext';
import { ResponseHistoricoSolicitacoes } from '../types/HistoricoSolicitacoes';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useConsultarHistoricoSolicitacoes = (
  dataInicial: string,
  dataFinal: string,
): UseQueryResult<ResponseHistoricoSolicitacoes[] | undefined> => {
  const { toastError } = useToast();
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-historico-solicitacoes-', cpfCnpj],
    queryFn: () =>
      HistoricoSolicitacoesApi.obterHistoricoSolicitacoes(
        cpfCnpj,
        numCertificado,
        dataInicial,
        dataFinal,
      ),
    staleTime: reactQueryCacheDuration(),
    retry: false,
    onError: (erro: Error) => toastError(erro.message),
  });
};
export default useConsultarHistoricoSolicitacoes;
