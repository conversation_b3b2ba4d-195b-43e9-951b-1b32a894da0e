import * as Const from 'extranet/types/ConstFop63';
import { MENSAGEM_NUMERO_MINIMO_PARTICIPANTE } from 'extranet/types/ConstFop63';
import { IResponseObterListaFopsAtivos } from 'main/features/Administracao/types/IFops';
import { validarCpf } from 'main/utils/cpf_cnpj';
import * as Yup from 'yup';
import { AnyObject } from 'yup/lib/types';

const FormFopsValidationSchema = Yup.object({
  nomeDaEmpresa: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  atividadePrincipalEmpresa: Yup.string().required(
    Const.MENSAGEM_CAMPO_OBRIGATORIO,
  ),
  cnpjEmpresa: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  faturamento: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test('faturamento', Const.MENSAGEM_CAMPO_OBRIGATORIO, (valor, contexto) =>
      validadorSelect(valor, contexto, 'faturamento'),
    ),
  emailInstitucional: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .email(Const.MENSAGEM_EMAIL_INVALIDO)
    .test('email', 'E-mail inválido', function (value) {
      if (value === null) {
        this.createError({
          message: Const.MENSAGEM_EMAIL_INVALIDO,
          path: 'email',
        });
        return false;
      }
      return true;
    }),
  logradouro: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  bairro: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  cidade: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  uf: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test('uf', Const.MENSAGEM_CAMPO_OBRIGATORIO, (valor, contexto) =>
      validadorSelect(valor, contexto, 'uf'),
    ),
  cep: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  nomeRepresentante: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  emailRepresentante: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .email(Const.MENSAGEM_EMAIL_INVALIDO)
    .test('email', 'E-mail inválido', function (value) {
      if (value === null) {
        this.createError({
          message: Const.MENSAGEM_EMAIL_INVALIDO,
          path: 'email',
        });
        return false;
      }
      return true;
    }),
  cargoRepresentante: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  agenciaSr: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  nomeDaAgencia: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  nomeIndicador: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  matriculaIndicador: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  agenciaFilial: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'agenciaFilial',
      Const.MENSAGEM_CAMPO_OBRIGATORIO,
      (valor, contexto) => validadorSelect(valor, contexto, 'agenciaFilial'),
    ),
  numeroDeParticipantes: Yup.number()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .transform(valorOriginal => {
      if (valorOriginal === '') return '';
      return Number(valorOriginal);
    })
    .min(5, MENSAGEM_NUMERO_MINIMO_PARTICIPANTE),
  aporteInicial: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'aporteInicial',
      Const.MENSAGEM_CAMPO_OBRIGATORIO,
      (valor, contexto) => validadorSelect(valor, contexto, 'aporteInicial'),
    ),
  formaCusteioModalidadePlano: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'formaCusteioModalidadePlano',
      Const.MENSAGEM_CAMPO_OBRIGATORIO,
      (valor, contexto) =>
        validadorSelect(valor, contexto, 'formaCusteioModalidadePlano'),
    ),
  pagamentoContribuicao: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'pagamentoContribuicao',
      Const.MENSAGEM_CAMPO_OBRIGATORIO,
      (valor, contexto) =>
        validadorSelect(valor, contexto, 'pagamentoContribuicao'),
    ),
  diaPagamento: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test('diaPagamento', Const.MENSAGEM_CAMPO_OBRIGATORIO, (valor, contexto) =>
      validadorSelect(valor, contexto, 'diaPagamento'),
    ),
  formaPagamentoRegraContratual: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'formaPagamentoRegraContratual',
      Const.MENSAGEM_CAMPO_OBRIGATORIO,
      (valor, contexto) =>
        validadorSelect(valor, contexto, 'formaPagamentoRegraContratual'),
    ),
  perdaVinculo: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test('perdaVinculo', Const.MENSAGEM_CAMPO_OBRIGATORIO, (valor, contexto) =>
      validadorSelect(valor, contexto, 'perdaVinculo'),
    ),
  demisaoJustaCausa: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'demisaoJustaCausa',
      Const.MENSAGEM_CAMPO_OBRIGATORIO,
      (valor, contexto) =>
        validadorSelect(valor, contexto, 'demisaoJustaCausa'),
    ),
  penalidades: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test('penalidades', Const.MENSAGEM_CAMPO_OBRIGATORIO, (valor, contexto) =>
      validadorSelect(valor, contexto, 'penalidades'),
    ),
  distribuicaoContaColetiva: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test(
      'distribuicaoContaColetiva',
      Const.MENSAGEM_CAMPO_OBRIGATORIO,
      (valor, contexto) =>
        validadorSelect(valor, contexto, 'distribuicaoContaColetiva'),
    ),
  tempoMinimoPlano: Yup.string()
    .test('min-num', Const.TEMPO_MINIMO_PLANO, value => {
      if (!value) return true;
      const num = Number(value);
      return !isNaN(num) && num >= 5;
    })
    .test(
      'tempoMinimoPlano',
      Const.MENSAGEM_CAMPO_OBRIGATORIO,
      function (value) {
        const { idadeAposentadoria } = this.parent;
        return !!value || !!idadeAposentadoria;
      },
    ),
  idadeAposentadoria: Yup.string().test(
    'idadeAposentadoria',
    Const.MENSAGEM_CAMPO_OBRIGATORIO,
    function (value) {
      const { tempoMinimoPlano } = this.parent;
      return !!value || !!tempoMinimoPlano; // se um dos dois existir, passa
    },
  ),
  nomeRepresentanteLegal: Yup.string().required(
    Const.MENSAGEM_CAMPO_OBRIGATORIO,
  ),
  cpfRepresentanteLegal: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test('cpf', Const.CPF_INVALIDO, function (value) {
      return validarCpf(value!);
    }),
  emailRepresentanteLegal: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .email(Const.MENSAGEM_EMAIL_INVALIDO)
    .test('email', 'E-mail inválido', function (value) {
      if (value === null) {
        this.createError({
          message: Const.MENSAGEM_EMAIL_INVALIDO,
          path: 'email',
        });
        return false;
      }
      return true;
    }),
  nomePrimeiraTestemunha: Yup.string().required(
    Const.MENSAGEM_CAMPO_OBRIGATORIO,
  ),
  cpfPrimeiraTestemunha: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .test('cpf', Const.CPF_INVALIDO, function (value) {
      return validarCpf(value!);
    }),
  emailPrimeiraTestemunha: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .email(Const.MENSAGEM_EMAIL_INVALIDO)
    .test('email', 'E-mail inválido', function (value) {
      if (value === null) {
        this.createError({
          message: Const.MENSAGEM_EMAIL_INVALIDO,
          path: 'email',
        });
        return false;
      }
      return true;
    }),
  nomeCompletoResponsavel: Yup.string().required(
    Const.MENSAGEM_CAMPO_OBRIGATORIO,
  ),
  telefoneResponsavel: Yup.string().required(Const.MENSAGEM_CAMPO_OBRIGATORIO),
  emailResponsavel: Yup.string()
    .required(Const.MENSAGEM_CAMPO_OBRIGATORIO)
    .email(Const.MENSAGEM_EMAIL_INVALIDO)
    .test('email', Const.MENSAGEM_DOMINIOS_CAIXA, function (value) {
      if (
        !value?.toLowerCase().match(Const.DOMINIO_CVP) &&
        !value?.toLowerCase().match(Const.DOMINIO_CAIXA)
      ) {
        this.createError({
          message: Const.MENSAGEM_DOMINIOS_CAIXA,
          path: 'email',
        });
        return false;
      }
      return true;
    }),
});

export const validarArquivoDownload = (
  dataLocation: IResponseObterListaFopsAtivos[],
) => {
  console.log('validar download')
  return dataLocation[0]?.dadosArquivos
    ? dataLocation[0]?.dadosArquivos[0]?.codigoIdentificadorUnico
    : '00000000';
};

const validadorSelect = (
  valor: string | undefined,
  contextoYup: Yup.TestContext<AnyObject>,
  nomeObjeto: string,
) => {
  if (
    valor?.match(
      Const.SELECIONE ||
      Const.SELECIONE_RELACIONAMENTO ||
      Const.SELECIONE_UF,
    )
  ) {
    contextoYup.createError({
      message: Const.MENSAGEM_CAMPO_OBRIGATORIO,
      path: nomeObjeto,
    });
    return false;
  }
  return true;
};

export default FormFopsValidationSchema;
