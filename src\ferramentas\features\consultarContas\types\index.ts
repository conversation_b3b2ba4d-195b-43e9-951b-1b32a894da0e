export interface IContaBancaria {
  operacao: string;
  agencia: string;
  numeroConta: string;
  digitoVerificador: string;
  contaSalario: boolean;
}

export interface IResponseContasCaixa {
  titularidade: string;
  tipo: string;
  agencia: string;
  tipoConta: string;
  numeroConta: string;
  digitoVerificador: string;
  contaSalario: string;
}

export type TTabelaContasBancariasProps = {
  loading: boolean;
  dados: IContaBancaria[];
  iniciado: boolean;
};

export type TFiltroContasBancariasProps = {
  onReset?: VoidFunction;
  onSubmit: (cpfCnpj: string) => void;
};

export type TUseObterContasBancarias = {
  loading: boolean;
  resetarResponse: VoidFunction;
  dados: IContaBancaria[];
  iniciado: boolean;
  obterContas: (cpfCnpj: string) => Promise<void>;
};
