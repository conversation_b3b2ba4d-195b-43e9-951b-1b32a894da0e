import {
  PRINCIPAL_USERS,
  TODOS_USUARIOS,
  USER_PROFILES,
} from 'main/features/Auth/config/userProfiles';
import { IMenuItem } from 'main/components/Menu/Sidebar/types';

export const menuAtendimento: IMenuItem = {
  label: 'Atendimento',
  alt: 'atendimento',
  path: '/atendimento',
  icon: 'contact',
  roles: [
    ...TODOS_USUARIOS,
    USER_PROFILES.SAC_CAIXA_TERCEIROS,
    USER_PROFILES.ANALISTA_MANUTENCAO,
    USER_PROFILES.ANALISTA_SAIDA,
    USER_PROFILES.ANALISTA_ENTRADA,
    USER_PROFILES.ANALISTA_CONSULTA,
    USER_PROFILES.ANALISTA_PJ,
  ],
  subItems: [
    {
      label: 'Canal de Solicitação',
      path: '/ocorrencias',
      roles: [
        ...PRINCIPAL_USERS,
        USER_PROFILES.SAC_CAIXA_TERCEIROS,
        USER_PROFILES.ANALISTA_MANUTENCAO,
        USER_PROFILES.ANALISTA_SAIDA,
        USER_PROFILES.ANALISTA_ENTRADA,
        USER_PROFILES.ANALISTA_CONSULTA,
        USER_PROFILES.ANALISTA_PJ,
      ],
      subItems: [
        {
          label: 'Registrar solicitação',
          path: '/registro-ocorrencias/registrar-ocorrencia',
          roles: [
            ...PRINCIPAL_USERS,
            USER_PROFILES.SAC_CAIXA_TERCEIROS,
            USER_PROFILES.ANALISTA_MANUTENCAO,
            USER_PROFILES.ANALISTA_SAIDA,
            USER_PROFILES.ANALISTA_ENTRADA,
            USER_PROFILES.ANALISTA_CONSULTA,
            USER_PROFILES.ANALISTA_PJ,
          ],
        },
        {
          label: 'Consultar solicitação',
          path: '/registro-ocorrencias/consultar-ocorrencia',
          roles: [
            ...PRINCIPAL_USERS,
            USER_PROFILES.SAC_CAIXA_TERCEIROS,
            USER_PROFILES.ANALISTA_MANUTENCAO,
            USER_PROFILES.ANALISTA_SAIDA,
            USER_PROFILES.ANALISTA_ENTRADA,
            USER_PROFILES.ANALISTA_CONSULTA,
            USER_PROFILES.ANALISTA_PJ,
          ],
        },
      ],
    },
    {
      label: 'Webchat',
      path: '/atendimento/webchat',
      roles: [
        ...TODOS_USUARIOS,
        USER_PROFILES.ANALISTA_ENTRADA,
        USER_PROFILES.ANALISTA_CONSULTA,
      ],
    },
    {
      label: 'Telefones Úteis',
      path: '/atendimento/telefones-uteis',
      roles: [
        ...TODOS_USUARIOS,
        USER_PROFILES.SAC_CAIXA_TERCEIROS,
        USER_PROFILES.ANALISTA_MANUTENCAO,
        USER_PROFILES.ANALISTA_SAIDA,
        USER_PROFILES.ANALISTA_ENTRADA,
        USER_PROFILES.ANALISTA_CONSULTA,
        USER_PROFILES.ANALISTA_PJ,
      ],
    },
    {
      label: 'Consulta cadastro Portal do cliente',
      path: '/atendimento/area-logada',
      roles: [
        USER_PROFILES.ANALISTA_MANUTENCAO,
        USER_PROFILES.ANALISTA_TI,
        USER_PROFILES.ANALISTA_PJ,
      ],
    },
  ],
};
