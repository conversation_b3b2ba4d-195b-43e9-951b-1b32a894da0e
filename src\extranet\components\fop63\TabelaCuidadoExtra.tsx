import * as DS from '@cvp/design-system/react';
import * as Const from 'extranet/types/ConstFop63';
import { IFop63Base } from 'extranet/types/InterfacesFop63/IFop63Base';
import { ICuidadoExtraContribuicao } from 'extranet/types/ITableValorContribuicao';
import Table from 'main/components/Table';
import { TextField } from 'main/features/Auth/components';
import masks from 'main/utils/masks';
import { TextAreaGrupos } from '../../features/fops/pages/styles';
import * as Enum from '../../types/enum';

export const TabelaCuidadoExtra: React.FC<IFop63Base> = ({
  setFieldValue,
  values,
}) => {
  return (
    <Table
      columns={[
        {
          name: Const.GRUPOS,
          selector: (row: ICuidadoExtraContribuicao) => row.descricao,
          wrap: true,
          cell: (row, index) => (
            <TextAreaGrupos
              value={row.descricao}
              placeholder={Const.DESCRICAO_GRUPOS}
              maxLength={38}
              onChange={e =>
                setFieldValue(
                  `tabelaCuidadoExtra.${index}.descricao`,
                  e.target.value,
                )
              }
            />
          ),
          center: true,
        },
        {
          name: Const.TIPO_CUIDADO_EXTRA,
          cell: () => (
            <div>
              <TextField type="text" value={'Pecúlio'} disabled />
              <TextField type="text" value={'Pensão Prazo Certo'} disabled />
            </div>
          ),
          wrap: true,
          center: true,
        },
        {
          name: (
            <div>
              <DS.Select
                label=''
                name="tipoDeContribuicaoCuidadoExtra"
                placeholder={Const.SELECIONE}
                value={values.tipoDeContribuicaoCuidadoExtra}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('tipoDeContribuicaoCuidadoExtra', value);
                }}
              >
                {Enum.SELECT_OPTIONS_VALORES_TABELA_CUIDADO_EXTRA.map(
                  opcaoContribuicao => (
                    <DS.Select.Item
                      key={opcaoContribuicao.key}
                      value={opcaoContribuicao.key}
                      text={opcaoContribuicao.value}
                      selected={
                        opcaoContribuicao.key ===
                        values.tipoDeContribuicaoCuidadoExtra
                      }
                    />
                  ),
                )}
              </DS.Select>
            </div>
          ),
          minWidth: '10%',
          selector: (row: ICuidadoExtraContribuicao) =>
            row.valorContribuicaoPeculio,
          cell: (row, index) => (
            <div>
              <TextField
                type="text"
                maxLength={16}
                value={row.valorContribuicaoPeculio}
                onChange={(e: any) =>
                  setFieldValue(
                    `tabelaCuidadoExtra.${index}.valorContribuicaoPeculio`,
                    masks.currency.mask(e.target.value),
                  )
                }
              />
              <TextField
                type="text"
                maxLength={16}
                value={row.valorContribuicaoPensao}
                onChange={(e: any) =>
                  setFieldValue(
                    `tabelaCuidadoExtra.${index}.valorContribuicaoPensao`,
                    masks.currency.mask(e.target.value),
                  )
                }
              />
            </div>
          ),
          width: '350px',
          wrap: true,
          center: true,
        },
        {
          name: Const.EMPRESA_PORCENTAGEM,
          selector: (row: ICuidadoExtraContribuicao) =>
            row.PorcentagemEmpresaContribuicao,
          cell: (_row, index) => (
            <div>
              <TextField
                value={masks.percentage.mask(values.tabelaCuidadoExtra[index].empresaPorcentagem)}
                onChange={(e: any) => {
                  setFieldValue(
                    `tabelaCuidadoExtra.${index}.empresaPorcentagem`,
                    masks.percentage.mask(e.target.value),
                  )
                }
                }
              />
              <TextField
                value={masks.percentage.mask(values.tabelaCuidadoExtra[index].empresaPorcentagemPensaoPrazoCerto)}
                onChange={(e: any) => {
                  setFieldValue(
                    `tabelaCuidadoExtra.${index}.empresaPorcentagemPensaoPrazoCerto`,
                    masks.percentage.mask(e.target.value),
                  )
                }
                }
              />
            </div>
          ),
          wrap: true,
          grow: 1,
        },
        {
          name: Const.PORCENTAGEM_PARTICIPANTE,
          selector: (row: ICuidadoExtraContribuicao) =>
            row.PorcentagemParticipanteContribuicao,
          cell: (_row, index) => (
            <div>
              <TextField
                type="text"
                value={masks.percentage.mask(values.tabelaCuidadoExtra[index].participantePorcentagem)}
                onChange={(e: any) =>
                  setFieldValue(
                    `tabelaCuidadoExtra.${index}.participantePorcentagem`,
                    masks.percentage.mask(e.target.value),
                  )
                }

              />
              <TextField
                type="text"
                value={masks.percentage.mask(values.tabelaCuidadoExtra[index].participantePorcentagemPensaoPrazoCerto)}
                onChange={(e: any) =>
                  setFieldValue(
                    `tabelaCuidadoExtra.${index}.participantePorcentagemPensaoPrazoCerto`,
                    masks.percentage.mask(e.target.value),
                  )
                }
              />
            </div>
          ),
          wrap: true,
          grow: 1,
          center: true,
        },
      ]}
      data={values.tabelaCuidadoExtra as unknown as ICuidadoExtraContribuicao[]}
    />
  );
};
