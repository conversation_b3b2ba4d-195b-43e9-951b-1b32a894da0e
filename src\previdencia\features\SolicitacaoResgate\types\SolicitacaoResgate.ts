import React from 'react';
import { FormikProps } from 'formik';

import { ISelectSearchItem } from 'main/types/Forms/SelectSearch';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import {
  ICalcularResgateDadosEncargo,
  ICalcularResgateResponse,
} from 'previdencia/types/CalculoResgate';
import { IRedirecionamentoHistoricoSolicitacaoLocation } from 'previdencia/types/IRedirecionamentoHistoricoSolicitacaoLocation';
import {
  IAlicotasAgrupadasFundosAliquota,
  IAlicotasAgrupadasResponse,
  TAlicotasAgrupadasFundosAliquotaFactory,
} from 'previdencia/types/AlicotasAgrupadas';
import {
  IListarFundosParaResgateAliquotaOpcoes,
  IListarFundosParaResgateFundosDisponiveis,
  IListarFundosParaResgateMensagensCertificado,
  IListarFundosParaResgateResponse,
} from 'previdencia/types/ConsultaListaFundosParaResgate';
import * as RESPONSE_TYPES from 'previdencia/features/SolicitacaoResgate/types/SolicitacaoResgateResponse';
import { TableColumn } from 'react-data-table-component';

export interface ISolicitacaoResgateContextData {
  dadosListarFundosParaResgate?: IListarFundosParaResgateResponse;
  simulacaoResgateFundos?: IFormikValuesSimulacaoResgate;
  dadosRecuperarBancos?: RESPONSE_TYPES.IRecuperarBancosResponse[];
  dadosListarMotivosResgate?: RESPONSE_TYPES.IListarMotivosResgateResponse[];
  dadosConsultaTiposPagamento?: RESPONSE_TYPES.IConsultarTiposPagamentoResponse;
  resumoAliquota?: IObterResumoAliquotaSelecionadaReturn;
  calculoResgateEtapaDetalheAliquota?: ICalculoResgateEtapaDetalheAliquota;
  dadosEtapaSelecaoAliquota?: IDadosEtapaSelecaoAliquota;
  numeroResgateConsolidado?: string;
  dadosConsultaContribuicaoRegular?: RESPONSE_TYPES.IConsultarContribuicaoRegularResponse;
  listaFundosContribuicaoRegular?: RESPONSE_TYPES.IConsultarContribuicaoRegularFundos[];
  dadosRetornoConfirmacaoResgate?: IDadosRetornoConfirmacaoResgate;
}

export interface IDadosRetornoConfirmacaoResgate {
  solicitarAssinaturaEletronica: boolean;
  statusResgate: string;
  motivoPendenciaResgate: string;
  numeroResgate?: string;
}

export interface IMensagensCertificadoFactory {
  indicadorPrazoDeDiferimentoExpirado: boolean | undefined;
  mensagensCertificado:
    | IListarFundosParaResgateMensagensCertificado[]
    | undefined;
  redirectPrazoDiferimento: () => void;
}

export interface IObservacoesCertificadoProps {
  mensagensCertificado:
    | IListarFundosParaResgateMensagensCertificado[]
    | undefined;
}

export interface IFormikValuesSimulacaoResgate {
  tipoResgate: string;
  valorSolicitado: string;
  listaFundosParaResgate: IListarFundosParaResgateFundosDisponiveis[];
}

export interface IColunasFundoResgateProps {
  formik: FormikProps<IFormikValuesSimulacaoResgate>;
  handleFundoSelecionado: (codigoFundo: string) => void;
  handleTipoResgate: (codigoFundo: string, tipoFundo: string) => void;
  handleValorRetirar: (codigoFundo: string, valorRetirar: string) => void;
  setSelectedTableInputFocus: React.Dispatch<React.SetStateAction<string>>;
  selectedTableInputFocus: string;
  isTipoResgateTotal: boolean;
  isTipoResgateParcial: boolean;
  valorMinimoResgate: number;
}

export interface ISelecionarTipoResgateFactoryParams {
  fundo: IListarFundosParaResgateFundosDisponiveis;
  codigoFundo: string;
  tipoResgate: string;
}

export interface IDistribuirValorAplicacaoFactoryParams {
  fundo: IListarFundosParaResgateFundosDisponiveis;
  codigoFundo: string;
  valorRetirar: string;
}

export interface IDistribuirValorContribuicaoRegularFactoryParams {
  fundo: RESPONSE_TYPES.IConsultarContribuicaoRegularFundos;
  codigoFundo: string;
  valorContribuicao: number;
}

export interface IModificarFundoSelecionadoFactoryParams {
  fundo: IListarFundosParaResgateFundosDisponiveis;
  codigoFundo: string;
}

export interface IModificarFundoParaContribuicaoRegularSelecionadoFactoryParams {
  fundo: RESPONSE_TYPES.IConsultarContribuicaoRegularFundos;
  codigoFundo: string;
  valorContribuicaoRegularlAtual: number;
}

export interface IConfirmaSolicitacaoResgate {
  isSimulacaoPreenchida: boolean;
  isBtnConfirmacaoSimulacaoClicada: boolean;
}

export interface IFiltroSimulacaoResgateProps {
  formik: FormikProps<IFormikValuesSimulacaoResgate>;
  alterarValorSolicitadoTotal: (value: string) => void;
  valorMinimoResgate: number;
  valorMaximoResgate: number;
  valorMinimoPermanencia: number;
  listaTiposResgateFiltro: IListarTiposResgateFiltroReturnFactoryParams[];
  isInputValorSolicitadoDesabilitado: boolean;
  isTipoResgateParcial: boolean;
  isDisabledConfirmarSimulacao: boolean;
  confirmarFiltroDesejadoParaSimulacao: () => void;
  resetarInputFocus: () => void;
}

export interface IColunaDescricaoFundoProps {
  row: IListarFundosParaResgateFundosDisponiveis;
  handleFundoSelecionado: (codigoFundo: string) => void;
  isTipoResgateTotal: boolean;
}

export interface IColunaSaldoTotalProps {
  row: IListarFundosParaResgateFundosDisponiveis;
  isTipoResgateParcial: boolean;
}

export interface IColunaTipoResgateProps {
  row: IListarFundosParaResgateFundosDisponiveis;
  formik: FormikProps<IFormikValuesSimulacaoResgate>;
  handleTipoResgate: (codigoFundo: string, tipoFundo: string) => void;
  isTipoResgateTotal: boolean;
}

export interface IColunaValorRetirarProps {
  row: IListarFundosParaResgateFundosDisponiveis;
  setSelectedTableInputFocus: React.Dispatch<React.SetStateAction<string>>;
  selectedTableInputFocus: string;
  isTipoResgateParcial: boolean;
  isTipoResgateTotal: boolean;
  valorMinimoResgate: number;
  handleValorRetirar: (codigoFundo: string, valorRetirar: string) => void;
}

export interface IPreencherDadosTabelaAliquotaFactory {
  saldoTotal: string;
  saldoTotalBloqueado: string;
  saldoDisponivelParaResgate: string;
  totalAliquotaIrrf: string;
}

export interface IObjetoEmailSolicitacaoResgate {
  tipoEmail: string;
  parametrosEnvio: {
    numeroResgate: string;
    numeroCertificado: string;
  };
}

export interface IModalValoresDetalhadosAliquotaProps {
  open: boolean;
  handleClose: () => void;
  detalhadoAliquota: IObterDetalhadoAliquotaReturnFactory[];
  dadosTabelaDetalheCalculo: Partial<RESPONSE_TYPES.IConsultarDetalheCalculoLista>[];
  objetoEmail: IObjetoEmailSolicitacaoResgate;
}

export type TObterDadosTabelaDetalheCalculoFactory = (
  featureData: ISolicitacaoResgateContextData | null | undefined,
  tipoAliquota: string,
) => Partial<RESPONSE_TYPES.IConsultarDetalheCalculoLista>[];

export interface IObterDetalhadoAliquotaReturnFactory {
  label?: string;
  value?: string;
  mask?: string;
}

export type TObterDetalhadoAliquotaFactory = (
  featureData: ISolicitacaoResgateContextData | null | undefined,
  tipoAliquota: string,
) => IObterDetalhadoAliquotaReturnFactory[];

export interface IModalMensagensResgate {
  openModalAtencaoResgate: boolean;
  isLoadingEfetuarResgate: boolean;
  handleCloseModalAtencaoResgate: () => void;
  efetuarResgate: () => void;
}

export interface IObterResumoAliquotaReturnFactory {
  label?: string;
  value?: string;
  mask?: string;
}

export type TObterResumoAliquotaFactory = (
  featureData: ISolicitacaoResgateContextData | null | undefined,
  tipoAliquota: string,
) => IObterResumoAliquotaReturnFactory[];

export interface ITabelaResumoSimulacaoProps {
  dadosResumo?: IObterResumoAliquotaReturnFactory[];
  disabled?: boolean;
}

export interface IFormikValuesEfetuarResgateNovaConta {
  banco: ISelectSearchItem;
  tipoConta: { codigo: string; descricao: string };
  agencia: string;
  conta: string;
  digitoConta: string;
}

export interface IDefinirTiposContaReturnFactory {
  codigo: string;
  descricao: string;
}

export interface IFormikValuesEfetuarResgate {
  contaExistente: RESPONSE_TYPES.IConsultarTiposPagamentoTipo;
  isNovaConta: boolean;
  novaConta: IFormikValuesEfetuarResgateNovaConta;
  motivoResgate: string;
}

export type TDefinirTiposContaFactory = (
  formik: FormikProps<IFormikValuesEfetuarResgate>,
) => IDefinirTiposContaReturnFactory[];

export interface IFormikValuesDefinicaoContribuicaoRegular {
  listaFundosParaContribuicaoRegular: RESPONSE_TYPES.IConsultarContribuicaoRegularFundos[];
}

export interface IDisclaimerMsgErroResgateProps {
  mensagem: string;
}

export interface IColunasMotivoResgateProps {
  handleMotivoSelecionado: (codigo: string) => void;
  formik: FormikProps<IFormikValuesEfetuarResgate>;
}

export interface IColunasDefinirContribuicaoProps {
  handleFundoSelecionado: (codigoFundo: string) => void;
  selectedTableInputFocus: string;
  setSelectedTableInputFocus: React.Dispatch<React.SetStateAction<string>>;
  handleValorContribuicao: (
    codigoFundo: string,
    valorContribuicao: string,
  ) => void;
}

export interface IParamsLocationData
  extends IRedirecionamentoHistoricoSolicitacaoLocation {
  resgateId: string;
  statusResgate?: string;
  saldo: string;
}

export interface IColunaObservacaoFundoProps {
  row: IListarFundosParaResgateFundosDisponiveis;
}

export interface IColunaNomeBancoProps {
  row: RESPONSE_TYPES.IConsultarTiposPagamentoTipo;
  formik: FormikProps<IFormikValuesEfetuarResgate>;
}

export interface IColunaDescricaoFundoContribuicaoProps {
  row: RESPONSE_TYPES.IConsultarContribuicaoRegularFundos;
  handleFundoSelecionado: (codigoFundo: string) => void;
}

export interface IColunaContribuicaoProps {
  row: RESPONSE_TYPES.IConsultarContribuicaoRegularFundos;
  selectedTableInputFocus: string;
  setSelectedTableInputFocus: React.Dispatch<React.SetStateAction<string>>;
  handleValorContribuicao: (
    codigoFundo: string,
    valorContribuicao: string,
  ) => void;
}

export interface IColunasMotivoResgateDataType {
  selector: string;
  name: string;
  center: boolean;
  maxWidth: string;
  cell: (
    row: RESPONSE_TYPES.IListarMotivosResgateResponse,
  ) => React.JSX.Element;
}

export interface ICardMotivoResgateProps {
  listaMotivosResgate: RESPONSE_TYPES.IListarMotivosResgateResponse[];
  colunasMotivoResgate: Partial<
    TableColumn<RESPONSE_TYPES.IListarMotivosResgateResponse>
  >[];
}

export interface IFormNovaContaBancariaProps {
  formik: FormikProps<IFormikValuesEfetuarResgate>;
  listaTiposConta: IDefinirTiposContaReturnFactory[];
  dadosRecuperarBancos: RESPONSE_TYPES.IRecuperarBancosResponse[];
  quantidadeCharInput: string;
  novaConta: IFormikValuesEfetuarResgateNovaConta;
}

export interface IColunasInfoBancariasDataType {
  name: string;
  selector: string;
  minWidth: string;
  wrap: boolean;
  cell: (row: RESPONSE_TYPES.IConsultarTiposPagamentoTipo) => React.JSX.Element;
  maxWidth: string;
  center: boolean;
}

export interface IInformacoesBancariasProps {
  contasBancariasValidasParaResgate: RESPONSE_TYPES.IConsultarTiposPagamentoTipo[];
  colunas: Partial<TableColumn<RESPONSE_TYPES.IConsultarTiposPagamentoTipo>>[];
}

export interface IUseHandleEfetuarSolicitacaoResgateParams {
  novaConta: IFormikValuesEfetuarResgateNovaConta;
  formik: FormikProps<IFormikValuesEfetuarResgate>;
}

export interface ICardResumoDistribuicaoFundosResgateProps {
  formik: FormikProps<IFormikValuesSimulacaoResgate>;
}

export interface ICardResumoDistribuicaoContribuicaoRegularProps {
  formik: FormikProps<IFormikValuesDefinicaoContribuicaoRegular>;
  valorContribuicaoRegularlAtual: number;
}

export interface IModalPendenciaSolicitacaoResgateProps {
  open: boolean;
  handleClose: () => void;
  motivoPendenciaResgate: string;
}

export interface IDadosTabelaFundosAliquota {
  fundosAliquotaRegressivo: Partial<TAlicotasAgrupadasFundosAliquotaFactory>[];
  fundosAliquotaProgressivo: Partial<TAlicotasAgrupadasFundosAliquotaFactory>[];
}

export interface ITabelasAliquotasAgrupadasResgateProps {
  loading: boolean;
  dadosTabelaFundosAliquota: IDadosTabelaFundosAliquota | undefined;
  exibirAliquotas: TExibirAliquotas;
}

export interface IExibirAliquotasReturn {
  condicionalTabelas: boolean;
  gridTabelas: {
    xs?: number;
    xl?: number;
  };
}

export type TExibirAliquotas = (
  tipoAliquota: IListarFundosParaResgateAliquotaOpcoes,
) => IExibirAliquotasReturn;

export interface IResultadoAliquota {
  fundos: Partial<IAlicotasAgrupadasFundosAliquota>[] | undefined;
  calculo: ICalcularResgateDadosEncargo | undefined;
}

export interface ICalculoResgateEtapaDetalheAliquota {
  progressivo: ICalcularResgateDadosEncargo | undefined;
  regressivo: ICalcularResgateDadosEncargo | undefined;
}

export interface IDadosAliquotaPorRegime {
  calculo: ICalcularResgateDadosEncargo | undefined;
  resumo: RESPONSE_TYPES.IConsultarResumoAliquotaResponse | undefined;
  detalhado: RESPONSE_TYPES.IConsultarDetalheCalculoResponse | undefined;
}

export interface IDadosEtapaSelecaoAliquota {
  calculoAliquotaProgressiva: ICalcularResgateDadosEncargo | undefined;
  calculoAliquotaRegressiva: ICalcularResgateDadosEncargo | undefined;
  resumoAliquotaProgressiva:
    | RESPONSE_TYPES.IConsultarResumoAliquotaResponse
    | undefined;
  resumoAliquotaRegressiva:
    | RESPONSE_TYPES.IConsultarResumoAliquotaResponse
    | undefined;
  detalhamentoAliquotaProgressiva:
    | RESPONSE_TYPES.IConsultarDetalheCalculoResponse
    | undefined;
  detalhamentoAliquotaRegressiva:
    | RESPONSE_TYPES.IConsultarDetalheCalculoResponse
    | undefined;
}

export interface IObterDetalhamentoAliquotaFactory {
  formik: FormikProps<IFormikValuesSimulacaoResgate>;
  aliquotaAtual: string;
  calcularResgate: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<ICalcularResgateResponse> | undefined>;
  consultarResumoAliquota: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarResumoAliquotaResponse>
    | undefined
  >;
  consultarDetalheCalculo: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarDetalheCalculoResponse>
    | undefined
  >;
}

export interface IObterResumoAliquotaSelecionadaReturn {
  resumo: IObterResumoAliquotaReturnFactory[] | undefined;
  tipoAliquota: string | undefined;
}

export interface IObterResumoAliquotaSelecionada {
  indicadorPermiteEditarAliquota: boolean;
  aliquotaAtual: string;
  resumoAliquotaProgressiva: IObterResumoAliquotaReturnFactory[];
  resumoAliquotaRegressiva: IObterResumoAliquotaReturnFactory[];
  dadosResumoSelecionado: IObterResumoAliquotaReturnFactory[];
  opcaoRegimeTributario: string;
}

export interface IDadosResumoAliquotaSelecionada {
  resumo: IObterResumoAliquotaReturnFactory[] | undefined;
  tipoAliquota: string | undefined;
}

export interface IDefaultFactoryConfirmarSimulacaoParams {
  formik: FormikProps<IFormikValuesSimulacaoResgate>;
  calcularResgate: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<ICalcularResgateResponse> | undefined>;
  consultarResumoAliquota: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarResumoAliquotaResponse>
    | undefined
  >;
  consultarDetalheCalculo: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarDetalheCalculoResponse>
    | undefined
  >;
}

export interface IModalDetalhesAliquotaProps {
  open: boolean;
  handleClose: () => void;
  isLoadingDetalhesAliquota: boolean;
  dadosTabelaFundosAliquota: IDadosTabelaFundosAliquota | undefined;
  exibirAliquotas: (
    tipoAliquota: IListarFundosParaResgateAliquotaOpcoes,
  ) => IExibirAliquotasReturn;
}

export interface IObterCalculoRegimeFactory {
  tipoRegimeTributario: string;
  calcularResgate: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<ICalcularResgateResponse> | undefined>;

  obterAlicotasAgrupadas: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<IAlicotasAgrupadasResponse[]> | undefined>;
  dadosFundosParaResgate: IListarFundosParaResgateResponse;
}

export interface IDefaultFactoryDetalhesAliquotaParams {
  calcularResgate: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<ICalcularResgateResponse> | undefined>;

  obterAlicotasAgrupadas: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<IHandleReponseResult<IAlicotasAgrupadasResponse[]> | undefined>;

  dadosFundosParaResgate: IListarFundosParaResgateResponse;
}

export interface IDesabilitarBotaoRealizarResgateParams {
  indicadorPermiteEditarAliquota: boolean;
  dadosResumoSelecionado: IObterResumoAliquotaReturnFactory[];
  opcaoRegimeTributario: string;
}

export interface IListarTiposResgateFiltroReturnFactoryParams {
  id: string;
  value: string;
}
