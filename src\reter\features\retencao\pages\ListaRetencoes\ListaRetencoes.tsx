import React, { useEffect, useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { <PERSON>, Button } from '@cvp/design-system/react';
import Table, { setTableType } from 'main/components/Table';
import Icon from 'main/components/Icon';
import SkeletonLoading from 'main/components/SkeletonLoading';
import TableFilter from 'main/components/Table/TableFilter/TableFilter';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import masks from 'main/utils/masks';
import { formatarValorPadraoBrasileiro } from 'main/utils/money';
import ModalMotivo from 'reter/features/retencao/components/ModalMotivo';
import { COLUNAS_TABELA_RECURSOS_PENDENTES_DEVOLUCAO } from 'reter/features/retencao/constants/constants';
import { filterOptions } from 'reter/features/retencao/factories/filterOptionsFactory';
import { usePendenteDevolucao } from 'reter/features/retencao/hooks/usePendenteDevolucao';
import * as S from 'reter/features/retencao/pages/ListaRetencoes/styles';
import { ModalFlag } from 'reter/features/retencao/types/ModalMotivo';
import { ENDPOINT_RETER } from '../../constants/endpoint';
import {
  ResponsePendenteDevolucao,
  ResponsePendenteDevolucaoColum,
} from "../../types/ResponsePendenteDevolucao";

const ListaRetencoes: React.FC = () => {
  const {
    loading,
    response: listaPendenteDevolucao,
    obterListaPendenteDevolucao,
  } = usePendenteDevolucao();

  const navigate = useNavigate();

  const [openModalMotivo, setOpenModalMotivo] = useState<ModalFlag>({
    open: false,
  });

  useEffect(() => {
    obterListaPendenteDevolucao();
  }, []);

  const handleEditarContaBancaria = (numeroResgate: string): void => {
    navigate(ENDPOINT_RETER.DADOS_BANCARIO, {
      state: { numeroResgate },
    });
  };

  const dadosTabelaRecursosPendentesDevolucao = ():
    | ResponsePendenteDevolucaoColum[]
    | undefined => {
    return listaPendenteDevolucao?.map(
      item =>
        ({
          ...item,
          id: item.numeroResgate,
          dadosPessoa: [
            {
              ...item.dadosPessoa[0],
              cpf: masks.cpf.mask(item.dadosPessoa[0].cpf),
            },
          ],
          nome: item.dadosPessoa[0].nome,
          cpf: item.dadosPessoa[0].cpf,
          valorReceber: formatarValorPadraoBrasileiro(item.valorReceber),
          motivo: item.motivo,
          motivoElement: (
            <S.BotaoVisualizar
              variant="text"
              onClick={() =>
                setOpenModalMotivo({ open: true, message: item.motivo })
              }
            >
              Visualizar
            </S.BotaoVisualizar>
          ),
          idElement: (
            <Button
              onClick={() => handleEditarContaBancaria(item.numeroResgate)}
            >
              Atualizar
            </Button>
          ),
        }) as ResponsePendenteDevolucaoColum,
    );
  };

  if (loading) {
    return <SkeletonLoading lines={4} />;
  }

  return (
    <>
      <S.CardContainer>
        <Card.Content>
          <S.DuvidasContainer alignItems="center">
            <S.TextoDuvidas variant="body02-ms" color="text">
              <Icon name="questionHelp" /> Está com dúvidas? &nbsp;
              <Link to={ENDPOINT_RETER.FAQ_RETENCAO}>
                Clique aqui e saiba mais!
              </Link>
            </S.TextoDuvidas>
          </S.DuvidasContainer>
          <TableFilter
            dataToFilter={tryGetValueOrDefault(
              [dadosTabelaRecursosPendentesDevolucao()],
              [],
            )}
            filterTextPartial
            filterOptions={filterOptions}
          >
            {filteredData => (
              <Table
                noHeader
                columns={COLUNAS_TABELA_RECURSOS_PENDENTES_DEVOLUCAO}
                data={setTableType<ResponsePendenteDevolucao[]>(filteredData)}
                responsive
                pagination
                paginationPerPage={10}
                noDataComponent="Não há dados para serem exibidos."
                paginationComponentOptions={{
                  rowsPerPageText: 'Items por página',
                  rangeSeparatorText: 'de',
                }}
              />
            )}
          </TableFilter>
        </Card.Content>
      </S.CardContainer>
      <ModalMotivo
        data={openModalMotivo}
        handleClose={() => setOpenModalMotivo({ open: false })}
      />
    </>
  );
};

export default ListaRetencoes;
