import { useAuth } from 'main/features/Auth/hooks';
import { usePecoRecuperarHierarquiaAgencia } from 'main/features/HierarquiaAgencia/hooks/usePecoRecuperarHierarquiaAgencia';
import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import * as CONSTS from 'painelInadimplencia/constants/constants';
import { RELATORIO_INADIMPLENCIA } from 'painelInadimplencia/constants/relatorioInadimplencia';
import { montarRelatorioInadimplencia } from 'painelInadimplencia/factories/montarRelatorioInadimplencia';
import usePecoConsultarInadimplencia from 'painelInadimplencia/hooks/usePecoConsultarInadimplencia';
import useSegmento from 'painelInadimplencia/hooks/useSegmento';
import { IDadosInadimplenciaPayload } from 'painelInadimplencia/types/ConsultarInadimplenciaRequest';
import { useEffect, useRef, useState } from 'react';

const useRelatorioInadimplencia = () => {
  const { obterSegmentoRota } = useSegmento();
  const { user } = useAuth();
  const [tipoVisao, setTipoVisao] = useState<string>('');
  const [unidade, setUnidade] = useState<number>();
  const [step, setStep] = useState([user?.agenciaVinculada]);
  const [stepSegmento, setStepSegmento] = useState([obterSegmentoRota()]);
  const [stepTipoVisao, setStepTipoVisao] = useState<string[]>([]);
  const ultimoCodRef = useRef('');
  const ultimoSegmentoRef = useRef('');
  const ultimoTipoVisaoRef = useRef('');
  const [isDisabled, setIsDisabled] = useState<boolean>(true);
  const [payloadConsulta, setPayloadConsulta] =
    useState<IDadosInadimplenciaPayload>(
      montarRelatorioInadimplencia(obterSegmentoRota()),
    );

  const filterFormaPagamento = () => {
    if (payloadConsulta?.Segmento === CONSTS.PREV)
      return CONSTS.FormaPagamentoFilterPREV;

    return CONSTS.FormaPagamentoFilterVIDA;
  };

  const {
    dadosInadimplencia,
    loadingDadosInadimplencia: loading,
    fetchData: consultarInadimplencia,
  } = usePecoConsultarInadimplencia(payloadConsulta);

  const {
    response: dadosHierarquiaAgencia,
    fetchData: fetchDataHierarquiaAgencia,
  } = usePecoRecuperarHierarquiaAgencia(String(unidade));

  const isDisabledBotaoVoltar: boolean = checkIfSomeItemsAreTrue([
    loading,
    isDisabled,
  ]);

  const handleConsulta = (cod?: string) => {
    if (cod) {
      if (
        checkIfAllItemsAreTrue([
          !!ultimoCodRef.current,
          !!ultimoSegmentoRef.current,
          !!ultimoTipoVisaoRef.current,
        ])
      ) {
        setStep([...step, ultimoCodRef.current]);
        setStepSegmento([...stepSegmento, ultimoSegmentoRef.current]);
      }

      setIsDisabled(false);

      ultimoCodRef.current = cod;
      ultimoSegmentoRef.current = payloadConsulta?.Segmento;

      setPayloadConsulta({ ...payloadConsulta, Codigo: cod });
    } else {
      setPayloadConsulta({ ...payloadConsulta, Codigo: unidade });
    }
  };

  const clearFilters = () => {
    setPayloadConsulta((prevState: IDadosInadimplenciaPayload) => ({
      ...prevState,
      ...RELATORIO_INADIMPLENCIA,
    }));
  };

  const handleBackStep = () => {
    const ultimoCodigo = step[step.length - 1];
    const ultimoSegmento = stepSegmento[stepSegmento.length - 1];
    const ultimoTipoVisao = stepTipoVisao[stepTipoVisao.length - 1];
    const penultimoTipoVisao = stepTipoVisao[stepTipoVisao.length - 2];

    clearFilters();

    if (checkIfAllItemsAreTrue([step.length > 0, stepSegmento.length > 0])) {
      setStep(step.slice(0, -1));
      setStepSegmento(stepSegmento.slice(0, -1));
      setStepTipoVisao(stepTipoVisao.slice(0, -1));

      ultimoCodRef.current = '';
      ultimoSegmentoRef.current = '';
      ultimoTipoVisaoRef.current = tryGetValueOrDefault([ultimoTipoVisao], '');

      setTipoVisao(tryGetValueOrDefault([penultimoTipoVisao], ''));

      setPayloadConsulta({ Segmento: ultimoSegmento, Codigo: ultimoCodigo });

      if (
        checkIfAllItemsAreTrue([
          ultimoSegmento === obterSegmentoRota(),
          ultimoCodigo === user?.agenciaVinculada,
        ])
      ) {
        setIsDisabled(true);
      }
    } else {
      setStep([]);
      setStepSegmento([]);
      setStepTipoVisao(penultimoTipoVisao ? [penultimoTipoVisao] : []); // Reseta para o penúltimo valor
      setPayloadConsulta({
        Segmento: obterSegmentoRota(),
        Codigo: user?.agenciaVinculada,
      });
      setIsDisabled(true);
    }
  };

  const camposFiltrosVazios = (
    payload: IDadosInadimplenciaPayload,
  ): boolean => {
    const camposParaVerificar = [
      CONSTS.PAYLOAD_FILTROS.PERIODO_DE_PAGAMENTO,
      CONSTS.PAYLOAD_FILTROS.FORMA_DE_PAGAMENTO,
      CONSTS.PAYLOAD_FILTROS.DOCUMENTO,
      CONSTS.PAYLOAD_FILTROS.NUMERO_DE_CONTRATO,
    ] as Array<keyof IDadosInadimplenciaPayload>;

    return camposParaVerificar.every(campo => {
      const valor = payload[campo];
      return checkIfSomeItemsAreTrue([
        !valor,
        typeof valor === 'string' && !valor.trim(),
      ]);
    });
  };
  const naoCotemFiltros = camposFiltrosVazios(payloadConsulta);

  useEffect(() => {
    if (stepTipoVisao.length) {
      setTipoVisao(stepTipoVisao[stepTipoVisao.length - 1]);
    } else {
      setTipoVisao('');
    }
  }, [stepTipoVisao]);

  useEffect(() => {
    if (dadosInadimplencia?.tipoHierarquia)
      setTipoVisao(dadosInadimplencia?.tipoHierarquia);
    if (dadosInadimplencia?.unidadeInicial)
      setUnidade(dadosInadimplencia?.unidadeInicial);
  }, [dadosInadimplencia]);

  useEffect(() => {
    if (tipoVisao !== CONSTS.TiposVisao.AG) {
      consultarInadimplencia();
    }
  }, [payloadConsulta]);

  useEffect(() => {
    fetchDataHierarquiaAgencia();
  }, [unidade]);

  return {
    tipoVisao,
    unidade,
    setPayloadConsulta,
    consultarInadimplencia,
    payloadConsulta,
    filterFormaPagamento,
    loading,
    handleConsulta,
    isDisabled,
    handleBackStep,
    clearFilters,
    dadosInadimplencia,
    isDisabledBotaoVoltar,
    dadosHierarquiaAgencia: dadosHierarquiaAgencia?.entidade,
    naoCotemFiltros,
  };
};

export default useRelatorioInadimplencia;
