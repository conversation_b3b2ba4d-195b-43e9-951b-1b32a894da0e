import { IResponseListarMelhoresTaxaFamiliaFundo } from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/types/IListarMelhoresTaxasPeco';

export const fundosDiferenciadosLabel: IResponseListarMelhoresTaxaFamiliaFundo[] =
  [
    {
      familia: 0,
      descricao: 'Fundos Diferenciados',
      efundoDiferenciado: 'x',
    },
  ];
export const fundosTradicionaisLabel: IResponseListarMelhoresTaxaFamiliaFundo[] =
  [
    {
      familia: 0,
      descricao: 'Fundos Tradicionais',
      efundoDiferenciado: 'z',
    },
  ];
