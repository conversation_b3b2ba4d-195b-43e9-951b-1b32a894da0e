import * as DS from '@cvp/design-system/react';
import { IFormBase63 } from 'extranet/types/InterfacesFop63/IFormBase63';
import { Field } from 'formik';
import { TitleSection } from 'main/styles/GlobalStyle';
import masks from 'main/utils/masks';
import * as S from '../../../extranet/features/fops/pages/styles';
import * as Enum from '../../types/enum';

export const FormDadosResponsavel: React.FC<IFormBase63> = ({
  setFieldValue,
  values,
  handleBlur,
  errors,
}) => {
  return (
    <div>
      <DS.Accordion open>
        <S.AccordionItem
          title={
            <TitleSection>
              {Enum.Titulos.DadosResponsavel}
            </TitleSection>}
        >
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="nomeCompletoResponsavel"
              maxLength={50}
              label={Enum.DadosResponsavelPreenchimento.NomeCompleto}
              component={DS.TextField}
              value={values.nomeCompletoResponsavel}
              error={errors.nomeCompletoResponsavel}
              errorMessage={errors.nomeCompletoResponsavel}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('nomeCompletoResponsavel', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="telefoneResponsavel"
              label={Enum.DadosResponsavelPreenchimento.Telefone}
              component={DS.TextField}
              value={masks.phone.mask(values.telefoneResponsavel)}
              error={errors.telefoneResponsavel}
              errorMessage={errors.telefoneResponsavel}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('telefoneResponsavel', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="emailResponsavel"
              maxLength={50}
              label={Enum.DadosResponsavelPreenchimento.EmailInfomativo}
              component={DS.TextField}
              value={values.emailResponsavel}
              error={errors.emailResponsavel}
              errorMessage={errors.emailResponsavel}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('emailResponsavel', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
        </S.AccordionItem>
      </DS.Accordion>
    </div>
  )
}
