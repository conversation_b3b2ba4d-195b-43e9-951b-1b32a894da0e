import { mapBreadCrumbsFromRoutes } from 'main/components/Layout/AppBreadcrumb/mapBreadcrumb';
import PrivateRoute from 'main/components/Route';
import { TPrivateRoute } from 'main/components/Route/PrivateRoute';
import { USER_PROFILES } from 'main/features/Auth/config/userProfiles';
import React from 'react';
import { Route, Routes } from 'react-router-dom';
import ConsultaResultado from '../features/consultarValorIS/pages/ConsultaResultado';
import { ConsultarContas } from 'ferramentas/features/consultarContas/pages/ConsultarContas';

const routes: TPrivateRoute[] = [
  {
    path: '/ferramentas/consultar-valor-is',
    exact: true,
    component: ConsultaResultado,
    key: 'consultar-valor-is',
    authenticated: true,
    breadcrumb: 'Consultar valor IS',
    requiredRoles: [
      USER_PROFILES.ANALISTA_TI,
      USER_PROFILES.ANALISTA_MANUTENCAO,
      USER_PROFILES.ANALISTA_PJ,
    ],
  },
  {
    path: '/ferramentas/consultar-contas-bancarias',
    exact: true,
    component: ConsultarContas,
    key: 'consultar-contas-bancarias',
    authenticated: true,
    breadcrumb: 'Listar contas bancárias',
    requiredRoles: [USER_PROFILES.ANALISTA_TI, USER_PROFILES.ANALISTA_ENTRADA],
  },
];

/**
 * Utilizar caso seja necessário personalizar o nome dos breadcrumbs
 */
export const ferramentasBreadcrumbs = {
  ...mapBreadCrumbsFromRoutes(routes),
  '/ferramentas': null,
};

const RotasFerramentas = (): React.ReactElement => (
  <Routes>
    {routes.map(route => (
      <Route
        path={route.path}
        element={
          <PrivateRoute
            key={route.key}
            exact={route.exact}
            component={route.component}
            authenticated={route.authenticated}
            requiredRoles={route.requiredRoles}
          />
        }
      />
    ))}
  </Routes>
);

export default RotasFerramentas;
