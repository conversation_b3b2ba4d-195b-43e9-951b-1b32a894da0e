import { CONTENT_TYPE } from 'main/constants/contentType';
import {
  BASE_FILE_NAME,
  FORMATO_FACTORY,
} from 'painelPrestamistaDeclinio/constants/constants';
import { TFormatoArquivoDeclinioPrestamistaDetalhesAgenciaFactory } from 'painelPrestamistaDeclinio/types/TFormatoArquivoDeclinioPrestamistaDetalhesAgenciaFactory';

export const FormatoArquivoDeclinioPrestamistaDetalhesAgenciaFactory = (
  formato: number,
): TFormatoArquivoDeclinioPrestamistaDetalhesAgenciaFactory => {
  const formatos: Record<number, { filename: string; type: string }> = {
    [FORMATO_FACTORY.CSV]: {
      filename: `${BASE_FILE_NAME}.csv`,
      type: CONTENT_TYPE.CSV,
    },
    [FORMATO_FACTORY.JSON]: {
      filename: `${BASE_FILE_NAME}.json`,
      type: CONTENT_TYPE.JSON,
    },
    [FORMATO_FACTORY.XML]: {
      filename: `${BASE_FILE_NAME}.xml`,
      type: CONTENT_TYPE.XML,
    },
    [FORMATO_FACTORY.EXCEL]: {
      filename: `${BASE_FILE_NAME}.xlsx`,
      type: CONTENT_TYPE.EXCEL,
    },
  };

  if (!formatos[formato]) {
    throw new Error(`Formato de arquivo inválido: ${formato}`);
  }

  return formatos[formato];
};
