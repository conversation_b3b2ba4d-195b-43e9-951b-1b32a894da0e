import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';
import * as RESPONSE_TYPES from 'registroOcorrenciaASC/features/registrarOcorrencia/types/RegistrarOcorrenciasResponse';
import {
  IAtualizacaoCliente,
  ICriacaoCamposDinamicos,
  ICriacaoCliente,
  IPayloadAberturaOcorrencia,
} from './RegistrarOcorrencias';

export interface IUseConsultarCliente {
  dadosConsultaCliente:
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarClienteResponse>
    | undefined;
  loadingDadosConsultaCliente: boolean;
  obterDadosConsultaCliente: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    IHandleReponseResult<RESPONSE_TYPES.IConsultarClienteResponse> | undefined
  >;
}

export interface IUseCriarCliente {
  dadosCriacaoCliente:
    | IHandleReponseResult<RESPONSE_TYPES.ICriarClienteResponse>
    | undefined;
  loadingDadosCriacaoCliente: boolean;
  criarCliente: (
    dynamicPayload?: ICriacaoCliente,
  ) => Promise<
    IHandleReponseResult<RESPONSE_TYPES.ICriarClienteResponse> | undefined
  >;
}

export interface IUseAtualizarCliente {
  dadosAtualizacaoCliente:
    | IHandleReponseResult<RESPONSE_TYPES.IAtualizarClienteResponse>
    | undefined;
  loadingDadosAtualizacaoCliente: boolean;
  atualizarCliente: (
    dynamicPayload?: IAtualizacaoCliente,
  ) => Promise<
    IHandleReponseResult<RESPONSE_TYPES.IAtualizarClienteResponse> | undefined
  >;
}

export interface IUseConsultarDuplicidadeSolicitacao {
  dadosSolicitacoesDuplicadas:
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarDuplicidadeSolicitacaoResponse>
    | undefined;
  loadingDadosSolicitacoesDuplicadas: boolean;
  obterSolicitacoesDuplicadas: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarDuplicidadeSolicitacaoResponse>
    | undefined
  >;
}

export interface IUseConsultarAssunto {
  dadosConsultaAssunto:
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarAssuntoResponse>
    | undefined;
  loadingDadosConsultaAssunto: boolean;
  obterDadosAssunto: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    IHandleReponseResult<RESPONSE_TYPES.IConsultarAssuntoResponse> | undefined
  >;
  setDadosConsultaAssunto: React.Dispatch<
    React.SetStateAction<
      IHandleReponseResult<RESPONSE_TYPES.IConsultarAssuntoResponse> | undefined
    >
  >;
}

export interface IUseConsultarSubAssunto {
  dadosConsultaSubAssunto:
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarSubAssuntoResponse>
    | undefined;
  loadingDadosConsultaSubAssunto: boolean;
  obterDadosSubAssunto: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarSubAssuntoResponse>
    | undefined
  >;
  setDadosConsultaSubAssunto: React.Dispatch<
    React.SetStateAction<
      | IHandleReponseResult<RESPONSE_TYPES.IConsultarSubAssuntoResponse>
      | undefined
    >
  >;
}

export interface IUseConsultarCamposDinamicos {
  dadosConsultaCamposDinamicos:
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarCamposDinamicosResponse>
    | undefined;
  loadingDadosConsultaCamposDinamicos: boolean;
  obterCamposDinamicos: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarCamposDinamicosResponse>
    | undefined
  >;
}

export interface IUseCriarCamposDinamicos {
  dadosCriacaoCamposDinamicos:
    | IHandleReponseResult<RESPONSE_TYPES.ICriarCamposDinamicosResponse>
    | undefined;
  loadingDadosCriacaoCamposDinamicos: boolean;
  criarCamposDinamicos: (
    dynamicPayload?: ICriacaoCamposDinamicos,
  ) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.ICriarCamposDinamicosResponse>
    | undefined
  >;
}

export interface IUseBuscarEnderecoCep {
  dadosEnderecoPeloCep:
    | IHandleReponseResult<RESPONSE_TYPES.IBuscarEnderecoCepResponse>
    | undefined;
  loadingDadosEnderecoPeloCep: boolean;
  obterEnderecoPeloCep: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    IHandleReponseResult<RESPONSE_TYPES.IBuscarEnderecoCepResponse> | undefined
  >;
}

export interface IUseCriarProtocolo {
  dadosProtocolo:
    | IHandleReponseResult<RESPONSE_TYPES.ICriarProtocoloResponse>
    | undefined;
  loadingDadosProtocolo: boolean;
  obterProtocolo: (
    dynamicPayload?: IPayloadAberturaOcorrencia,
  ) => Promise<
    IHandleReponseResult<RESPONSE_TYPES.ICriarProtocoloResponse> | undefined
  >;
}

export interface IUseConsultarDadosBICliente {
  dadosContratos:
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarDadosBIClienteResponse[]>
    | undefined;
  loadingDadosContratos: boolean;
  obterContratos: (dynamicPayload?: {
    [key: string]: unknown;
  }) => Promise<
    | IHandleReponseResult<RESPONSE_TYPES.IConsultarDadosBIClienteResponse[]>
    | undefined
  >;
}
