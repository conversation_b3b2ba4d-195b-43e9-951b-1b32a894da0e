import { FOPS } from 'extranet/features/fops/constants/consts';
import {
  IUseListarFops,
  IUseListarFopsReturn,
} from 'extranet/types/ListarFops';
import { IResponseObterListaFopsAtivos } from 'main/features/Administracao/types/IFops';
import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';

export const useListarFops = ({
  tipoFop,
  dataToList,
  abrirFop,
}: IUseListarFops): IUseListarFopsReturn => {
  const arquivoValido = (fop: IResponseObterListaFopsAtivos): boolean =>
    !!fop.dadosArquivos?.some(arquivo =>
      checkIfAllItemsAreTrue([
        arquivo.indicadorUsoDoArquivo === FOPS.INDICADOR_USO_ARQUIVO.DOWNLOAD,
        !!arquivo.codigoIdentificadorUnico,
      ]),
    );

  const filterFops = (fop: IResponseObterListaFopsAtivos): boolean =>
    checkIfAllItemsAreTrue([
      checkIfSomeItemsAreTrue([!tipoFop, fop.tipo === tipoFop]),
      fop.codigo !== FOPS.CODIGOS.FOP_62,
      fop.codigo !== FOPS.CODIGOS.FOP_63,
      arquivoValido(fop),
    ]);

  const listaFops: IResponseObterListaFopsAtivos[] = tryGetValueOrDefault(
    [dataToList],
    [] as IResponseObterListaFopsAtivos[],
  ).filter(fop => filterFops(fop));

  const handleFopClick = (item: IResponseObterListaFopsAtivos): void => {
    abrirFop(item);
  };

  return {
    listaFops,
    handleFopClick,
  };
};
