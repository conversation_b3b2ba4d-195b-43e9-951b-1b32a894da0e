import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { api } from 'main/services';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import { act } from 'react-dom/test-utils';
import {
  getRetornoEnvioCodigo,
  getRetornoValidacaoCodigo,
} from '../../mocks/mockEnvioValidacaoCodigo';
import ListagemSegurosCancelados from '../ListagemSegurosCancelados';

describe('Prestamista - ModalReversaoFluxoPrincipalSucesso.spec', () => {
  it('executa o fluxo principal da reversao do cancelamento', () => {
    vi.spyOn(api, 'post').mockReturnValue(
      Promise.resolve({
        data: {
          dados: {
            entidade: [
              {
                numeroPropostaEstipulante: '389945',
                numeroPropostaEmissao: null,
                codTipoOperacaoFinanceira: 'R',
                statusCobranca: '1',
                statusMotivoParcela: '3',
                statusReversao: 'DENTRO DO PRAZO DE RETENÇÃO',
                dtaFimReversao: '2022-02-20T00:00:00',
                txtDescricaoReversao: 'EM PROCESSAMENTO              ',
                qtdDiasReversao: '5',
                numeroLinhaDoProduto: '2',
                numContrato: '28272964',
                numeroPesOperador: null,
                numeroProposta: null,
                codigoDoEstipulante: '2',
                cpfCnpj: '61768278091',
                valorImportanciaSegurada: 4499100,
                numeroContratoTerceiro: null,
                valorPremio: 423170,
                codigoAgenciaVenda: '3445',
                dataHoraEmissaoDaProposta: '2017-06-13T00:00:00',
                descricaoStatusProposta: null,
                dataFimVigencia: null,
                dataInicioVigencia: null,
                descricaoPeriodicidadeCobranca: null,
                valorPremioLiquido: null,
                numeroCertificado: null,
                nomeSegurado:
                  'MARCELO DA CUNHA COLL OLIVEIRA                                                                      ',
                numeroCelular: null,
                dddCelular: null,
                statusContrato: 'E',
                email: null,
                dataCancelamento: '2022-02-11T00:00:00',
              },
            ],
            sucesso: true,
            mensagens: [
              {
                codigo: 'INFCBSVC0',
                descricao: 'Validação da conta bancária realizada com sucesso.',
              },
            ],
          },
        },
      }),
    );

    const { container } = render(
      <RenderPageWithThemeProviderAndRouter>
        <QueryClientProvider client={new QueryClient()}>
          <ListagemSegurosCancelados />
        </QueryClientProvider>
      </RenderPageWithThemeProviderAndRouter>,
    );

    waitFor(
      () => {
        expect(container.querySelector('div[role="rowgroup"]')).not.toBeNull();
        const buttonReversao = container.querySelectorAll(
          'button[id^="reversaoButton"',
        )[0];
        expect(buttonReversao).not.toBeNull();
        act(() => {
          userEvent.click(buttonReversao);
        });

        waitFor(
          () => {
            const modal = screen.getByTestId('modalReversaoCancelamento');
            const buttonConfirmarReversao =
              screen.getByTestId('confirmarReversao');

            expect(modal).toBeInTheDocument();
            expect(buttonConfirmarReversao).toBeInTheDocument();

            act(() => {
              userEvent.click(buttonConfirmarReversao);
            });
            const modalInputCelular = screen.getByTestId('modalInputCelular');
            const inputCelular = screen.getByTestId('inputCelular');
            const buttonEnviarCodigo = screen.getByTestId('enviarCodigo');

            expect(modalInputCelular).toBeInTheDocument();

            vi.spyOn(api, 'post').mockReturnValue(
              Promise.resolve({
                data: getRetornoEnvioCodigo(),
              }),
            );

            act(() => {
              userEvent.type(inputCelular, '61982880470');
              userEvent.click(buttonEnviarCodigo);
            });

            const modalValidacaoCodigo = screen.getByTestId(
              'modalValidacaoCodigo',
            );
            const inputCodigoRecebido = screen.getByTestId(
              'inputCodigoRecebido',
            );
            const buttonValidarCodigo = screen.getByTestId(
              'buttonValidarCodigo',
            );

            expect(modalValidacaoCodigo).toBeInTheDocument();

            vi.spyOn(api, 'post').mockReturnValue(
              Promise.resolve({
                data: getRetornoValidacaoCodigo(),
              }),
            );

            act(() => {
              userEvent.type(inputCodigoRecebido, '123456789');
              userEvent.click(buttonValidarCodigo);
            });

            const modalFeedback = screen.getByTestId('modalFeedback');
            const mensagemFeedback = screen.getByTestId('mensagemFeedback');
            expect(mensagemFeedback).toHaveTextContent(
              'Reversão do cancelamento concluída com sucesso!',
            );
            expect(modalFeedback).toBeInTheDocument();
          },
          { timeout: 1000 },
        );
      },
      { timeout: 1000 },
    );
  });
});
