import { ISelectOption } from '../features/fops/pages/ISelectOption';

export enum EnumFopPrevidencia {
  prevManutencao = 'prev_manutencao',
  Suspenso = 'U',
  Beneficio = 'B',
}

export enum Titulos {
  AnexoFop = '*Anexe aqui o FOP 064 ou documentos importantes para elaboração do estudo',
  AssinaturaContrato = '6. Assinatura do Contrato',
  Aviso = '*Atenção: É obrigatório o preenchimento de todos os campos.',
  AvisaDados = 'Atenção! Confira seus dados',
  BeneficioBasico = '3. Benefício Básico',
  DadosAgencia = '2. Dados da Agência',
  DadosRepresentante = 'Dados do Representante',
  DadosResponsavel = '7. Dados do Responsável Pelo Preenchimento',
  Download = 'Faça o download do FOP 064 aqui',
  Empresa = 'Empresa',
  Fop63 = ' FOP 063 - Solicitação de Contrato e Aditivo de Prev Empresarial',
  Funcionario = 'Funcionário',
  InformacoesGerais = '1. Informações Gerais',
  RegrasContratuais = '4. Regras Contratuais',
  Penalidades = 'Penalidades',
  RegraAposentadoria = "Regra de Aposentadoria",
  AplicacaoRecursosInstituidora = "Aplicação dos Recursos da Instituidora",
  InformacoesComplementares = '5. Informações Complementares',
  ValorContribuicao = "Valor da contribuição",
}

export enum Textos {
  CusteioPagamento = 'Descreva neste espaço como será o custeio do pagamento',
  InformativoValorFop = 'Informe o valor do salário para cada participante no FOP 064',
}

export enum InformacoesGerais {
  NomeEmpresa = "Nome da Empresa",
  AtividadePrincipal = "Atividade Principal",
  CNPJ = "CNPJ",
  Faturamento = "Faturamento",
  EmailInstitucional = "E-mail Institucional",
  Logradouro = "Logradouro",
  Bairro = "Bairro",
  Cidade = "Cidade",
  UF = "UF",
  CEP = "CEP",
}

export enum DadosRepresnetantes {
  NomeRepresentante = "Nome Representante",
  EmailRepresentante = "E-mail Representante",
  CargoRepresentante = "Cargo Representante"
}

export enum DadosAgencia {
  NumeroAgencia = "Número da Agência",
  SuperintendênciaRegional = "Superintendência Regional",
  NomeAgência = "Nome da Agência",
  Filial = "Filial",
  MatrículaIndicador = "Matrícula do Indicador",
  NomeIndicador = "Nome do Indicador"
}

export enum BeneficioBasico {
  NumeroParticipantes = "Número de Participantes",
  AporteInicial = "Aporte Inicial",
  ValorAporteInicial = "Valor do Aporte Inicial",
  ValorPortabilidade = "Valor da Portabilidade",
}
export enum RegrasContratuais {
  FormaCusteioModalidadePlano = "Forma de Custeio e Modalidade do Plano",
  PagamentoContribuicao = "Pagamento da Contribuição",
  DiaPagamento = "Dia de Pagamento",
  FormaPagamento = "Forma de Pagamento",
  Banco = "Banco",
  Agencia = "Agência",
  Conta = "Conta",
  Operacao = "Operação",
  Caixa = "Caixa Econômica Federal",
  PerdaVinculo = "Perda do Vínculo - Condições de Vesting",
  JustaCausa = "Em caso de demissão por justa causa, o PARTICIPANTE/SEGURADO fará jus a reserva constituída pela empresa?",
  InformativoPenalidades = "Em caso de resgate das contribuições efetuadas pelo colaborador, as contribuições efetuadas pela empresa sofrerão penalidade?",
  DistribuicaoContaColetiva = "Distribuição de Conta Coletiva",
  informativoRegraAposentadoria = "Idade de aposentadoria e/ou Tempo Mínimo de Plano:",
  IdadeAposentadoria = "Idade de Aposentadoria",
  TempoMinimoPlano = "Tempo Mínimo de Plano",
  RecursosInstituidoras = "As contribuições da empresa serão alocadas",
  DescricaoFundo = "Descreva o Fundo",
  InformcaoesComplementares = "Informações Complementares (Opcional)",
  Aposentadoria = "Aposentadoria",
  CuidadoExtra = "Cuidado Extra",
}

export enum AssinaturaContrato {
  NomeRepresentantelegal = "Nome do Representante Legal",
  CPFRepresentantelegal = "CPF do Representante Legal",
  EmailRepresentanteLegal = "E-mail do Representante Legal",
  NomeTestemunha = "Nome da 1ª Testemunha",
  CPFTestemunha = "CPF da 1ª  Testemunha",
  EmailTestemunha = "E-mail da 1ª Testemunha"
}
export enum DadosResponsavelPreenchimento {
  NomeCompleto = "Nome Completo",
  Telefone = "Telefone",
  EmailInfomativo = "E-mail (Atenção: Este será o e-mail para o recebimento do estudo)",
  Email = "Email"
}
export enum Botoes {
  Ok = "OK",
  Limpar = "Limpar",
  Visualizar = "Visualizar",
  Enviar = "Enviar",
  Adicionar = "Adicionar",
  Remover = "Remover",
}

export const SELECT_OPTIONS_FILIAL: ISelectOption[] = [
  { key: 'centroOeste', value: 'CENTRO OESTE' },
  { key: 'nordeste', value: 'NORDESTE' },
  { key: 'norte', value: 'NORTE' },
  { key: 'saoPaulo', value: 'SÃO PAULO' },
  { key: 'sudeste', value: 'SUDESTE' },
  { key: 'sul', value: 'SUL' },
  { key: 'sured', value: 'SURED' },
];

export const SECT_OPTIONS_LIBERACAO_RESERVA: ISelectOption[] = [
  { key: 'sugestao', value: 'Sugestão da Caixa Seguradora (ver tabela)' },
  {
    key: 'liberacaoTotal',
    value:
      'Liberação total da reserva (100% da reserva constituída pela empresa)',
  },
  { key: 'semLiberacao', value: 'Sem liberação da reserva' },
  { key: 'outraRegra', value: 'Definir outra regra de liberação da reserva' },
];

export const SELECT_OPTIONS_REGRA: ISelectOption[] = [
  { key: 'aposentadoria', value: 'Valor de aposentadoria' },
  {
    key: 'contribuicaoMensal',
    value: 'Valor de contribuição mensal',
  },
  {
    key: 'contribuicaoAnual',
    value: 'Valor de contribuição Anual',
  },
];

export const SELECT_OPTIONS_BENEFICIO: ISelectOption[] = [
  { key: 'rendaTemporaria', value: 'Renda temporária' },
  {
    key: 'prazoMinimoGarantido',
    value: 'Prazo mínimo garantido',
  },
  {
    key: 'vitalicia',
    value: 'Vitalícia',
  },
  {
    key: 'vitaliciareversivel',
    value: 'Vitalícia Reversível',
  },
  {
    key: 'rendaPorPrazoCerto',
    value: 'Renda por prazo certo',
  },
];

export const SELECT_OPTIONS_PRAZO_BENEFICIO: ISelectOption[] = [
  { key: '001', value: '1' },
  { key: '002', value: '2' },
  { key: '003', value: '3' },
  { key: '004', value: '4' },
  { key: '005', value: '5' },
  { key: '006', value: '6' },
  { key: '007', value: '7' },
  { key: '008', value: '8' },
  { key: '009', value: '9' },
  { key: '10', value: '10' },
];

export const SELECT_OPTIONS_REVERSAO: ISelectOption[] = [
  { key: '50', value: '50%' },
  { key: '75', value: '75%' },
  { key: '100', value: '100%' },
];

export const SELECT_OPTIONS_PAGAMENTO: ISelectOption[] = [
  {
    key: 'averbado',
    value: 'Averbado: Pagamento feito apenas pelo participante',
  },
  {
    key: 'instituido',
    value: 'Instituído: Pagamento feito apenas pela empresa',
  },
  {
    key: 'planoInstituido',
    value:
      'Plano Instituído com Divisão de Pagamento: O Pagamento é feito pela empresa e também pelo participante',
  },
];

export const SELECT_OPTIONS_VALORES_PARTICIPANTES: ISelectOption[] = [
  {
    key: 'sim',
    value: 'SIM',
  },
  {
    key: 'nao',
    value: 'NÃO',
  },
];

export const SELECT_OPTIONS_VALORES_CONTRIBUICAO: ISelectOption[] = [
  {
    key: 'valorFixoContribuicao',
    value: 'Valor fixo',
  },
  {
    key: 'valorBaseContribuicao',
    value: 'Valor com base no percentual do salário',
  },
  {
    key: 'outraFormaContribuicao',
    value: 'Outra Forma de Pagamento',
  },
];

export const SELECT_OPTIONS_CUIDADO_EXTRA: ISelectOption[] = [
  {
    key: 'peculio',
    value: 'Pecúlio',
  },
  {
    key: 'pensao',
    value: 'Pensão por prazo certo',
  },
  {
    key: 'semCuidadoExtra',
    value: 'Sem cuidado extra',
  },
];

export const SELECT_OPTIONS_REGRA_CUIDADO: ISelectOption[] = [
  {
    key: 'valorIndenizacao',
    value: 'Valor da Indenização',
  },
  {
    key: 'pagamentoMensal',
    value: 'Valor de pagamento mensal',
  },
  {
    key: 'pagamentoAnual',
    value: 'Valor de pagamento anual',
  },
];

export const SELECT_OPTIONS_REGRA_CUIDADO_PENSAO: ISelectOption[] = [
  {
    key: 'valorIndenizacaoRecebido',
    value: 'Valor de Indenização: estipular o valor a ser recebido',
  },
  {
    key: 'valorContribuicaoMensal',
    value:
      'Valor de contribuição mensal: quanto o participante irá contribuir mensalmente',
  },
];

export const SELECT_OPTIONS_ANOS_PENSAO: ISelectOption[] = [
  {
    key: '1',
    value: '1',
  },
  {
    key: '2',
    value: '2',
  },
  {
    key: '3',
    value: '3',
  },
  {
    key: '4',
    value: '4',
  },
  {
    key: '5',
    value: '5',
  },
  {
    key: '6',
    value: '6',
  },
  {
    key: '7',
    value: '7',
  },
  {
    key: '8',
    value: '8',
  },
  {
    key: '9',
    value: '9',
  },
  {
    key: '10',
    value: '10',
  },
];

// FOP 063

export const SELECT_OPTIONS_FATURAMENTO: ISelectOption[] = [
  {
    key: 'microempresa',
    value: 'Microempresa -  Menor ou igual a R$ 360 mil',
  },
  {
    key: 'pequenaEmpresa',
    value:
      'Pequena empresa - Maior que R$ 360 mil e menor ou igual a R$ 4,8 milhões',
  },
  {
    key: 'mediaEmpresa',
    value:
      'Média empresa - Maior que R$ 4,8 milhões e menor ou igual a R$ 300 milhões',
  },
  {
    key: 'grandeEmpresa',
    value: 'Grande empresa - Maior que R$ 300 milhões',
  },
];

export const SELECT_OPTIONS_REGRA_CUIDADO_63: ISelectOption[] = [
  {
    key: 'valorIndenizacao',
    value: 'Valor da Indenização',
  },
  {
    key: 'pagamentoMensal',
    value: 'Valor de pagamento mensal',
  },
];

export const SELECT_OPTIONS_TIPO_FUNDO: ISelectOption[] = [
  { key: 'rendaFixaPosFixado', value: 'Renda Fixa Pós Fixado' },
  { key: 'rendaFixa', value: 'Renda Fixa' },
  { key: 'creditoPrivado', value: 'Crédito Privado' },
  {
    key: 'multiEstrategiaLivreConservador',
    value: 'Multi Estratégia Livre Conservador',
  },
  { key: 'rendaFixaModerado', value: 'Renda Fixa Moderado' },
  { key: 'indicePrecos', value: 'Índice de preços' },
  {
    key: 'MultiRendaVariavel15',
    value: 'Multi Renda Variável 15',
  },
  {
    key: 'multiEstrategiaLivreModerado',
    value: 'Multi Estratégia Livre Moderado',
  },
  {
    key: 'Inflacao Ativa',
    value: 'Inflação Ativa',
  },
  {
    key: 'multiRendaVariavel30',
    value: 'Multi Renda Variável 30',
  },
  {
    key: 'multiRendaVariavel49',
    value: 'Multi Renda Variável 49',
  },
  {
    key: 'multiRendaVariavel70',
    value: 'Multi Renda Variável 70',
  },
  {
    key: 'multiRendaVariavel70Livre',
    value: 'Multi Renda Variável 70 Livre',
  },
];

export const SELECT_OPTION_CUSTEIO_MODALIDADE: ISelectOption[] = [
  {
    key: 'instituído',
    value: 'Instituído – Somente contribuição da INSTITUIDORA',
  },
  { key: 'instituídoDivisao', value: 'Instituído – Com divisão de pagamento' },
  {
    key: 'averbado',
    value: 'Averbado – Somente contribuição do PARTICIPANTE/SEGURADO',
  },
];

export const SELECT_OPTIONS_VALORES_CONTRIBUICAO_PARTICIPANTE: ISelectOption[] = [
  {
    key: 'porcentagemSalario',
    value: 'Porcentagem do Salário',
  },
  {
    key: 'valorFixo',
    value: 'Valor Fixo',
  }
]
export const SELECT_OPTIONS_VALORES_TABELA_CUIDADO_EXTRA: ISelectOption[] = [
  {
    key: 'Valor Contribuição',
    value: 'Valor Contribuição',
  },
  {
    key: 'Valor Cobertura',
    value: 'Valor Cobertura',
  },
];



export const SELECT_OPTIONS_VALORES_CONTRIBUICAO_EMPRESA: ISelectOption[] = [
  {
    key: 'porcentagemContribuicaoParticipante',
    value: 'Porcentagem da Contribuição Participante',
  },
  {
    key: 'porcentagemSalario',
    value: 'Porcentagem do Salário',
  },
  {
    key: 'valorFixo',
    value: 'Valor Fixo',
  }
]

export const SELECT_OPTIONS_PAGAMENTO_cONTRIBUICAO: ISelectOption[] = [
  {
    key: 'pagamentoUnico',
    value: 'Pagamento Único'
  },
  {
    key: 'pagamentoMensal',
    value: 'Pagamento Mensal'
  },

]

export const SELECT_OPTION_DIA_PAGAMENTO: ISelectOption[] = [
  {
    key: '5',
    value: '5'
  },
  {
    key: '6',
    value: '6'
  },
  {
    key: '7',
    value: '7'
  },
  {
    key: '8',
    value: '8'
  },
  {
    key: '9',
    value: '9'
  },
  {
    key: '11',
    value: '11'
  },
  {
    key: '12',
    value: '12'
  },
  {
    key: '13',
    value: '13'
  },
  {
    key: '14',
    value: '14'
  },
  {
    key: '16',
    value: '16'
  },
  {
    key: '17',
    value: '17'
  },
  {
    key: '18',
    value: '18'
  },
  {
    key: '19',
    value: '19'
  }, {
    key: '20',
    value: '20'
  },
  {
    key: '21',
    value: '21'
  },
  {
    key: '22',
    value: '22'
  },
  {
    key: '23',
    value: '23'
  },
  {
    key: '24',
    value: '24'
  },
  {
    key: '25',
    value: '25'
  },
]

export const SELECT_OPTIONS_TIPO_PLANO: ISelectOption[] = [
  {
    key: 'plano',
    value: 'Plano'
  },
  {
    key: 'Empresa',
    value: 'Empresa'
  }
]

export const SELECT_OPTION_PERDA_VINCULO: ISelectOption[] = [
  {
    key: 'naoAplicavel',
    value: 'Não aplicável'
  },
  {
    key: 'contribuiçõesinstituidoraParticipante',
    value: 'Contribuições da(s) INSTITUIDORA(S) 100% disponibilizadas ao PARTICIPANTE'
  },
  {
    key: 'contribuiçõesinstituidoraParticipanteVesting',
    value: 'Contribuições da(s) INSTITUIDORA(S) disponibilizadas ao PARTICIPANTE conforme tabela de vesting.'
  }
];

export const SELET_OPTIONS_PENALIDADE: ISelectOption[] = [
  {
    key: 'naoAplicavel',
    value: 'Não aplicável'
  },
  {
    key: 'semPenalidade',
    value: 'Sem penalidade'
  },
  {
    key: 'comPenalidade',
    value: 'Com penalidade na mesma proporção de contrapartida da INSTITUIDORA do resgate/portabilidade (% equivalente da retirada em relação ao saldo da reserva da conta)'
  }
];

export const SELECT_OPTIONS_DISTRIBUICAO_CONTA_COLETIVA: ISelectOption[] = [
  {
    key: 'naoAplicavel',
    value: 'Não Aplicável'
  },
  {
    key: 'fracoesIguais',
    value: 'Em Frações Iguais'
  },
  {
    key: 'proporcaoIndividual',
    value: 'Na proporção individual do saldo total da reserva constituída pela INSTITUIDORA no plano'
  }
];

export const SELECT_OPTION_RECURSOS_INSTITUIDORA: ISelectOption[] = [
  {
    key: 'naoAplicavel',
    value: 'Não aplicável'
  },
  {
    key: 'fundo',
    value: 'No fundo'
  },
  {
    key: 'fundoEscolhido',
    value: 'No fundo escolhido pelo colaborador na proposta de inscrição/adesão'
  }
];

export const SELECT_FORMA_PAGAMENTO_REGRA_CONTRATUAL: ISelectOption[] = [
  {
    key: 'boleto',
    value: 'Boleto'
  },
  {
    key: 'debitoConta',
    value: 'Débito em conta'
  }
];

export const SELECT_DEMISSAO_JUSTA_CAUSA: ISelectOption[] = [
  {
    key: 'nao',
    value: 'Não'
  },
  {
    key: 'sim',
    value: ' Sim, conforme tabela de vesting'
  }
]

export const SELECT_OPTIONS_CONCESSAO_BENEFICIO: ISelectOption[] = [
  { key: 'idadeAposentadoria', value: 'Idade de aposentadoria' },
  {
    key: 'prazoContribuicao',
    value: 'Prazo da contribuição (diferimento)',
  },
];

export const SELECT_OPTIONS_PAGAMENTO_FATURA: ISelectOption[] = [
  { key: 'aporteUnico', value: 'Aporte único' },
  {
    key: 'mensal',
    value: 'Mensal',
  },
];
export const SELECT_OPTIONS_OPERACAO: ISelectOption[] = [
  { key: '022', value: '022' },
  {
    key: '003',
    value: '003',
  },
];
export const SELECT_OPTIONS_DADOS_COBRANCA: ISelectOption[] = [
  { key: 'boleto', value: 'Boleto' },
  {
    key: 'debito',
    value: 'Débito em conta',
  },
];

export const SELECT_OPTIONS_VENCIMENTO_FATURA: ISelectOption[] = [
  { key: '5', value: '5' },
  { key: '6', value: '6' },
  { key: '7', value: '7' },
  { key: '8', value: '8' },
  { key: '9', value: '9' },
  { key: '10', value: '10' },
  { key: '11', value: '11' },
  { key: '12', value: '12' },
  { key: '13', value: '13' },
  { key: '14', value: '14' },
  { key: '15', value: '15' },
  { key: '16', value: '16' },
  { key: '17', value: '17' },
  { key: '18', value: '18' },
  { key: '19', value: '19' },
  { key: '20', value: '20' },
  { key: '21', value: '21' },
  { key: '22', value: '22' },
  { key: '23', value: '23' },
  { key: '24', value: '24' },
  { key: '25', value: '25' },
];

export const SELECT_OPTIONS_MODALIDADE: ISelectOption[] = [
  { key: 'PGBL', value: 'PGBL' },
  { key: 'VGBL', value: 'VGBL' },
  { key: 'PGBLVGBL', value: 'PGBL e VGBL' },
];

export const SELECT_OPTIONS_MODALIDADE_PGBL: ISelectOption[] = [
  { key: 'PGBL', value: 'PGBL' },
];
export const SELECT_OPTIONS_MODALIDADE_VGBL: ISelectOption[] = [
  { key: 'PGBL', value: 'PGBL' },
  { key: 'PGBLVGBL', value: 'PGBL e VGBL' },
];

export const SELECT_OPTION_UF: ISelectOption[] = [
  { key: 'AC', value: 'AC' },
  { key: 'AL', value: 'AL' },
  { key: 'AM', value: 'AM' },
  { key: 'AP', value: 'AP' },
  { key: 'BA', value: 'BA' },
  { key: 'CE', value: 'CE' },
  { key: 'DF', value: 'DF' },
  { key: 'ES', value: 'ES' },
  { key: 'GO', value: 'GO' },
  { key: 'MA', value: 'MA' },
  { key: 'MG', value: 'MG' },
  { key: 'MS', value: 'MS' },
  { key: 'MT', value: 'MT' },
  { key: 'PA', value: 'PA' },
  { key: 'PB', value: 'PB' },
  { key: 'PE', value: 'PE' },
  { key: 'PI', value: 'PI' },
  { key: 'PR', value: 'PR' },
  { key: 'RJ', value: 'RJ' },
  { key: 'RN', value: 'RN' },
  { key: 'RO', value: 'RO' },
  { key: 'RR', value: 'RR' },
  { key: 'RS', value: 'RS' },
  { key: 'SC', value: 'SC' },
  { key: 'SE', value: 'SE' },
  { key: 'SP', value: 'SP' },
  { key: 'TO', value: 'TO' },
];

export const SELECT_OPTIONS_REGRA_APORTE: ISelectOption[] = [
  { key: 'semAporte', value: 'Sem aporte inicial' },
  {
    key: 'comAporte',
    value: 'Com aporte inicial',
  },
  {
    key: 'portabilidade',
    value: 'Portabilidade',
  },
];

