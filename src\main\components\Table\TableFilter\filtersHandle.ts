import { IFilterableEntity } from 'main/types/IFilterableEntity';
import {
  ISearchTerms,
  ITagFilterOption,
} from 'main/types/TableFilters/IFilterOption';
import { dateWithoutTime } from 'main/utils';

const filterByTextPartial = <T extends IFilterableEntity>(
  text: string,
  filterColumn: string,
  dataToFilter: T[],
  hasUnmask?: (value: string) => string,
): T[] => {
  return dataToFilter?.filter(item => {
    const itemFilterColumn = item[filterColumn]
      ?.toString()
      .trim()
      .toLowerCase();
    const textToFilter = text.trim().toLowerCase();

    if (item[filterColumn]) {
      if (hasUnmask) {
        const unmaskedData = hasUnmask(itemFilterColumn).toLowerCase();
        const unmaskedSearchText = hasUnmask(textToFilter).toLowerCase();
        return unmaskedData.includes(unmaskedSearchText);
      }

      return itemFilterColumn.includes(textToFilter);
    }
    return null;
  });
};

export const filterByDate = <T extends IFilterableEntity>(
  search: ISearchTerms,
  dataToFilter: T[],
): T[] => {
  const filterColumn = search.filter;
  return dataToFilter?.filter(item => {
    if (item[filterColumn] && search.initialDate && search.endDate) {
      return (
        dateWithoutTime(new Date(item[filterColumn]).toISOString()) >=
          dateWithoutTime(search.initialDate.toISOString()) &&
        dateWithoutTime(new Date(item[filterColumn]).toISOString()) <=
          dateWithoutTime(search.endDate.toISOString())
      );
    }
    return null;
  });
};

export const filterByText = <T extends IFilterableEntity>(
  text: string,
  filterColumn: string,
  dataToFilter: T[],
  hasUnmask?: (value: string) => string,
  partial = false,
): T[] => {
  if (partial)
    return filterByTextPartial(text, filterColumn, dataToFilter, hasUnmask);

  return dataToFilter?.filter(item => {
    const itemFilterColumn = item[filterColumn]?.toString().trim();
    const textToFilter = text.trim();
    if (itemFilterColumn) {
      if (
        hasUnmask &&
        hasUnmask(itemFilterColumn) === hasUnmask(textToFilter)
      ) {
        return item;
      }
      return itemFilterColumn === textToFilter;
    }
    return null;
  });
};

const filterByIdOrDescription = <T extends IFilterableEntity>(
  dataToFilter: T[],
  item: ITagFilterOption,
) => {
  const filter = dataToFilter.filter(x => x[item.column ?? ''] === item.id);
  const filterByDescription = dataToFilter.filter(
    x =>
      x[item.column ?? '']?.toString().trim().toLowerCase() ===
      item.description.trim().toLowerCase(),
  );

  return filter.length > 0 ? filter : filterByDescription;
};

export const filterByTags = <T extends IFilterableEntity>(
  dataToFilter: T[],
  filterColumn?: string,
  options?: ITagFilterOption[],
): T[] => {
  if (filterColumn && options && options?.length > 0) {
    const filteredItemsBySingleColumn: T[] = [];
    // items filtrados com mais de uma coluna
    const filteredItemsByMultipleColumns: T[] = [];

    const [firstOptionToFilter] = options;

    let lastColumnFiltered = firstOptionToFilter.column;

    options.forEach(item => {
      if (lastColumnFiltered !== item.column) {
        const filterResult = filterByIdOrDescription(
          filteredItemsBySingleColumn,
          item,
        );
        filteredItemsByMultipleColumns.push(...filterResult);
      } else {
        const filterResult = filterByIdOrDescription(dataToFilter, item);
        filteredItemsBySingleColumn.push(...filterResult);
      }
      lastColumnFiltered = item.column ?? '';
    });

    return filteredItemsByMultipleColumns?.length > 0
      ? filteredItemsByMultipleColumns
      : filteredItemsBySingleColumn;
  }
  return dataToFilter;
};
