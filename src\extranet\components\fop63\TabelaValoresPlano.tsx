import * as DS from '@cvp/design-system/react';
import { ITempoContribuicao } from 'extranet/types/ITableValorContribuicao';
import { ITableaValoresPlano } from 'extranet/types/InterfacesFop63/ITabelaValoresPlano';
import Icon from 'main/components/Icon';
import RenderConditional from 'main/components/RenderConditional';
import Table from 'main/components/Table';
import { TextField } from 'main/features/Auth/components';
import { getTernaryResult } from 'main/utils/conditional';
import masks from 'main/utils/masks';
import { ChangeEvent, KeyboardEvent } from 'react';
import * as Enum from '../../types/enum';

export const TabelaValoresPlano: React.FC<ITableaValoresPlano> = ({
  setFieldValue,
  values,
  handleBlur,
  remove
}) => {
  return (
    <Table
      columns={[
        {
          name: (
            <div>
              <DS.Select
                label="Tempo de"
                name="tempoMesesReversao"
                placeholder="Selecione"
                value={values.pagamentoContribuicaoParticipante}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('tempoMesesReversao', `tempo de ${value}`);
                }}
                onBlur={handleBlur}
              >
                {Enum.SELECT_OPTIONS_TIPO_PLANO.map(optTipoPlano => (
                  <DS.Select.Item
                    key={optTipoPlano.key}
                    value={optTipoPlano.key}
                    text={optTipoPlano.value}
                    selected={optTipoPlano.key === values.tempoMesesReversao}
                  />
                ))}
              </DS.Select>
            </div>
          ),
          selector: (row: ITempoContribuicao) => row.tempoMesesContribuicao,
          wrap: true,
          cell: (_row, index) => {
            const total = values.valoresPlano.length;
            const prevRaw = values.valoresPlano[index - 1]?.tempoMeses ?? '';
            const currRaw = values.valoresPlano[index]?.tempoMeses ?? '';

            const prevMatch = prevRaw.match(/\d+/g);
            const prevNum = getTernaryResult(!!prevMatch, Number(prevMatch?.pop()), null)
            const nextNum = prevNum !== null ? prevNum + 1 : 0;

            const isFirst = index === 0;
            const isLast = index === total - 1;

            const placeholder = getTernaryResult(
              isFirst,
              'Até XX meses',
              getTernaryResult(
                isLast && prevNum !== null,
                `A partir de ${nextNum} meses`,
                getTernaryResult(prevNum !== null, `De ${nextNum} a XX meses`, 'De YYY a XX meses')
              )
            );

            return (
              <TextField
                type="text"
                value={getTernaryResult(isLast, placeholder, currRaw)}
                placeholder={placeholder}
                onChange={(e: ChangeEvent<HTMLInputElement>) => {
                  setFieldValue(`valoresPlano.${index}.tempoMeses`, e.target.value);
                }}
                onBlur={() => {
                  const raw = values.valoresPlano[index].tempoMeses;
                  const matchResult = raw.match(/\d+(?:,\d+)?/);

                  if (!matchResult) {
                    setFieldValue(`valoresPlano.${index}.tempoMeses`, '');
                    return;
                  }

                  const numStr = matchResult[0];
                  const formatted = getTernaryResult(
                    isFirst,
                    `Até ${numStr} meses`,
                    getTernaryResult(
                      isLast,
                      `A partir de ${numStr} meses`,
                      `De ${nextNum} a ${numStr} meses`
                    )
                  );
                  setFieldValue(`valoresPlano.${index}.tempoMeses`, formatted);
                }}
                onKeyDown={(e: KeyboardEvent<HTMLInputElement>) => {
                  if (e.key === 'Backspace' && currRaw) {
                    setFieldValue(`valoresPlano.${index}.tempoMeses`, '');
                    e.preventDefault();
                  }
                }}
              />
            );
          },
          grow: 1,
          center: true,
        },
        {
          name: '(%) de reversão ao participante',
          selector: (row: ITempoContribuicao) => row.porcentagemReversao,
          cell: (_row, index) => (
            <>
              <TextField
                value={masks.percentage.mask(values.valoresPlano[index]?.porcentagemReversao)}
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setFieldValue(
                    `valoresPlano.${index}.porcentagemReversao`,
                    masks.percentage.mask(e.target.value),
                  )
                }
              />
              <RenderConditional condition={index !== 0 && index !== values.valoresPlano.length - 1}>

                <DS.IconButton
                  type="button"
                  variant="outlined"
                  onClick={() => {
                    if (values.valoresPlano.length <= 1) return
                    if (remove) {
                      remove(index)
                    }
                  }}
                >
                  <Icon name="trash" />
                </DS.IconButton>
              </RenderConditional>
            </>
          ),
          wrap: true,
          grow: 1,
        },
      ]}
      data={values.valoresPlano as unknown as ITempoContribuicao[]}
    />
  );
};
