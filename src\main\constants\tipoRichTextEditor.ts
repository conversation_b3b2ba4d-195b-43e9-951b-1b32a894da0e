import { $generateNodesFromDOM } from '@lexical/html';
import { InitialConfigType } from '@lexical/react/LexicalComposer';
import { $getRoot } from 'lexical';
import { toastError } from 'main/hooks/useToast';

export const editorConfig = (initialText: string): InitialConfigType => ({
  namespace: 'MeuEditor',
  theme: {
    text: {
      bold: 'font-bold',
      italic: 'italic',
      underline: 'underline',
      strikethrough: 'strike',
    },
  },
  onError: error => {
    toastError(`${error}`);
  },
  editorState: editor => {
    const parser = new DOMParser();
    const transformDecodeText = decodeURIComponent(initialText);
    const dom = parser.parseFromString(transformDecodeText, 'text/html');
    const nodes = $generateNodesFromDOM(editor, dom);

    const root = $getRoot();
    root.clear();
    root.append(...nodes);
  },
});
