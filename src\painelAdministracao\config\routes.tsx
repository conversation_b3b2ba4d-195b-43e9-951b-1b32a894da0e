import React from 'react';
import { Route, Routes } from 'react-router-dom';
import PrivateRoute from 'main/components/Route';
import { TPrivateRoute } from 'main/components/Route/PrivateRoute';
import { USER_PROFILES } from 'main/features/Auth/config/userProfiles';
import { mapBreadCrumbsFromRoutes } from 'main/components/Layout/AppBreadcrumb/mapBreadcrumb';
import ListagemFop from 'painelAdministracao/pages/ListagemFop';
import AdmContextProvider from 'painelAdministracao/context/AdministracaoContext';
import RelatorioSolicitacaoFop from 'painelAdministracao/pages/RelatorioSolicitacaoFop';

const routes: TPrivateRoute[] = [
  {
    path: '/administracao/gestao-fop',
    component: ListagemFop,
    key: 'fop-prestamista',
    breadcrumb: 'Gestão de FOPs',
    authenticated: true,
    withSidebar: true,
    exact: true,
    requiredRoles: [
      USER_PROFILES.ANALISTA_TI,
      USER_PROFILES.ANALISTA_POS_VENDA,
    ],
  },
  {
    path: '/administracao/gestao-fop/relatorio',
    component: RelatorioSolicitacaoFop,
    key: 'fop-prestamista-relatorio',
    breadcrumb: 'Relatório de solicitações',
    authenticated: true,
    withSidebar: true,
    exact: true,
    requiredRoles: [
      USER_PROFILES.ANALISTA_TI,
      USER_PROFILES.ANALISTA_POS_VENDA,
    ],
  },
];

export const administracaoBreadcrumbs = {
  ...mapBreadCrumbsFromRoutes(routes),
  '/administracao': null,
};

const RotasAdministracao = (): React.ReactElement => (
  <AdmContextProvider>
    <Routes>
      {routes.map(route => (
        <Route
          key={route.key}
          path={route.path}
          element={
            <PrivateRoute
              key={route.key}
              exact={route.exact}
              component={route.component}
              authenticated={route.authenticated}
              requiredRoles={route.requiredRoles}
            />
          }
        />
      ))}
    </Routes>
  </AdmContextProvider>
);

export default RotasAdministracao;
