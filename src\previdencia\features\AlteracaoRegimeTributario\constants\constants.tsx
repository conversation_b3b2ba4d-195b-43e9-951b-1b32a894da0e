import { Text } from '@cvp/design-system/react';

import { getTernaryResult, tryGetValueOrDefault } from 'main/utils/conditional';
import { tryGetMonetaryValueOrDefault } from 'main/utils/money';
import { TAlicotasAgrupadasFundosAliquotaFactory } from 'previdencia/types/AlicotasAgrupadas';
import * as REGIME_TRIBUTARIO_TYPES from 'previdencia/features/AlteracaoRegimeTributario/types/AlteracaoRegimeTributario';
import { ConditionalStyles, TableColumn } from 'react-data-table-component';

export const TIPOS_RESGATE = {
  PARCIAL: 'Parcial',
  TOTAL: 'Total',
};

export const TEXTOS_REGIME_TRIBUTARIO = {
  TITULO: 'Alteração de Regime Tributário',
  DESCRICAO:
    'Confira as deduções aplicáveis para cada regime tributário e selecione uma opção para prosseguir.',
  TITULO_DECLARACAO: 'Declaração',
  INFO_CHECKBOX: `O cliente confirma a opção pelo regime de tributação de alíquotas selecionada, previsto na IN RFB Nº 2.209 de 06 de agosto de 2024, para o certificado indicado, ciente de que esta opção é irretratável.`,
  TITULO_MODAL:
    'A alíquota escolhida não poderá mais ser alterada, sendo assim, definitiva. Tem certeza da escolha do modelo tributário?',
  BUTTON_CANCELAR: 'Cancelar',
  BUTTON_CONFIRMAR: 'Confirmar',
};

export const MODAIS_REGIME_TRIBUTARIO: REGIME_TRIBUTARIO_TYPES.IModaisRegimeTributario =
  {
    modalTipo: false,
    modalDocusign: false,
    modalAlertaContatos: false,
    modalAlertaAlteracaoExpirada: false,
    modalAlertaAlteracaoTributaria: false,
  };

export const TEXTOS_REGIME_TRIBUTARIO_MSG = {
  SUCESSO: 'Solicitação recebida com sucesso!',
};

export const ALTERACAO_REGIME_TRIBUTARIO = {
  PODE_ALTERAR: 'S',
  NAO_PODE_ALTERAR: 'N',
};

export const COLUNAS_ALIQUOTA: TableColumn<
  Partial<TAlicotasAgrupadasFundosAliquotaFactory>
>[] = [
  {
    name: 'Fundos',
    selector: row => tryGetValueOrDefault([row.descricaoFundo], ''),
    center: true,
    minWidth: '200px',
    cell: row =>
      getTernaryResult<React.JSX.Element | string>(
        row.descricaoFundo !== TIPOS_RESGATE.TOTAL,
        <Text variant="body02-md" align="center">
          {tryGetValueOrDefault([row.descricaoFundo], '-')}
        </Text>,
        tryGetValueOrDefault([row.descricaoFundo], '-'),
      ),
    wrap: true,
  },
  {
    name: 'Saldo Total',
    selector: row => tryGetValueOrDefault([row.saldoTotal], ''),
    cell: row => tryGetMonetaryValueOrDefault(row.saldoTotal),
    center: true,
    minWidth: '180px',
  },
  {
    name: 'Alíquota IRPF (%)',
    selector: row => tryGetValueOrDefault([row.aliquota], ''),
    cell: row =>
      getTernaryResult(
        row.descricaoFundo !== TIPOS_RESGATE.TOTAL,
        tryGetValueOrDefault([row.aliquota], '-'),
        '',
      ),
    center: true,
  },
  {
    name: 'Saldo Disponível',
    selector: row => tryGetValueOrDefault([row.valorDisponivel], ''),
    center: true,
    minWidth: '180px',
    cell: row => {
      return getTernaryResult(
        row.descricaoFundo !== TIPOS_RESGATE.TOTAL,
        tryGetMonetaryValueOrDefault(row.valorDisponivel),
        '',
      );
    },
  },
  {
    name: 'Saldo Indisponível',
    selector: row => tryGetValueOrDefault([row.valorIndisponivel], ''),
    center: true,
    minWidth: '180px',
    cell: row => {
      return getTernaryResult(
        row.descricaoFundo !== TIPOS_RESGATE.TOTAL,
        tryGetMonetaryValueOrDefault(row.valorIndisponivel),
        '',
      );
    },
  },
];

export const NO_DATA_ALIQUOTA =
  'Não há opção de alíquota para o atual certificado.';

export const conditionalRowStylesAliquota = (
  disabled: boolean,
): ConditionalStyles<Partial<TAlicotasAgrupadasFundosAliquotaFactory>>[] => {
  return [
    {
      when: row =>
        tryGetValueOrDefault([row.descricaoFundo], '') === TIPOS_RESGATE.TOTAL,
      style: {
        backgroundColor: '#005FC8',
        opacity: getTernaryResult(disabled, '50%', '100%'),
        color: 'white',
      },
    },
  ];
};
