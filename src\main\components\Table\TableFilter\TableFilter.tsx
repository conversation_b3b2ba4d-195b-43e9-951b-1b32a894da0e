import React from 'react';
import { Select, Grid, But<PERSON>, Display, Text } from '@cvp/design-system/react';
import Calendar from 'main/components/Calendar/Calendar';
import Input from 'main/components/form/Input';
import RenderConditional from 'main/components/RenderConditional';
import InputSelect from 'main/components/form/InputSelect';
import { required } from 'main/features/Validation/validations';
import {
  FilterTypes,
  TableFilterProps,
} from 'main/types/TableFilters/IFilterOption';
import {
  Container,
  GridItem,
  Label,
  GridFlex,
} from 'main/components/Table/TableFilter/styles';
import { useTableFilters } from 'main/hooks/useTableFilters';
import { TagsFilter } from 'main/components/Table/TableFilter/TagsFilter';
import { IFilterableEntity } from 'main/types/IFilterableEntity';
import { SearchKey } from 'main/types/TableFilters/TableFilters';
import { tryGetValueOrDefault } from 'main/utils/conditional';

const TableFilter = <T extends IFilterableEntity>({
  children,
  filterOptions = [],
  dataToFilter = [],
  filterTextPartial = false,
  customButton,
  defaultFilter,
  tagsFilterDescription,
  dataProps,
  onSubmitCallback,
}: TableFilterProps<T>): React.ReactElement => {
  const {
    selectFilter,
    searchText,
    searchFilterTypeSelected,
    initialDate,
    endDate,
    showClearFilters,
    dataFiltered,
    tagsSelecteds,
    searchLabel,
    inputsAreValids,
    setInitialDate,
    setEndDate,
    onSubmitSearch,
    clearFilters,
    handleTagsFilter,
  } = useTableFilters({
    initialData: dataToFilter ?? [],
    filterOptions,
    filterTextPartial,
    defaultFilter,
    onSubmitCallback,
  });

  return (
    <>
      <Container>
        <GridFlex>
          <RenderConditional
            condition={filterOptions?.length > 0}
            component={
              <InputSelect
                label="Selecione o filtro"
                placeholder="Escolha a opção"
                link={selectFilter}
                validationRules={[required()]}
                data-testid="select-options"
              >
                {filterOptions
                  .filter(x => x.type !== FilterTypes.TAG)
                  .map(item => {
                    return (
                      <Select.Item
                        selected={selectFilter.get().value === item.key}
                        key={item.key}
                        value={item.key}
                        text={item.value}
                        data-testid={`option-${item.key}`}
                      />
                    );
                  })}
              </InputSelect>
            }
          />
          <RenderConditional
            condition={searchFilterTypeSelected === 'text'}
            component={
              <div>
                <Label>
                  {tryGetValueOrDefault(
                    [searchLabel[selectFilter.get().value as SearchKey]],
                    'Digite o termo da pesquisa',
                  )}
                </Label>
                <Input
                  type="text"
                  link={searchText}
                  validationRules={[required()]}
                  data-testid="input-pesquisa"
                />
              </div>
            }
          />
          <RenderConditional
            condition={searchFilterTypeSelected === 'date'}
            component={
              <div>
                <Calendar
                  id="periodo"
                  placeholder=""
                  range
                  value={initialDate}
                  endDate={endDate}
                  onChange={(dataInicial, dataFinal) => {
                    setInitialDate(dataInicial);
                    setEndDate(dataFinal);
                  }}
                  data-testid="calendario-pesquisa"
                />
              </div>
            }
          />
        </GridFlex>
        <div>
          <RenderConditional
            condition={
              filterOptions.filter(x => x.type === FilterTypes.TAG)?.length > 0
            }
          >
            {filterOptions
              .filter(x => x.type === FilterTypes.TAG)
              .map(item => (
                <TagsFilter
                  key={item.key}
                  filterOption={item}
                  tagsSelecteds={tagsSelecteds}
                  handleTagsFilter={handleTagsFilter}
                  tagsFilterDescription={tagsFilterDescription}
                />
              ))}
          </RenderConditional>
        </div>
        <Grid>
          <GridItem
            xs={1}
            md={1 / 2}
            style={{ alignSelf: inputsAreValids() ? 'end' : 'center' }}
          >
            <Display>
              <Button
                data-testid="botao-filtrar"
                variant="secondary"
                onClick={onSubmitSearch}
              >
                Filtrar
              </Button>
              <RenderConditional
                condition={showClearFilters}
                component={
                  <Button
                    data-testid="limpar-filtros"
                    variant="outlined"
                    onClick={clearFilters}
                  >
                    Limpar filtros
                  </Button>
                }
              />
              <RenderConditional condition={!!customButton}>
                {customButton}
              </RenderConditional>
            </Display>
          </GridItem>
        </Grid>
        <RenderConditional condition={dataProps?.exibir ?? false}>
          <Text variant="caption-02">
            Período de busca:{' '}
            <strong>
              {dataProps?.dataInicio} - {dataProps?.dataFim}
            </strong>
          </Text>
        </RenderConditional>
      </Container>
      {typeof children === 'function' ? children(dataFiltered) : children}
    </>
  );
};

export default TableFilter;
