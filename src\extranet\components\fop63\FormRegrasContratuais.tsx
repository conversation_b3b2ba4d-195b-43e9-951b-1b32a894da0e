import * as DS from '@cvp/design-system/react';
import { FormatarNumerico } from 'extranet/hooks/useFormFops';
import { IFormFopContext } from 'extranet/types/InterfacesFop63/IFormFopContext';
import { Field, FieldArray } from 'formik';
import RenderConditional from 'main/components/RenderConditional';
import { TextLabel, TitleSection } from 'main/styles/GlobalStyle';
import * as S from '../../../extranet/features/fops/pages/styles';
import * as Enum from '../../types/enum';
import { TabelaCuidadoExtra } from './TabelaCuidadoExtra';
import { TabelaValoresPlano } from './TabelaValoresPlano';
import { TabelaAposentadoria } from './TableAposentadora';

export const RegrasContratuais: React.FC<IFormFopContext> = ({
  setFieldValue,
  values,
  handleBlur,
  errors,
  formFop,
}) => {
  return (
    <div>
      <DS.Accordion open>
        <S.AccordionItem
          title={
            <TitleSection>
              {Enum.Titulos.RegrasContratuais}
            </TitleSection>}
        >
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="formaCusteioModalidadePlano"
              label={Enum.RegrasContratuais.FormaCusteioModalidadePlano}
              component={DS.Select}
              placeholder="Selecione"
              value={values.formaCusteioModalidadePlano}
              error={errors.formaCusteioModalidadePlano}
              errorMessage={errors.formaCusteioModalidadePlano}
              onChange={({
                target: { value },
              }: React.ChangeEvent<{
                text: string;
                value: string;
              }>) => {
                setFieldValue('formaCusteioModalidadePlano', value);
                formFop.setselectFormaPagamentoCusteio(value);
              }}
              onBlur={handleBlur}
            >
              {Enum.SELECT_OPTION_CUSTEIO_MODALIDADE.map(
                optCusteioModalidade => (
                  <DS.Select.Item
                    key={optCusteioModalidade.key}
                    value={optCusteioModalidade.key}
                    text={optCusteioModalidade.value}
                    selected={
                      optCusteioModalidade.key ===
                      values.formaCusteioModalidadePlano
                    }
                  />
                ),
              )}
            </Field>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1}>
            <TitleSection>
              {Enum.Titulos.ValorContribuicao}
            </TitleSection>
            <DS.Grid.Item xs={1} lg={1 / 2}>
              <TextLabel variant="body02-sm">
                {Enum.RegrasContratuais.Aposentadoria}
              </TextLabel>
            </DS.Grid.Item>
            <DS.Grid.Item xs={1} lg={1}>
              <FieldArray name="valoresContribuicao">
                {({ push, remove }) => (
                  <>
                    <TabelaAposentadoria
                      setFieldValue={setFieldValue}
                      values={values}
                      remove={remove}
                    />
                    <DS.Button
                      type="button"
                      disabled={values.valoresContribuicao.length === 5}
                      onClick={() => {
                        if (values.valoresContribuicao.length < 5) {
                          push({
                            descricaoGrupo: '',
                            participante: '',
                            empresa: '',
                            valorContribuicao: '',
                          });
                        }
                      }}
                    >
                      {Enum.Botoes.Adicionar}
                    </DS.Button>

                  </>
                )}
              </FieldArray>
            </DS.Grid.Item>
            <DS.Grid.Item xs={1} lg={1 / 2}>
              <TextLabel variant="body02-sm">
                {Enum.RegrasContratuais.CuidadoExtra}
              </TextLabel>
            </DS.Grid.Item>
            <DS.Grid.Item xs={1} lg={1}>
              <TabelaCuidadoExtra
                values={values}
                setFieldValue={setFieldValue}
              />

            </DS.Grid.Item>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="pagamentoContribuicao"
              label={Enum.RegrasContratuais.PagamentoContribuicao}
              component={DS.Select}
              placeholder="Selecione"
              value={values.pagamentoContribuicaoParticipante}
              error={errors.pagamentoContribuicao}
              errorMessage={errors.pagamentoContribuicao}
              onChange={({
                target: { value },
              }: React.ChangeEvent<{
                text: string;
                value: string;
              }>) => {
                setFieldValue('pagamentoContribuicao', value);
              }}
              onBlur={handleBlur}
            >
              {Enum.SELECT_OPTIONS_PAGAMENTO_cONTRIBUICAO.map(
                optPagamentoContribuicao => (
                  <DS.Select.Item
                    key={optPagamentoContribuicao.key}
                    value={optPagamentoContribuicao.key}
                    text={optPagamentoContribuicao.value}
                    selected={
                      optPagamentoContribuicao.key ===
                      values.pagamentoContribuicaoParticipante
                    }
                  />
                ),
              )}
            </Field>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="diaPagamento"
              label={Enum.RegrasContratuais.DiaPagamento}
              component={DS.Select}
              placeholder="Selecione"
              value={values.diaPagamento}
              error={errors.diaPagamento}
              errorMessage={errors.diaPagamento}
              onChange={({
                target: { value },
              }: React.ChangeEvent<{
                text: string;
                value: string;
              }>) => {
                setFieldValue('diaPagamento', value);
              }}
              onBlur={handleBlur}
            >
              {Enum.SELECT_OPTION_DIA_PAGAMENTO.map(optdiaPagamento => (
                <DS.Select.Item
                  key={optdiaPagamento.key}
                  value={optdiaPagamento.key}
                  text={optdiaPagamento.value}
                  selected={optdiaPagamento.key === values.diaPagamento}
                />
              ))}
            </Field>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="formaPagamentoRegraContratual"
              label={Enum.RegrasContratuais.FormaPagamento}
              component={DS.Select}
              placeholder="Selecione"
              value={values.formaPagamentoRegraContratual}
              error={errors.formaPagamentoRegraContratual}
              errorMessage={errors.formaPagamentoRegraContratual}
              onChange={({
                target: { value },
              }: React.ChangeEvent<{
                text: string;
                value: string;
              }>) => {
                setFieldValue('formaPagamentoRegraContratual', value);
                formFop.selectFormaPagamentoRegra(value);
              }}
              onBlur={handleBlur}
            >
              {Enum.SELECT_FORMA_PAGAMENTO_REGRA_CONTRATUAL.map(
                optFormaPagamentoContratual => (
                  <DS.Select.Item
                    key={optFormaPagamentoContratual.key}
                    value={optFormaPagamentoContratual.key}
                    text={optFormaPagamentoContratual.value}
                    selected={
                      optFormaPagamentoContratual.key ===
                      values.formaPagamentoRegraContratual
                    }
                  />
                ),
              )}
            </Field>
          </DS.Grid.Item>
          <RenderConditional
            condition={values.formaPagamentoRegraContratual === 'debitoConta'}
          >
            <DS.Grid.Item xs={1} lg={1 / 2}>
              <Field
                name="BancoPagamentoConta"
                label={Enum.RegrasContratuais.Banco}
                component={DS.TextField}
                value={Enum.RegrasContratuais.Caixa}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue('BancoPagamentoConta', value);
                }}
                onBlur={handleBlur}
                disabled
              />
            </DS.Grid.Item>
            <DS.Grid.Item xs={1} lg={1 / 2}>
              <Field
                name="agenciaPagamentoConta"
                label={Enum.RegrasContratuais.Agencia}
                component={DS.TextField}
                maxLength={4}
                value={values.agenciaPagamentoConta}
                error={errors.agenciaPagamentoConta}
                errorMessage={errors.agenciaPagamentoConta}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue(
                    'agenciaPagamentoConta',
                    value
                  );
                }}
                onBlur={handleBlur}
              />
            </DS.Grid.Item>
            <DS.Grid.Item xs={1} lg={1 / 2}>
              <Field
                name="contaPagamentoConta"
                label={Enum.RegrasContratuais.Conta}
                component={DS.TextField}
                maxLength={11}
                value={values.contaPagamentoConta}
                error={errors.contaPagamentoConta}
                errorMessage={errors.contaPagamentoConta}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue('contaPagamentoConta', value);
                }}
                onBlur={handleBlur}
              />
            </DS.Grid.Item>
            <DS.Grid.Item xs={1} lg={1 / 2}>
              <Field
                name="operacaoPagamentoConta"
                label={Enum.RegrasContratuais.Operacao}
                component={DS.TextField}
                maxLength={4}
                value={values.operacaoPagamentoConta}
                error={errors.operacaoPagamentoConta}
                errorMessage={errors.operacaoPagamentoConta}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue('operacaoPagamentoConta', value);
                }}
                onBlur={handleBlur}
              />
            </DS.Grid.Item>
          </RenderConditional>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <RenderConditional
              condition={
                values.formaCusteioModalidadePlano === 'averbado'
              }
            >
              <Field
                name="perdaVinculo"
                label={Enum.RegrasContratuais.PerdaVinculo}
                component={DS.TextField}
                disabled
                value={Enum.SELECT_OPTION_PERDA_VINCULO[0].value}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue('perdaVinculo', value);
                }}
                onBlur={handleBlur}
              />
            </RenderConditional>
            <RenderConditional
              condition={
                values.formaCusteioModalidadePlano !== 'averbado'
              }
            >
              <Field
                name="perdaVinculo"
                label={Enum.RegrasContratuais.PerdaVinculo}
                component={DS.Select}
                placeholder="Selecione"
                value={values.perdaVinculo}
                error={errors.perdaVinculo}
                errorMessage={errors.perdaVinculo}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('perdaVinculo', value);
                }}
                onBlur={handleBlur}
              >
                {Enum.SELECT_OPTION_PERDA_VINCULO.map(
                  optPerdaVinculo => (
                    <DS.Select.Item
                      key={optPerdaVinculo.key}
                      value={optPerdaVinculo.key}
                      text={optPerdaVinculo.value}
                      selected={
                        optPerdaVinculo.key === values.perdaVinculo
                      }
                    />
                  ),
                )}
              </Field>
            </RenderConditional>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1}>
            <FieldArray name="valoresPlano">
              {({ remove, insert }) => (
                <>
                  <TabelaValoresPlano
                    setFieldValue={setFieldValue}
                    values={values}
                    handleBlur={handleBlur}
                    remove={remove}
                  />
                  <DS.Button
                    type="button"
                    disabled={values.valoresPlano.length === 6}
                    onClick={() => {
                      if (values.valoresPlano.length < 6) {
                        insert(values.valoresContribuicao.length - 2, {
                          tempoMeses: '',
                          porcentagemReversao: '',
                        })
                      }
                    }}
                  >
                    {Enum.Botoes.Adicionar}
                  </DS.Button>
                </>
              )}
            </FieldArray>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="demisaoJustaCausa"
              label={Enum.RegrasContratuais.JustaCausa}
              component={DS.Select}
              placeholder="Selecione"
              value={values.demisaoJustaCausa}
              error={errors.demisaoJustaCausa}
              errorMessage={errors.demisaoJustaCausa}
              onChange={({
                target: { value },
              }: React.ChangeEvent<{
                text: string;
                value: string;
              }>) => {
                setFieldValue('demisaoJustaCausa', value);
              }}
              onBlur={handleBlur}
            >
              {Enum.SELECT_DEMISSAO_JUSTA_CAUSA.map(
                optDemissaoJustaCausa => (
                  <DS.Select.Item
                    key={optDemissaoJustaCausa.key}
                    value={optDemissaoJustaCausa.key}
                    text={optDemissaoJustaCausa.value}
                    selected={
                      optDemissaoJustaCausa.key ===
                      values.formaPagamentoRegraContratual
                    }
                  />
                ),
              )}
            </Field>
          </DS.Grid.Item>
          <TitleSection>{Enum.Titulos.Penalidades}</TitleSection>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <RenderConditional
              condition={
                values.formaCusteioModalidadePlano === 'averbado'
              }
            >
              <Field
                name="penalidades"
                label={Enum.RegrasContratuais.InformativoPenalidades}
                component={DS.TextField}
                disabled
                value={Enum.SELET_OPTIONS_PENALIDADE[0].value}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue('penalidades', value);
                }}
                onBlur={handleBlur}
              />
            </RenderConditional>
            <RenderConditional
              condition={
                values.formaCusteioModalidadePlano !== 'averbado'
              }
            >
              <Field
                name="penalidades"
                label={Enum.RegrasContratuais.InformativoPenalidades}
                component={DS.Select}
                placeholder="Selecione"
                value={values.penalidades}
                error={errors.penalidades}
                errorMessage={errors.penalidades}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('penalidades', value);
                }}
                onBlur={handleBlur}
              >
                {Enum.SELET_OPTIONS_PENALIDADE.map(optPenalidade => (
                  <DS.Select.Item
                    key={optPenalidade.key}
                    value={optPenalidade.key}
                    text={optPenalidade.value}
                    selected={optPenalidade.key === values.penalidades}
                  />
                ))}
              </Field>
            </RenderConditional>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <RenderConditional
              condition={
                values.formaCusteioModalidadePlano === 'averbado'
              }
            >
              <Field
                name="distribuicaoContaColetiva"
                label={Enum.RegrasContratuais.DistribuicaoContaColetiva}
                component={DS.TextField}
                disabled
                value={
                  Enum.SELECT_OPTIONS_DISTRIBUICAO_CONTA_COLETIVA[0]
                    .value
                }
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('distribuicaoContaColetiva', value);
                }}
                onBlur={handleBlur}
              />
            </RenderConditional>
            <RenderConditional
              condition={
                values.formaCusteioModalidadePlano !== 'averbado'
              }
            >
              <Field
                name="distribuicaoContaColetiva"
                label={Enum.RegrasContratuais.DistribuicaoContaColetiva}
                component={DS.Select}
                placeholder="Selecione"
                value={values.distribuicaoContaColetiva}
                error={errors.distribuicaoContaColetiva}
                errorMessage={errors.distribuicaoContaColetiva}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('distribuicaoContaColetiva', value);
                }}
                onBlur={handleBlur}
              >
                {Enum.SELECT_OPTIONS_DISTRIBUICAO_CONTA_COLETIVA.map(
                  optDistribuicaoColetiva => (
                    <DS.Select.Item
                      key={optDistribuicaoColetiva.key}
                      value={optDistribuicaoColetiva.key}
                      text={optDistribuicaoColetiva.value}
                      selected={
                        optDistribuicaoColetiva.key ===
                        values.distribuicaoContaColetiva
                      }
                    />
                  ),
                )}
              </Field>
            </RenderConditional>
          </DS.Grid.Item>
          <TitleSection>{Enum.Titulos.RegraAposentadoria}</TitleSection>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="IdadeAposentadoria"
              label={Enum.RegrasContratuais.IdadeAposentadoria}
              component={DS.TextField}
              maxLength={2}
              value={values.idadeAposentadoria}
              error={errors.idadeAposentadoria}
              errorMessage={errors.idadeAposentadoria}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('idadeAposentadoria', FormatarNumerico(value));
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="tempoMinimoPlano"
              label={Enum.RegrasContratuais.TempoMinimoPlano}
              component={DS.TextField}
              maxLength={2}
              value={values.tempoMinimoPlano}
              min={5}
              error={errors.tempoMinimoPlano}
              errorMessage={errors.tempoMinimoPlano}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('tempoMinimoPlano', FormatarNumerico(value));
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <TitleSection>
            {Enum.Titulos.AplicacaoRecursosInstituidora}
          </TitleSection>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <RenderConditional
              condition={
                values.formaCusteioModalidadePlano === 'averbado'
              }
            >
              <Field
                name="recursoInstituido"
                label={Enum.RegrasContratuais.RecursosInstituidoras}
                component={DS.TextField}
                disabled
                value={
                  Enum.SELECT_OPTION_RECURSOS_INSTITUIDORA[0].value
                }
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('recursoInstituido', value);
                }}
                onBlur={handleBlur}
              />
            </RenderConditional>
            <RenderConditional
              condition={
                values.formaCusteioModalidadePlano !== 'averbado'
              }
            >
              <Field
                name="recursosInstituidora"
                label={Enum.RegrasContratuais.RecursosInstituidoras}
                component={DS.Select}
                placeholder="Selecione"
                value={values.penalidades}
                error={errors.penalidades}
                errorMessage={errors.penalidades}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<{
                  text: string;
                  value: string;
                }>) => {
                  setFieldValue('recursosInstituidora', value);
                  formFop.openFieldInstituidoraValor(value);
                }}
                onBlur={handleBlur}
              >
                {Enum.SELECT_OPTION_RECURSOS_INSTITUIDORA.map(
                  optDistribuicaoColetiva => (
                    <DS.Select.Item
                      key={optDistribuicaoColetiva.key}
                      value={optDistribuicaoColetiva.key}
                      text={optDistribuicaoColetiva.value}
                      selected={
                        optDistribuicaoColetiva.key ===
                        values.recursosInstituidora
                      }
                    />
                  ),
                )}
              </Field>
            </RenderConditional>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <RenderConditional
              condition={values.recursosInstituidora === "fundo"}
            >
              <Field
                name="valorfundo"
                maxLength={50}
                label={Enum.RegrasContratuais.DescricaoFundo}
                component={DS.TextField}
                value={values.descricaoFundo}
                error={errors.descricaoFundo}
                errorMessage={errors.descricaoFundo}
                onChange={({
                  target: { value },
                }: React.ChangeEvent<HTMLInputElement>) => {
                  setFieldValue('descricaoFundo', value);
                }}
                onBlur={handleBlur}
              />
            </RenderConditional>
          </DS.Grid.Item>
        </S.AccordionItem>
      </DS.Accordion>
    </div>)
}
