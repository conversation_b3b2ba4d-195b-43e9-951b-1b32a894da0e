import { Card, Display } from "@cvp/design-system/react";
import RenderConditional from "main/components/RenderConditional";
import SkeletonLoading from "main/components/SkeletonLoading";
import { tryGetValueOrDefault } from "main/utils/conditional";
import { DEFAULT_NO_DATA_TABLE } from "main/constants/messages";
import { TableInadimplencia } from "painelInadimplencia/pages/TableInadimplencia";
import {
  IModalDetalhamentoParcelasProps,
  IPagamentosAdimplenciaResponse,
} from "painelInadimplencia/types/PainelInadimplencia";
import { TableColumn } from "react-data-table-component";
import { ModalDetalhamentoStyled } from "./styles";

const ModalDetalhamentoParcelas = ({
  open,
  onClose,
  listaPagamentosDetalhado,
  colunasDetalhesPagamentos,
  isLoading,
}: IModalDetalhamentoParcelasProps) => {
  return (
    <ModalDetalhamentoStyled show={open} onClose={onClose}>
      <RenderConditional condition={!!isLoading}>
        <SkeletonLoading blocks={1} lines={10} />
      </RenderConditional>

      <RenderConditional condition={!isLoading}>
        <Display type="display-block">
          <Card>
            <Card.Content>
              <Display>
                <TableInadimplencia
                  noHeader
                  responsive
                  striped
                  highlightOnHover
                  data={tryGetValueOrDefault([listaPagamentosDetalhado], [])}
                  columns={
                    colunasDetalhesPagamentos as unknown as TableColumn<IPagamentosAdimplenciaResponse>[]
                  }
                  noDataComponent={DEFAULT_NO_DATA_TABLE}
                  pagination
                  paginationRowsPerPageOptions={[5, 10, 25, 50, 100]}
                  paginationPerPage={5}
                  paginationComponentOptions={{
                    rowsPerPageText: "Items por página",
                    rangeSeparatorText: "de",
                  }}
                />
              </Display>
            </Card.Content>
          </Card>
        </Display>
      </RenderConditional>
    </ModalDetalhamentoStyled>
  );
};

export default ModalDetalhamentoParcelas;
