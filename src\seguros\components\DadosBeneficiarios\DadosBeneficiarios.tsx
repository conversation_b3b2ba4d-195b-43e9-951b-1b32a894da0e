import React from 'react';
import { But<PERSON>, Card, Display, Divider, Text } from '@cvp/design-system/react';
import EmailSenderModal from 'main/components/EmailSenderModal';
import RenderConditional from 'main/components/RenderConditional';
import SkeletonLoading from 'main/components/SkeletonLoading';
import Table from 'main/components/Table';
import {
  checkIfAllItemsAreTrue,
  getTernaryResult,
} from 'main/utils/conditional';
import {
  conditionalRowStyles,
  TEXTOS_DADOS_BENEFICIARIOS,
} from 'seguros/constants/DadosBeneficiarios';
import {
  COLUNAS_TABELA_BENEFICIARIOS,
  COLUNAS_TABELA_BENEFICIARIOS_VIDA,
} from 'seguros/constants/DadosSegurado';
import useVidaNavigation from 'seguros/hooks/useVidaNavigation';
import { TDadosBeneficiariosProps } from 'seguros/types/DadosBeneficiarios';
import AdicionarBeneficiario from 'vida/features/AdicionarBeneficiario';
import {
  commonTablePropsFactory,
  montaTabelaBeneficiariosFactory,
} from 'seguros/factories/DadosBeneficiariosFactory';
import { TipoSeguro } from 'seguros/types/DadosSegurado';
import ModalAlertaHerdeiros from './ModalAlertaHerdeiros';
import ModalAlertaExcluir from './ModalAlertaExcluido';
import { useDadosBeneficiariosVida } from 'seguros/hooks/useDadosBeneficiariosVida';

export const DadosBeneficiarios: React.FC<TDadosBeneficiariosProps> = ({
  dadosCertificado,
}) => {
  const { goDadosSeguradoVida } = useVidaNavigation();

  const {
    loadingListar,
    loadingSalvar,
    listaBeneficiarios,
    podeEditar,
    exibirBeneficiario,
    somaPercentuais,
    disableConfirmarAlteracoes,
    beneficiarioAExcluir,
    exibirAlertaHerdeiros,
    emailDefault,
    objetoEmail,
    alterarPercentual,
    excluirBeneficiario,
    setExibirBeneficiario,
    handleAdicioinarBeneficiario,
    handleVerificaConfirmacao,
    handleResetBeneficiario,
    handleExcluirBeneficiario,
    handleAlertaHerdeiros,
    handleConfirmarAlteracoes,
    handleImprimir,
  } = useDadosBeneficiariosVida({
    dadosCertificado,
  });

  return (
    <>
      <Card>
        <Card.Content>
          <Text
            variant="headline-05"
            color="primary"
            margin
            data-testid="dadosBeneficiariosTitulo"
          >
            {TEXTOS_DADOS_BENEFICIARIOS.TITULO}
          </Text>
          <Text
            variant="caption-02"
            color="text-light"
            margin
            data-testid="dadosBeneficiariosSubtitulo"
          >
            {TEXTOS_DADOS_BENEFICIARIOS.SUBTITULO}
          </Text>

          <div data-testid="hide-print">
            <RenderConditional condition={loadingListar}>
              <SkeletonLoading blocks={1} />
            </RenderConditional>

            <RenderConditional condition={!loadingListar}>
              <Table
                columns={getTernaryResult(
                  podeEditar,
                  COLUNAS_TABELA_BENEFICIARIOS_VIDA,
                  COLUNAS_TABELA_BENEFICIARIOS,
                )}
                data={montaTabelaBeneficiariosFactory({
                  listaBeneficiarios,
                  podeEditar,
                  loadingListar,
                  loadingSalvar,
                  alterarPercentual,
                  excluirBeneficiario,
                })}
                conditionalRowStyles={conditionalRowStyles}
                {...commonTablePropsFactory({
                  pagination: false,
                })}
              />

              <Divider />

              <Display justify="space-between" style={{ margin: '0 5px' }}>
                <Text
                  color={getTernaryResult(
                    somaPercentuais !== 100,
                    'error',
                    'success',
                  )}
                >
                  <strong>TOTAL</strong>
                </Text>
                <Text
                  color={getTernaryResult(
                    somaPercentuais !== 100,
                    'error',
                    'success',
                  )}
                >
                  <strong>{somaPercentuais}%</strong>
                </Text>
              </Display>
            </RenderConditional>
          </div>

          <RenderConditional
            condition={checkIfAllItemsAreTrue([
              !exibirBeneficiario,
              podeEditar,
            ])}
          >
            <Display justify="space-between">
              <Button
                variant="secondary"
                onClick={() => {
                  setExibirBeneficiario(true);
                }}
              >
                Adicionar beneficiário
              </Button>

              <Button
                variant="primary"
                loading={loadingSalvar}
                disabled={disableConfirmarAlteracoes}
                onClick={handleVerificaConfirmacao}
              >
                Confirmar alterações
              </Button>
            </Display>
          </RenderConditional>

          <RenderConditional
            condition={checkIfAllItemsAreTrue([exibirBeneficiario, podeEditar])}
          >
            <AdicionarBeneficiario
              onAdicionarBeneficiario={handleAdicioinarBeneficiario}
              onResetBeneficiario={handleResetBeneficiario}
            />
          </RenderConditional>

          <Divider />
          <br />
          <Display>
            <Button
              onClick={goDadosSeguradoVida}
              variant="outlined"
              data-testid="voltar"
              className="hide-print"
            >
              Voltar
            </Button>
            <Button
              className="hide-print"
              variant="secondary"
              onClick={handleImprimir}
              data-testid="print-button"
            >
              Imprimir
            </Button>
            <EmailSenderModal
              objetoEmail={objetoEmail}
              defaultEmail={emailDefault}
              seguro={TipoSeguro.VIDA}
            />
          </Display>
        </Card.Content>
      </Card>

      <ModalAlertaExcluir
        exibirAlertaHerdeiros={!!beneficiarioAExcluir}
        statusBeneficiario={beneficiarioAExcluir?.status}
        nomeBeneficiario={beneficiarioAExcluir?.nomeBeneficiario}
        handleFecharExcluirBeneficiario={() => excluirBeneficiario()}
        handleConfirmarExcluirBeneficiario={handleExcluirBeneficiario}
      />

      <ModalAlertaHerdeiros
        exibirAlertaHerdeiros={exibirAlertaHerdeiros}
        handleAlertaHerdeiros={handleAlertaHerdeiros}
        handleConfirmarAlteracoes={handleConfirmarAlteracoes}
      />
    </>
  );
};
