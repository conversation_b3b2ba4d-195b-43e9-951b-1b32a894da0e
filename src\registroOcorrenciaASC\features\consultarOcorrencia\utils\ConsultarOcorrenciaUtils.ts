import * as Yup from 'yup';
import * as CONSTS from 'registroOcorrenciaASC/features/consultarOcorrencia/constants/constants';
import {
  checkIfSomeItemsAreTrue,
  getTernaryResult,
} from 'main/utils/conditional';
import { isCnpj, isCpf } from 'main/utils/cpf_cnpj';
import { FormikErrors } from 'formik';
import { IFormikConsultaOcorrenciaValues } from '../types/ConsultarOcorrencia';

/**
 * Limita o tamanho de um texto, adicionando três pontos (...) no final, se necessário.
 *
 * @param {string} texto - O texto original que será limitado.
 * @param {number} [limite=20] - O tamanho máximo do texto permitido (incluindo os três pontos).
 * @returns {string} - O texto limitado, com os três pontos adicionados se necessário.
 */
export const charSizeLimit = (texto: string, limite = 20): string => {
  if (texto.length <= limite) return texto;

  const extensao = texto.substring(texto.lastIndexOf('.') + 1);
  const nomeArquivo = texto.substring(0, texto.lastIndexOf('.'));
  const tamanhoNomeArquivo = limite - extensao.length - 3; // Considerando os três pontos (...)
  const nomeArquivoLimitado = nomeArquivo.substring(0, tamanhoNomeArquivo);

  return `${nomeArquivoLimitado}...${extensao}`;
};

export const FormikDetalhesOcorrenciaSchema = Yup.object({
  conteudoTextarea: Yup.string().required(
    CONSTS.VALIDATION_DEFAULT_MESSAGE_SCHEMA.CAMPO_OBRIGATORIO,
  ),
});

/**
 * Retorna uma representação formatada do tamanho de um arquivo em bytes, kilobytes (KB),
 * megabytes (MB), gigabytes (GB) ou terabytes (TB).
 *
 * @param {number} [fileSize] - O tamanho do arquivo em bytes.
 * @returns {string} - Uma string formatada representando o tamanho do arquivo com a unidade de medida apropriada.
 *                    Por exemplo: "1.23 KB", "456 MB", "789 GB", etc.
 *                    Retorna "Valor desconhecido" se o tamanho do arquivo não for informado (undefined).
 *                    Retorna "0 bytes" se o tamanho do arquivo for 0.
 */
export const fileSizeMask = (fileSize?: number): string => {
  if (fileSize === undefined) return 'Valor desconhecido';

  if (fileSize === 0) return '0 bytes';

  const medidas = ['bytes', 'KB', 'MB', 'GB', 'TB'];

  // Calcula o índice da unidade de medida apropriada com base no tamanho do arquivo.
  const index = Math.floor(Math.log2(fileSize) / 10);

  // Calcula o tamanho do arquivo na unidade de medida apropriada e arredonda para 2 casas decimais.
  const tamanho = (fileSize / 1024 ** index).toFixed(2);

  return `${tamanho} ${medidas[index]}`;
};

export const validarValoresFormConsultaOcorrencia = (
  values: IFormikConsultaOcorrenciaValues,
): FormikErrors<IFormikConsultaOcorrenciaValues> => {
  const cpfCnpjError = !checkIfSomeItemsAreTrue([
    isCnpj(values.cpfCnpj),
    isCpf(values.cpfCnpj),
  ]);

  const protocoloError = !values.protocolo;

  const initialDateError = !values.inputDate.initialDate;
  const finalDateError = !values.inputDate.finalDate;
  const dateError = checkIfSomeItemsAreTrue([initialDateError, finalDateError]);

  return {
    ...getTernaryResult(
      cpfCnpjError,
      {
        cpfCnpj: CONSTS.FORM_CONSULTA_OCORRENCIA_ERRORS.CPF_CNPJ,
      },
      {},
    ),
    ...getTernaryResult(
      protocoloError,
      {
        protocolo: CONSTS.FORM_CONSULTA_OCORRENCIA_ERRORS.PROTOCOLO,
      },
      {},
    ),
    ...getTernaryResult(
      dateError,
      {
        inputDate: {
          initialDate: getTernaryResult(
            !!initialDateError,
            CONSTS.FORM_CONSULTA_OCORRENCIA_ERRORS.DATE,
            undefined,
          ),
          finalDate: getTernaryResult(
            !!finalDateError,
            CONSTS.FORM_CONSULTA_OCORRENCIA_ERRORS.DATE,
            undefined,
          ),
        },
      },
      {},
    ),
  };
};
