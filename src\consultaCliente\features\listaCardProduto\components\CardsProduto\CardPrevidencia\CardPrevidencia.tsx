import { Divider, Text, Tooltip } from '@cvp/design-system/react';
import {
  ICardProdutoPrevidenciaProps,
  Modalidade,
} from 'consultaCliente/features/listaCardProduto/components/CardsProduto/CardPrevidencia/types/CardPrevidencia.types';
import NomeCertificadoCardItem from 'consultaCliente/features/listaCardProduto/components/CardsProduto/NomeCertificadoCardItem';
import * as S from 'consultaCliente/features/listaCardProduto/components/CardsProduto/styles';
import { MODALIDADES } from 'consultaCliente/features/listaCardProduto/consts/constDescricaoModalidades';
import Icon from 'main/components/Icon';
import TagStatusCertificado from 'main/components/TagStatusCertificado/TagStatusCertificado';
import { AppContext } from 'main/contexts/AppContext';
import { formatarData } from 'main/utils';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import React, { useContext } from 'react';
import { LISTA_PRODUTO } from 'registroOcorrenciaASC/features/registrarOcorrencia/constants/constants';

export const CardPrevidencia: React.FunctionComponent<
  ICardProdutoPrevidenciaProps
> = ({
  onClick,
  produto,
  pessoa,
  certificadoNumero,
  situacao = '',
  regimeTributario,
  emissao,
  aposentadoria,
  taxaAtual,
}) => {
  const { nomeSocial } = useContext(AppContext);

  const getInfoPorModalidade = (modalidade: Modalidade) => {
    const infoModalidade = MODALIDADES[modalidade];
    if (infoModalidade) {
      return tryGetValueOrDefault([infoModalidade.info], '');
    }
    return '';
  };

  const getCorPorModalidade = (modalidade: Modalidade) => {
    const infoModalidade = MODALIDADES[modalidade];
    if (infoModalidade) {
      return tryGetValueOrDefault([infoModalidade.cor], '');
    }
    return '';
  };

  return (
    <S.ContainerCard>
      <span>
        <S.Text title={produto.descricaoProduto} status={situacao}>
          {produto.descricaoProduto}
        </S.Text>
        <TagStatusCertificado status={situacao} />
      </span>
      <Divider />

      <S.ContentData>
        <NomeCertificadoCardItem
          nomeCliente={tryGetValueOrDefault(
            [nomeSocial],
            pessoa.pessoaFisica.nome,
          )}
          numCertificado={certificadoNumero}
          classificacaoDoPlano={produto.classificacaoDoPlano}
          taxaAtual={taxaAtual}
          classificacaoCard={LISTA_PRODUTO[0].produto}
        />
        <S.CardMobilidade
          color={getCorPorModalidade(produto.modalidade as Modalidade)}
        >
          <S.IconContainer>
            <Icon name="documentBack" />
          </S.IconContainer>

          <Tooltip
            text={getInfoPorModalidade(produto.modalidade as Modalidade)}
            position="top"
            variant="gray"
          >
            <span style={{ textAlign: 'left', marginLeft: '-6px' }}>
              <Text variant="caption-02">Modalidade / Regime Tributário</Text>

              <Text variant="body03-md">
                {produto.modalidade} / {regimeTributario} &nbsp;
                <Icon name="warning" />
              </Text>
            </span>
          </Tooltip>
        </S.CardMobilidade>

        <div>
          <S.IconContainer>
            <Icon name="calendar" />
          </S.IconContainer>

          <span>
            <Text variant="caption-02">Vigência</Text>

            <Text variant="body03-md">
              {formatarData(emissao)} - {formatarData(aposentadoria)}
            </Text>
          </span>
        </div>
      </S.ContentData>
      <S.ButtonCard onClick={onClick} fullWidth small>
        Detalhes
      </S.ButtonCard>
    </S.ContainerCard>
  );
};
