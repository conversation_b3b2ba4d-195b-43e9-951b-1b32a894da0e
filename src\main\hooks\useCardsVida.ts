import { useState } from 'react';
import {
  ICardProdutoVida,
  IControlePaginacao,
} from 'consultaCliente/features/listaCardProduto/interfaces/ICardProdutoVida';
import { obterProdutosVida } from 'consultaCliente/features/listaCardProduto/service/consultarProdutosApi';
import {
  checkIfAllItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import { UseCardsVidaReturn } from 'consultaCliente/features/listaCardProduto/types/cardsVidaType';
import { initialValues } from 'main/constants/paginacaoInitialValues';
import { formatarSeguros } from 'consultaCliente/features/listaCardProduto/utils/filtrarSeguros';
import { EnumStatusVida } from 'consultaCliente/features/listaCardProduto/types/enumStatus';

const useCardsVida = (cpfCnpj?: string): UseCardsVidaReturn => {
  const [dadosCardsVidaAtivos, setDadosCardsVidaAtivos] = useState<
    ICardProdutoVida[] | undefined
  >();
  const [dadosCardsVidaInativos, setDadosCardsVidaInativos] = useState<
    ICardProdutoVida[] | undefined
  >();

  const [isLoadingVida, setIsLoadingVida] = useState<boolean>(false);
  const [paginacao, setPaginacao] = useState<IControlePaginacao>(initialValues);

  const isFirstPage = checkIfAllItemsAreTrue([
    paginacao.pageAtivos <= 1,
    paginacao.pageAtivos <= 1,
  ]);

  const fetchDadosCardsVida = async (
    codCliente?: string,
    novaPagina?: number,
  ): Promise<void> => {
    setIsLoadingVida(true);
    const payload = {
      cpf: tryGetValueOrDefault([codCliente], ''),
      seguroAtivo: {
        tamanhoPagina: 12,
        numeroPagina: tryGetValueOrDefault([novaPagina], 1),
        retornarTotalItens: true,
      },
      seguroInativo: {
        numeroPagina: tryGetValueOrDefault([novaPagina], 1),
        tamanhoPagina: 12,
        retornarTotalItens: true,
      },
    };
    const resultado = await obterProdutosVida(payload);
    setDadosCardsVidaAtivos(
      formatarSeguros(resultado?.seguroAtivo?.itensPaginados),
    );
    setDadosCardsVidaInativos(
      formatarSeguros(resultado?.seguroInativo?.itensPaginados),
    );

    setPaginacao({
      ...paginacao,
      totalPagesAtivos: tryGetValueOrDefault(
        [resultado?.seguroAtivo?.paginacao?.totalPaginas],
        0,
      ),
      totalPagesInativos: tryGetValueOrDefault(
        [resultado?.seguroInativo?.paginacao?.totalPaginas],
        0,
      ),
      pageAtivos: tryGetValueOrDefault(
        [resultado?.seguroAtivo?.paginacao?.paginaAtual],
        0,
      ),
      pageInativos: tryGetValueOrDefault(
        [resultado?.seguroInativo?.paginacao?.paginaAtual],
        0,
      ),
    });
    setIsLoadingVida(false);
  };

  const handleChangePage = (novaPagina: number, status: string): void => {
    if (status === EnumStatusVida.ativos) {
      setPaginacao({ ...paginacao, pageAtivos: novaPagina });
    }
    if (status === EnumStatusVida.inativos) {
      setPaginacao({ ...paginacao, pageInativos: novaPagina });
    }
    fetchDadosCardsVida(cpfCnpj, novaPagina);
  };
  return {
    paginacao,
    dadosCardsVidaAtivos,
    dadosCardsVidaInativos,
    isLoadingVida,
    isFirstPage,
    handleChangePage,
    fetchDadosCardsVida,
  };
};

export default useCardsVida;
