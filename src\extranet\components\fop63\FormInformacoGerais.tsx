import * as DS from '@cvp/design-system/react';
import { FormatarNumerico } from 'extranet/hooks/useFormFops';
import { IFormBase63 } from 'extranet/types/InterfacesFop63/IFormBase63';
import { Field } from 'formik';
import { TitleSection } from 'main/styles/GlobalStyle';
import masks from 'main/utils/masks';
import * as S from '../../../extranet/features/fops/pages/styles';
import * as Const from '../../types/ConstFop63';
import * as Enum from '../../types/enum';

export const FormInformacoGerais: React.FC<IFormBase63> = ({
  setFieldValue,
  values,
  handleBlur,
  errors
}) => {
  return (
    <div>
      <DS.Accordion open>
        <S.AccordionItem
          title={
            <TitleSection>
              {Enum.Titulos.InformacoesGerais}
            </TitleSection>}
        >
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="nomeDaEmpresa"
              label={Enum.InformacoesGerais.NomeEmpresa}
              maxLength={50}
              component={DS.TextField}
              value={values.nomeDaEmpresa}
              error={errors.nomeDaEmpresa}
              errorMessage={errors.nomeDaEmpresa}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('nomeDaEmpresa', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="atividadePrincipalEmpresa"
              label={Enum.InformacoesGerais.AtividadePrincipal}
              component={DS.TextField}
              maxLength={50}
              value={values.atividadePrincipalEmpresa}
              error={errors.atividadePrincipalEmpresa}
              errorMessage={errors.atividadePrincipalEmpresa}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('atividadePrincipalEmpresa', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="cnpjEmpresa"
              label={Enum.InformacoesGerais.CNPJ}
              component={DS.TextField}
              inputMode="numeric"
              pattern="[0-9]*"
              maxLength={17}
              value={masks.cnpj.mask(FormatarNumerico(values.cnpjEmpresa))}
              error={errors.cnpjEmpresa}
              errorMessage={errors.cnpjEmpresa}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('cnpjEmpresa', masks.cnpj.mask(FormatarNumerico(value)));
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="faturamento"
              label={Enum.InformacoesGerais.Faturamento}
              component={DS.Select}
              placeholder={Const.SELECIONE_FATURAMENTO}
              value={values.faturamento}
              error={errors.faturamento}
              errorMessage={errors.faturamento}
              onChange={({
                target: { value },
              }: React.ChangeEvent<{
                text: string;
                value: string;
              }>) => {
                setFieldValue('faturamento', value);
              }}
              onBlur={handleBlur}
            >
              {Enum.SELECT_OPTIONS_FATURAMENTO.map(optFaturamento => (
                <DS.Select.Item
                  key={optFaturamento.key}
                  value={optFaturamento.key}
                  text={optFaturamento.value}
                  selected={optFaturamento.key === values.faturamento}
                />
              ))}
            </Field>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="emailInstitucional"
              label={Enum.InformacoesGerais.EmailInstitucional}
              component={DS.TextField}
              maxLength={50}
              value={values.emailInstitucional}
              error={errors.emailInstitucional}
              errorMessage={errors.emailInstitucional}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('emailInstitucional', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="logradouro"
              label={Enum.InformacoesGerais.Logradouro}
              component={DS.TextField}
              value={values.logradouro}
              maxLength={50}
              error={errors.logradouro}
              errorMessage={errors.logradouro}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('logradouro', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="bairro"
              label={Enum.InformacoesGerais.Bairro}
              component={DS.TextField}
              value={values.bairro}
              error={errors.bairro}
              maxLength={50}
              errorMessage={errors.bairro}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('bairro', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="cidade"
              label={Enum.InformacoesGerais.Cidade}
              component={DS.TextField}
              value={values.cidade}
              maxLength={50}
              error={errors.cidade}
              errorMessage={errors.cidade}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('cidade', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="uf"
              label={Enum.InformacoesGerais.UF}
              component={DS.Select}
              placeholder={Const.SELECIONE_UF}
              value={values.uf}
              error={errors.uf}
              errorMessage={errors.uf}
              onChange={({
                target: { value },
              }: React.ChangeEvent<{
                text: string;
                value: string;
              }>) => {
                setFieldValue('uf', value);
              }}
              onBlur={handleBlur}
            >
              {Enum.SELECT_OPTION_UF.map(optUF => (
                <DS.Select.Item
                  key={optUF.key}
                  value={optUF.key}
                  text={optUF.value}
                  selected={optUF.key === values.uf}
                />
              ))}
            </Field>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="cep"
              label={Enum.InformacoesGerais.CEP}
              component={DS.TextField}
              value={masks.cep.mask(values.cep)}
              error={errors.cep}
              errorMessage={errors.cep}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('cep', value);
              }}
              onBlur={handleBlur}
            />
            <br />
            <TitleSection>
              {Enum.Titulos.DadosRepresentante}
            </TitleSection>
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="nomeRepresentante"
              maxLength={50}
              label={Enum.DadosRepresnetantes.NomeRepresentante}
              component={DS.TextField}
              value={values.nomeRepresentante}
              error={errors.nomeRepresentante}
              errorMessage={errors.nomeRepresentante}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('nomeRepresentante', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="emailRepresentante"
              label={Enum.DadosRepresnetantes.EmailRepresentante}
              component={DS.TextField}
              value={values.emailRepresentante}
              maxLength={50}
              error={errors.emailRepresentante}
              errorMessage={errors.emailRepresentante}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('emailRepresentante', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="cargoRepresentante"
              maxLength={50}
              label={Enum.DadosRepresnetantes.CargoRepresentante}
              component={DS.TextField}
              value={values.cargoRepresentante}
              error={errors.cargoRepresentante}
              errorMessage={errors.cargoRepresentante}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('cargoRepresentante', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
        </S.AccordionItem>
      </DS.Accordion>
    </div>)
}
