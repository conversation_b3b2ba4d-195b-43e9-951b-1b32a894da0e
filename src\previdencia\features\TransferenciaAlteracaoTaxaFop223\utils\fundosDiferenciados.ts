import {
  checkIfAllItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import {
  fundosDiferenciadosLabel,
  fundosTradicionaisLabel,
} from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/constants/fundosDiferenciados';
import { ENUM_FUNDO_DIFERENCIADO } from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/types/enum';
import { IResponseListarMelhoresTaxaFamiliaFundo } from 'previdencia/features/TransferenciaAlteracaoTaxaFop223/types/IListarMelhoresTaxasPeco';

export const verificarFundosDiferenciados = (
  fundos?: IResponseListarMelhoresTaxaFamiliaFundo[],
): IResponseListarMelhoresTaxaFamiliaFundo[] | undefined => {
  return fundos?.filter(
    fundo => fundo.efundoDiferenciado === ENUM_FUNDO_DIFERENCIADO.SIM,
  );
};
export const filtrarFamilias = (
  fundos?: IResponseListarMelhoresTaxaFamiliaFundo[],
) => {
  return fundos?.filter(item => {
    if (item.efundoDiferenciado === ENUM_FUNDO_DIFERENCIADO.NAO) {
      return !fundos.some(fundo =>
        checkIfAllItemsAreTrue([
          fundo.familia === item.familia,
          fundo.efundoDiferenciado === ENUM_FUNDO_DIFERENCIADO.SIM,
        ]),
      );
    }
    return true;
  });
};
export const formatarFundos = (
  fundos?: IResponseListarMelhoresTaxaFamiliaFundo[],
): IResponseListarMelhoresTaxaFamiliaFundo[] => {
  const fundosFiltrados = filtrarFamilias(fundos);
  const fundosDiferenciados = tryGetValueOrDefault(
    [verificarFundosDiferenciados(fundosFiltrados)],
    [],
  );
  const fundosTradicionais = fundosFiltrados?.filter(
    fundo => fundo.efundoDiferenciado === ENUM_FUNDO_DIFERENCIADO.NAO,
  );
  return [
    ...fundosDiferenciadosLabel,
    ...fundosDiferenciados,
    ...fundosTradicionaisLabel,
    ...tryGetValueOrDefault([fundosTradicionais], []),
  ];
};

export const obterFlagPorFamilia = (
  familia: number,
  fundos?: IResponseListarMelhoresTaxaFamiliaFundo[],
): string | undefined => {
  return fundos?.find(fundo => fundo.familia === familia)?.efundoDiferenciado;
};
