import { render, screen, waitFor } from '@testing-library/react';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { api } from 'main/services';
import DetalheProposta from '../DetalheProposta';
import * as UseLocationPeco from 'main/hooks/useLocationPeco';

describe('Prestamista - DetalheProposta', () => {
  const mockLocation = {
    key: '',
    pathname: '/welcome',
    hash: '',
    search: '',
    state: {
      proposta: {
        numeroLinhaDoProduto: '2',
        codigoDoEstipulante: '2',
      },
    },
  };
  beforeEach(() => {
    vi.spyOn(UseLocationPeco, 'useLocationPeco').mockReturnValue(mockLocation);
  });

  it('renderiza a tela e verifica se os dados foram apresentados', () => {
    vi.spyOn(api, 'post').mockReturnValue(
      Promise.resolve({
        data: {
          dados: {
            sucesso: false,
            entidade: {
              numeroProposta: null,
              descricaoStatusProposta: 'A INTEGRAR',
              codigoAgenciaVenda: '630',
              dataInicioVigencia: null,
              dataFimVigencia: null,
              valorPremioLiquido: null,
              valorImportanciaSegurada: 40000000,
              descricaoPeriodicidadeCobranca: 'A VISTA',
              numeroCertificado: null,
              nomeSegurado: 'MENDES LIMA ENGENHARIA LTDA',
              cpfCnpjSegurado: null,
            },
          },
        },
      }),
    );
    const queryClient = new QueryClient();

    render(
      <RenderPageWithThemeProviderAndRouter>
        <QueryClientProvider client={queryClient}>
          <DetalheProposta />
        </QueryClientProvider>
      </RenderPageWithThemeProviderAndRouter>,
    );

    waitFor(
      () => {
        const nomeSegurado = screen.getByTestId('nomeSegurado');
        const descricaoPeriodicidadeCobranca = screen.getByTestId(
          /descricaoPeriodicidadeCobranca/i,
        );
        const descricaoStatusProposta = screen.getByTestId(
          /descricaoStatusProposta/i,
        );
        const codigoAgenciaVenda = screen.getByTestId(/codigoAgenciaVenda/i);

        expect(nomeSegurado).toHaveTextContent('MENDES LIMA ENGENHARIA LTDA');
        expect(descricaoStatusProposta).toHaveTextContent('A INTEGRAR');
        expect(codigoAgenciaVenda).toHaveTextContent('630');
        expect(descricaoPeriodicidadeCobranca).toHaveTextContent('A VISTA');
      },
      { timeout: 1000 },
    );
  });
});
