import * as DS from '@cvp/design-system/react';
import * as Enum from '../../types/enum';
import { FormatarNumerico } from "extranet/hooks/useFormFops";
import { Field } from "formik";
import RenderConditional from "main/components/RenderConditional";
import { TitleSection } from "main/styles/GlobalStyle";
import masks from "main/utils/masks";
import { IFormFopContext } from "extranet/types/InterfacesFop63/IFormFopContext";
import * as S from '../../../extranet/features/fops/pages/styles';
import * as Const from '../../types/ConstFop63'

export const FormBeneficioBasico: React.FC<IFormFopContext> = ({
    setFieldValue,
    values,
    handleBlur,
    errors,
    formFop
}) => {
    return (
        <div>
            <DS.Accordion open>
                <S.AccordionItem
                    title={
                        <TitleSection>
                            {Enum.Titulos.BeneficioBasico}
                        </TitleSection>}
                >
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Field
                            name="numeroDeParticipantes"
                            label={Enum.BeneficioBasico.NumeroParticipantes}
                            component={DS.TextField}
                            type="text"
                            inputMode="numeric"
                            pattern="[0-9]*"
                            maxLength={4}
                            value={values.numeroDeParticipantes}
                            error={errors.numeroDeParticipantes}
                            errorMessage={errors.numeroDeParticipantes}
                            onChange={({
                                target: { value },
                            }: React.ChangeEvent<HTMLInputElement>) => {
                                setFieldValue(
                                    'numeroDeParticipantes',
                                    FormatarNumerico(value),
                                );
                            }}
                            onBlur={handleBlur}
                        />
                    </DS.Grid.Item>
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Field
                            name="aporteInicial"
                            label={Enum.BeneficioBasico.AporteInicial}
                            component={DS.Select}
                            placeholder={Const.SELECIONE}
                            value={values.aporteInicial}
                            error={errors.aporteInicial}
                            errorMessage={errors.aporteInicial}
                            onChange={({
                                target: { value },
                            }: React.ChangeEvent<{
                                text: string;
                                value: string;
                            }>) => {
                                setFieldValue('aporteInicial', value);
                                formFop.selectRegraAporte(value);
                            }}
                            onBlur={handleBlur}
                        >
                            {Enum.SELECT_OPTIONS_REGRA_APORTE.map(optRegraAporte => (
                                <DS.Select.Item
                                    key={optRegraAporte.key}
                                    value={optRegraAporte.key}
                                    text={optRegraAporte.value}
                                    selected={optRegraAporte.key === values.aporteInicial}
                                />
                            ))}
                        </Field>
                    </DS.Grid.Item>
                    <RenderConditional condition={formFop.openSelectTipoAporte}>
                        <>
                            <RenderConditional
                                condition={formFop.openSelectComAporte}
                            >
                                <DS.Grid.Item xs={1 / 2}>
                                    <Field
                                        name="valorAporteInicial"
                                        label={Enum.BeneficioBasico.ValorAporteInicial}
                                        component={DS.TextField}
                                        value={values.valorAporteInicial}
                                        maxLength={16}
                                        error={errors.valorAporteInicial}
                                        errorMessage={errors.valorAporteInicial}
                                        onChange={({
                                            target: { value },
                                        }: React.ChangeEvent<HTMLInputElement>) => {
                                            setFieldValue(
                                                'valorAporteInicial',
                                                masks.currencyInput.mask(value),
                                            );
                                        }}
                                        onBlur={handleBlur}
                                    />
                                </DS.Grid.Item>
                            </RenderConditional>
                            <RenderConditional
                                condition={formFop.openSelectPortabilidade}
                            >
                                <DS.Grid.Item xs={1 / 2}>
                                    <Field
                                        name="valorPortabilidade"
                                        label={Enum.BeneficioBasico.ValorPortabilidade}
                                        component={DS.TextField}
                                        value={values.valorPortabilidade}
                                        error={errors.valorPortabilidade}
                                        maxLength={16}
                                        errorMessage={errors.valorPortabilidade}
                                        onChange={({
                                            target: { value },
                                        }: React.ChangeEvent<HTMLInputElement>) => {
                                            setFieldValue(
                                                'valorPortabilidade',
                                                masks.currencyInput.mask(value),
                                            );
                                        }}
                                        onBlur={handleBlur}
                                    />
                                </DS.Grid.Item>
                            </RenderConditional>
                        </>
                    </RenderConditional>
                </S.AccordionItem>
            </DS.Accordion>
        </div>
    )
}