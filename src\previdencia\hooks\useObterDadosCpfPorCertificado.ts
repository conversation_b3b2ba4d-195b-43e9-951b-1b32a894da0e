import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { AppContext } from 'main/contexts/AppContext';
import { ResponseDadosCertificadosPorCpf } from '../types/DadosCertificado';
import * as DadosCpfPorCertificadoApi from '../services/dadosCpfPorCertificado.api';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useObterDadosCpfPorCertificado = (): UseQueryResult<
  ResponseDadosCertificadosPorCpf[] | undefined
> => {
  const { toastError } = useToast();
  const { cliente } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-consultar-dados-cpf-por-certificado', cliente?.cpfCnpj],
    queryFn: () =>
      DadosCpfPorCertificadoApi.obterDadosCpfPorCertificado(cliente?.cpfCnpj),

    refetchOnWindowFocus: false,
    gcTime: reactQueryCacheDuration(),
    retry: false,
    onError: (erro: Error) => toastError(erro.message),
  });
};

export default useObterDadosCpfPorCertificado;
