import React from 'react';
import { ISelectProps, TSelectItem } from 'main/types/Forms/Select';
import { Select } from '@cvp/design-system/react';

const FormikSelect = <T,>({
  selected,
  options,
  label,
  disabled,
  error,
  errorMsg,
  onKeyPress,
  onChange,
  ...props
}: ISelectProps<T>): React.ReactElement => {
  const handleChange = (e: React.ChangeEvent<TSelectItem<T>>) => {
    const selectValue = e.target.value;

    onChange(selectValue);
  };

  return (
    <Select
      {...props}
      label={label}
      isDisabled={disabled}
      error={error}
      errorMessage={errorMsg}
      onKeyPress={onKeyPress}
      onChange={handleChange}
    >
      {options.map(option => (
        <Select.Item
          key={option.value}
          value={option.value}
          text={option.text}
          selected={option.value === selected}
        />
      ))}
    </Select>
  );
};

export default FormikSelect;
