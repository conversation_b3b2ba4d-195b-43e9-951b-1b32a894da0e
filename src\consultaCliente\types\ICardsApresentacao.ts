export enum EnumStatusCard {
  ativos = 'ativos',
  inativos = 'inativos',
}

export enum EnumStatusCardVida {
  ativo = 'ativo',
  emitido = 'emitido',
}

export enum EnumStatusCardProduto {
  Ativo = 'A',
  Suspenso = 'U',
  Transferencia = 'T',
  Beneficio = 'B',
  Cancelado = 'C',
}

export enum EnumFormaPagamento {
  CB = 'CB',
  FC = 'FC',
}

export interface IFormSearchClient {
  handleSearchSubmit: (document: string) => void;
}
