import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { IApiResponse } from 'main/services';
import * as AtualizarEnderecoApi from 'previdencia/features/DadosParticipante/services/atualizarEndereco.api';
import { AppContext } from 'main/contexts/AppContext';
import { RequestAlterarEndereco } from '../types/DadosParticipante';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useAtualizarEndereco = (
  request: RequestAlterarEndereco | undefined,
  onCancelar: () => void,
): UseQueryResult<IApiResponse<undefined> | undefined> => {
  const { toastError, toastSuccess } = useToast();
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-atualizar-endereco', cpfCnpj],
    queryFn: () =>
      AtualizarEnderecoApi.atualizarEndereco(cpfCnpj, numCertificado, request),
    staleTime: reactQueryCacheDuration(),
    retry: false,
    enabled: false,
    onError: (erro: Error) => toastError(erro.message),
    onSuccess: data => {
      if (data?.dados?.mensagens) {
        onCancelar();
        toastSuccess(String(data.dados.mensagens[0].descricao));
      }
    },
  });
};

export default useAtualizarEndereco;
