import { TableColumn } from "react-data-table-component";
import { IAgenciaDPS } from "dps/features/pendentesDps/types/IBuscarBuscarAgenciaDPSResponse";
import { IBuscarClientesDPS } from "dps/features/pendentesDps/types/IBuscarClientesDPSResponse";

export type DadosTabelaPainelDPS = (IAgenciaDPS | IBuscarClientesDPS)[];
export interface ITabelaPainelDPS {
  dados: DadosTabelaPainelDPS;
  colunas: TableColumn<IBuscarClientesDPS | IAgenciaDPS>[];
  loading: boolean;
  agencia: boolean;
}
