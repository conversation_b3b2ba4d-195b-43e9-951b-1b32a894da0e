import { IArquivoDownload } from 'extranet/types/InterfacesFop63/IArquivoDownload';
import { IResponseObterListaFopsAtivos, TListaFopsProps } from 'main/features/Administracao/types/IFops';
import LinkedValue from 'main/features/Validation/types/LinkedValue';
import ValidationFunction from 'main/features/Validation/types/ValidationFunction';
import { IHandleReponseResult } from 'main/types/HandleResponseApi/IHandleReponseResult';

export interface IFormFop {
  openModalFormulario: boolean;
  textOutraFormaPagamento: string;
  textOutraFormaPgEmpresa: string;
  textOutraFormaPagamentoEmpresa: string;
  textOutraFormaPagamentoEmpresaCuidadoExtra: string;
  textOutraFormaPagamentoFuncionario: string;
  textOutraFormaPagamentoFuncionarioCuidadoExtra: string;
  textOutraFormaPagamentoCuidadoExtra: string;
  textOutraRegraLiberacaoDaReserva: string;
  textInformacoesComplementares: string;
  openSelectTipoBenficio: boolean;
  openSelectReversivel: boolean;
  openSelectValoresParticipantesCuidado?: boolean;
  openSelectValorContribuicao: boolean;
  openSelectValorContribuicaoCuidadoExtra: boolean;
  openSelectValorFixo: boolean;
  openSelectValorFixoCuidadoExtra: boolean;
  openSelectValorFixoEmpresa: boolean;
  openSelectValorFixoEmpresaCuidadoExtra: boolean;
  openSelectValorFixoFuncionario: boolean;
  openSelectValorFixoFuncionarioCuidadoExtra: boolean;
  openSelectValorPercentual: boolean;
  openSelectValorPercentualCuidadoExtra: boolean;
  openSelectValorPercentualEmpresa: boolean;
  openSelectValorPercentualEmpresaCuidadoExtra: boolean;
  openSelectValorPercentualFuncionario: boolean;
  openSelectValorPercentualFuncionarioCuidadoExtra: boolean;
  openSelectPlanoInstituido: boolean;
  openSelectOutraFormaPagamento: boolean;
  openSelectPlanoInstituidoCuidadoExtra: boolean;
  openSelectOutraFormaPagamentoCuidadoExtra: boolean;
  openSelectPrazoBenficio: boolean;
  openSelectValoresParticipantes: boolean;
  openSelectPeculio: boolean;
  openSelectPensao: boolean;
  openSelectFormaPagamentoCuidado: boolean;
  openSelectOutraFormaPagamentoEmpresa: boolean;
  openSelectOutraFormaPagamentoEmpresaCuidadoExtra: boolean;
  openSelectOutraFormaPagamentoFuncionario: boolean;
  openSelectOutraFormaPagamentoFuncionarioCuidadoExtra: boolean;
  openSelectTipoModalidade: boolean;
  openSelectLiberacaoReserva: boolean;
  openSelectSugestaoLiberacao: boolean;
  openSelectOutraFormaLiberacao: boolean;
  openSelectTipoModalidadePGBL: boolean;
  openSelectTipoModalidadeVGBL: boolean;
  openSelectTipoAporte: boolean;
  openSelectPortabilidade: boolean;
  openSelectComAporte: boolean;
  openSelectIdadeAposentadoria: boolean;
  openSelectPrazoContribuicao: boolean;
  openSelectAporteUnico: boolean;
  openSelectDiaVencimento: boolean;
  openSelectDebitoEmConta: boolean;
  openSelectFormaPagamentoRegra: boolean;
  openSelectRegraPagamentoCusteio: boolean;
  openFieldRecursoInstituidora: boolean;
  selectRegraCalculo: (valor: string) => void;
  selectFormaPagamento: (valor: string) => void;
  selectFormaPagamentoCuidado: (valor: string) => void;
  selectBeneficio: (valor: string) => void;
  selectValoresParticipantes: (valor: string) => void;
  selectValoresParticipantesCuidado: (valor: string) => void;
  selectCuidadoExtra: (valor: string) => void;
  selectValorContribuicao: (valor: string) => void;
  selectValorContribuicaoCuidadoExtra: (valor: string) => void;
  selectValorContribuicaoEmpresa: (valor: string) => void;
  selectValorContribuicaoEmpresaCuidadoExtra: (valor: string) => void;
  selectValorContribuicaoFuncionario: (valor: string) => void;
  selectValorContribuicaoFuncionarioCuidadoExtra: (valor: string) => void;
  selectFormaPagamentoFop63: (valor: string) => void;
  selecLiberacaoReserva: (valor: string) => void;
  selectRegraAporte: (valor: string) => void;
  selectTipoConcessao: (valor: string) => void;
  selecTipoPagamentoFatura: (valor: string) => void;
  selectDadosCobranca: (valor: string) => void;
  selectFormaPagamentoRegra: (valor: string) => void;
  fecharModal: () => void;
  openModal: () => void;
  setselectFormaPagamentoCusteio: (FormaCusteioModalidadePlano: string) => void;
  openFieldInstituidoraValor: (penalidade: string) => void;
  arquivoAnexoFop: LinkedValue<FileList>;
  arquivoAnexoFop63: LinkedValue<FileList>;
  loadingDownload: boolean;
  baixarArquivoFop: (
    codigoIdentificadorUnico?: string,
    nomeArquivo?: string,
  ) => Promise<React.ReactText>;
  regraFiles: ValidationFunction<FileList>[];
  loadingDownloadArquivo: boolean;
  setLoadingDownloadArquivo: React.Dispatch<React.SetStateAction<boolean>>;
  loadingFops: boolean;
  responseFops:
    | IHandleReponseResult<IResponseObterListaFopsAtivos[]>
    | undefined;
  setTextInformacoesComplementares: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamentoEmpresa: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPgEmpresa: React.Dispatch<React.SetStateAction<string>>;
  setTextOutraFormaPagamentoCuidadoExtra: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraRegraLiberacaoDaReserva: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamentoFuncionarioCuidadoExtra: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamentoFuncionario: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamentoEmpresaCuidadoExtra: React.Dispatch<
    React.SetStateAction<string>
  >;
  setTextOutraFormaPagamento: React.Dispatch<React.SetStateAction<string>>;
  toastSuccess: (
    message?: string,
    onClose?: (props: any) => void,
  ) => React.ReactText;
  obterArquivoFOP: (
    codigoIdentificadorUnico: string,
    nomeArquivo: string,
  ) => Promise<IArquivoDownload | undefined>;

  openMotivo?: IResponseObterListaFopsAtivos;

  handleFopAtivo: (value?: IResponseObterListaFopsAtivos) => void;

  handleOpenMotivo: (value?: IResponseObterListaFopsAtivos) => void;

  selectRegraCalculoFOP62: (valor: string) => void;

  listarFopsProps: Omit<TListaFopsProps, 'tipoFop'>;

}
