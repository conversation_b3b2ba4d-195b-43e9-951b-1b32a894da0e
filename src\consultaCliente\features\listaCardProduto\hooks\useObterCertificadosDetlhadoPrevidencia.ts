import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { useQuery } from '@tanstack/react-query';
import { obterCertificadoDetalhe } from '../service/consultarProdutosApi';

const useObterCertificadosDetlhadoPrevidencia = (codCliente?: string) => {
  return useQuery({
    queryKey: ['detalhes-previdencia', codCliente],
    queryFn: () => obterCertificadoDetalhe(codCliente),
    staleTime: reactQueryCacheDuration(),
    retry: false,
  });
};

export default useObterCertificadosDetlhadoPrevidencia;
