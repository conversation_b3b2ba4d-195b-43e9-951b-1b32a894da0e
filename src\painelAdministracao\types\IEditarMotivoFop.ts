import { FormikProps } from 'formik';
import { IEditarFopProps } from './IEditarFop';
import { IResponseObterListaFopsAtivos } from 'main/features/Administracao/types/IFops';

export interface IFormikValuesEditarMotivoFop {
  motivoSolicitacaoAtivo: boolean;
  descricaoMotivoSolicitacao: string;
}

export interface IFormEditarMotivoFopProps {
  formik: FormikProps<IFormikValuesEditarMotivoFop>;
  handleSubmit: () => Promise<void>;
}

export interface IModalEditarMotivoProps extends IEditarFopProps {
  onClose: () => void;
  formik: FormikProps<IFormikValuesEditarMotivoFop>;
  loading: boolean;
  handleAtualizarMotivo: () => void;
  handleChangeMotivoAtivo: (value: boolean) => void;
  handleDescricao: (value: string) => void;
}

export interface IUseEditarMotivoFop {
  (): {
    formikMotivo: FormikProps<IFormikValuesEditarMotivoFop>;
    handleChangeMotivoAtivo: (value: boolean) => void;
    handleDescricao: (value: string) => void;
  };
}

export interface IUseHandleAtualizarFopMotivoProps {
  (
    formikMotivo: FormikProps<IFormikValuesEditarMotivoFop>,
    fopEditar: IResponseObterListaFopsAtivos | undefined,
    handleFecharModalMotivo: () => void,
  ): {
    loadingMotivoFop: boolean;
    atualizarFopMotivo: () => void;
  };
}
