import { useNavigate } from 'react-router-dom';
import { useFormFops } from './useFormFops';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import { FOP_PREVIDENCIA, FOPS } from 'extranet/features/fops/constants/consts';
import {
  IResponseObterListaFopsAtivos,
  IUseFormFopsPrevidencia,
} from 'main/features/Administracao/types/IFops';

export const useFormFopsPrevidencia = (): IUseFormFopsPrevidencia => {
  const { openMotivo, handleOpenMotivo, loadingFops, listarFopsProps } =
    useFormFops();

  const navigate = useNavigate();

  const fop64 = tryGetValueOrDefault(
    [listarFopsProps.dataToList],
    [] as IResponseObterListaFopsAtivos[],
  ).filter(item => item.codigo === FOPS.CODIGOS.FOP_64);

  const goToFOP63 = (): void => {
    navigate(FOP_PREVIDENCIA.EXTRANET_FOP_063, {
      state: {
        from: FOPS.LISTA,
        dataLocation: fop64,
      },
    });
  };

  const goToFOP62 = (): void => {
    navigate(FOP_PREVIDENCIA.EXTRANET_FOP_062, {
      state: {
        from: FOPS.LISTA,
        dataLocation: fop64,
      },
    });
  };

  return {
    openMotivo,
    handleOpenMotivo,
    loadingFops,
    goToFOP63,
    goToFOP62,
    listarFopsProps,
  };
};
