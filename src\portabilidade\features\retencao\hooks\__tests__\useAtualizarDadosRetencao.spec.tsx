import { waitFor } from '@testing-library/react';
import { act, renderHook } from '@testing-library/react-hooks';
import faker from 'faker';
import { api } from 'main/services';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { AtualizacaoPortabilidade } from '../../types/AtualizacaoPortabilidade';
import { useAtualizarDadosRetencao } from '../useAtualizarDadosRetencao';

// Mock do getAuthData
vi.mock('../../../../../main/features/Auth/utils/auth', () => ({
  default: () => ({
    user: {
      marcadorControle: 'TEST_MARCADOR_CONTROLE',
    },
  }),
}));

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

afterEach(() => {
  vi.clearAllMocks();
});

describe('useAtualizarDadosRetencao.spec', () => {
  const queryClient = new QueryClient();

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ComponentesPosVendaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: api,
            authMode: 'DEFAULT_GI',
            usernameKey: '@portal-eco:nomeAcesso',
            storageMode: 'LOCAL',
            operationPath: 'PortalEconomiario/',
          }}
        >
          {children}
        </ApiGatewayCvpProvider>
      </ComponentesPosVendaProvider>
    </QueryClientProvider>
  );

  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

  it('deve atualizar os dados com sucesso', async () => {
    const idPortabilidade = faker.datatype.uuid();
    const payload: AtualizacaoPortabilidade = {
      numPortabilidade: idPortabilidade,
      acao: '6',
      dadosCliente: {},
    };

    const mockResponse = {
      sucessoBFF: true,
      entidade: null,
      mensagens: [{ descricao: faker.lorem.lines() }],
    };

    // Mock do useApiGatewayCvpInvoker
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi.fn().mockResolvedValue(mockResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });
    const { result } = renderHook(() => useAtualizarDadosRetencao(), {
      wrapper,
    });

    let successOperation = false;
    await act(async () => {
      successOperation = await result.current.atualizarDados(payload);
    });

    await waitFor(() => {
      expect(
        mockUseApiGatewayCvpInvoker().invocarApiGatewayCvpComToken,
      ).toBeCalled();
      expect(successOperation).toBeTruthy();
    });
  });

  it('deve atualizar os dados com erro', async () => {
    const idPortabilidade = faker.datatype.uuid();
    const payload: AtualizacaoPortabilidade = {
      numPortabilidade: idPortabilidade,
      acao: '7',
      dadosCliente: {},
    };

    const mockErrorResponse = {
      sucessoBFF: false,
      entidade: null,
      mensagens: [{ descricao: faker.lorem.lines() }],
    };

    // Mock do useApiGatewayCvpInvoker com erro
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockErrorResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi
        .fn()
        .mockResolvedValue(mockErrorResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    const { result } = renderHook(() => useAtualizarDadosRetencao(), {
      wrapper,
    });

    let successOperation = false;
    await act(async () => {
      successOperation = await result.current.atualizarDados(payload);
    });

    await waitFor(() => {
      expect(
        mockUseApiGatewayCvpInvoker().invocarApiGatewayCvpComToken,
      ).toBeCalled();
      expect(successOperation).toBeFalsy();
    });
  });
});
