import javascriptObfuscator from 'javascript-obfuscator';
import fs from 'fs';
import path from 'path';



const { obfuscate } = javascriptObfuscator;

const buildDir = 'build'; 
const fileTypes = ['.js', '.mjs', '.cjs'];

function walk(dir, callback) {
  fs.readdirSync(dir).forEach(f => {
    const filePath = path.join(dir, f);
    const stat = fs.statSync(filePath);
    if (stat.isDirectory()) {
      walk(filePath, callback);
    } else {
      callback(filePath);
    }
  });
}

walk(buildDir, filePath => {
  if (fileTypes.includes(path.extname(filePath))) {
    const code = fs.readFileSync(filePath, 'utf-8');
    const obfuscated = obfuscate(code, {
      compact: true,
      controlFlowFlattening: true,
      deadCodeInjection: true,
      disableConsoleOutput: true,
      stringArray: false,
    });
    fs.writeFileSync(filePath, obfuscated.getObfuscatedCode());
    console.log(`Obfuscated: ${filePath}`);
  }
});
