import { TMotivoInitialValues } from 'main/features/Administracao/types/IFops';

export const ENDPOINT_FOP062 = {
  ExtranetFopPrevidencia: `/extranet/fop-previdencia`,
};

export const ENDPOINT_FOP063 = {
  ExtranetFopPrevidencia: ENDPOINT_FOP062.ExtranetFopPrevidencia,
};

export const FOP_PREVIDENCIA = {
  EXTRANET_FOP_062: '/extranet/fop-062',
  EXTRANET_FOP_063: '/extranet/fop-063',
  EXTRANET_FOP_223: '/extranet/fop-223',
};

export const FOPS = {
  VALOR_FIXO_CONTRIBUICAO: 'valorFixoContribuicao',
  VALOR_BASE_CONTRIBUICAO: 'valorBaseContribuicao',
  OUTRA_FORMA_CONTRIBUICAO: 'outraFormaContribuicao',
  LISTA: 'LISTA_FOPS',
  CODIGOS: {
    FOP_64: 64,
    FOP_63: 63,
    FOP_62: 62,
    FOP_59: 59,
  },
  INDICADOR_USO_ARQUIVO: {
    DOWNLOAD: 'D',
    TEMPLATE: 'E',
    ANEXO: 'A',
  },
};

export const FOP_TEXTS = {
  TITULOS: {
    PREVIDENCIA: 'Formulários (FOPs) de Previdência',
    PRESTAMISTA: 'Formulários (FOPs) de Prestamista',
    VIDA: 'Formulários (FOPs) de Vida',
  },
  MANUTENCAO: 'Mais utilizados - Manutenção',
  MANUTENCAO_SUB:
    'Estes processos já podem ser executados de forma digital no Serviços Online.',
  ADESAO: 'Adesão',
  SAIDA: 'Saída',
  PESSOA_JURIDICA: 'Pessoa Jurídica',
  FOP_062: {
    TITULO: 'FOP 062 07',
    SUB: '02/2019 - Solicitação de Estudo de Plano Prev Empresarial',
  },
  FOP_063: {
    TITULO: 'FOP 063 11',
    SUB: '02/2019 - Solicitação para Elaboração de Contrato de Plano Empresarial',
  },
  OUTROS: {
    DOCUMENTOS: 'Outros Documentos',
  },
  MOTIVO: {
    CARACTERES: {
      MIN: 'Mínimo 50 caracteres',
      MAX: 'Máximo 250 caracteres',
    },
    ATENCAO: 'Atenção!',
    CANCELAR: 'Cancelar',
    SEGUIR: 'Seguir',
    ERRO: 'Erro ao registrar motivo.',
  },
  DOWNLOADS: {
    PLANILHA_TESTE: 'Planilha_teste.html',
  },
};

export const FOP_VALORES = {
  MOTIVO: {
    CARACTERES: {
      MAX: 250,
      MIN: 50,
    },
  },
};

export const initialValues: TMotivoInitialValues = { descricaoMotivo: '' };
