import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from 'main/hooks/useToast';
import { IPecoFetchDataParams, IPecoParams } from 'main/types/IPecoParams';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import { useApiGatewayCvpInvoker } from '@cvp/componentes-posvenda';
import getAuthData from '../features/Auth/utils/auth';

// Interface para dados de autenticação
interface IDadosAutenticacao {
  marcadorControle?: string;
}

/**
 * * * @link https://azuredevops.caixavidaeprevidencia.intranet/CVP/CVP.Frontends/_wiki/wikis/CVP.FrontEnds.wiki/253/React-Hook-usePeco
 * * @description 2023-01-30 - Hook responsável por consumir APIs de forma flexível, suportando cache, invalidação de cache e carregamento automático.
 * * @description Antes de alterar, comunicar o autor ou os arquitetos
 *
 * @template T - Tipo do payload enviado na requisição.
 * @template U - Tipo esperado da resposta.
 *
 * @param {IPecoParams<T>} params - Parâmetros de configuração para o hook.
 * @param {Object} params.api - Configurações da API.
 * @param {string} params.api.operationPath - Caminho da operação da API.
 * @param {string} [params.api.pecoBasePath] - Base URL para a API. Se não fornecida, usa a configuração padrão.
 * @param {T} [params.payload] - Payload padrão enviado na requisição.
 * @param {boolean} [params.cache] - Indica se o cache deve ser habilitado.
 * @param {string} [params.cacheKey] - Chave única para o cache.
 * @param {number} [params.cacheTime] - Tempo de validade do cache em milissegundos.
 * @param {Object} [params.requestConfig] - Configurações adicionais para a requisição, como headers.
 * @param {boolean} [params.autoFetch] - Indica se a requisição deve ser disparada automaticamente ao inicializar o hook.
 * @param {Function} [params.handleResponse] - Função para manipular a resposta da API.
 *
 * @returns {Object} - Objeto contendo os estados e funções relacionadas à requisição:
 * @returns {boolean} loading - Indica se a requisição está em andamento.
 * @returns {IHandleReponseResult<U> | undefined} response - Resposta da requisição em formato tratado.
 * @returns {unknown} responseBinary - Resposta em formato bruto (binário), usada para arquivos.
 * @returns {Function} fetchData - Função para realizar requisições com suporte a payload dinâmico e cache.
 * @returns {Function} fetchDataBinary - Função para realizar requisições e obter resposta binária.
 * @returns {unknown} error - Objeto de erro, se houver falhas na requisição.
 * @returns {Function} setResponse - Função para atualizar manualmente a resposta.
 * @returns {Function} invalidateCache - Função para invalidar o cache de uma consulta específica.
 *
 * @example
 * const { fetchData, loading, response, error } = usePeco({
 *   api: { operationPath: '/endpoint' },
 *   payload: { id: 123 },
 *   cache: true,
 *   cacheKey: 'unique-key',
 * });
 *
 * useEffect(() => {
 *   fetchData({ dynamicParam: 'value' });
 * }, []);
 */
export function usePeco<T, U>(params: IPecoParams<T>) {
  const queryClient = useQueryClient();
  const { toastError } = useToast();
  const [error, setError] = useState<unknown>(null);

  // Obter dados de autenticação para o marcadorControle
  const {
    user: { marcadorControle },
  } = getAuthData();

  const addMarcadorControleToPayload = (dynamicPayload = {}) => {
    const isDynamicPayloadFormData = dynamicPayload instanceof FormData;
    const isPayloadFormData = params.payload instanceof FormData;

    if (isDynamicPayloadFormData) {
      (dynamicPayload as FormData).append('marcadorControle', marcadorControle);
      return dynamicPayload;
    }
    if (isPayloadFormData) {
      (params.payload as FormData).append('marcadorControle', marcadorControle);
      return params.payload;
    }
    return { ...params.payload, ...dynamicPayload, marcadorControle };
  };

  const initialPayload = params.payload
    ? addMarcadorControleToPayload({})
    : params.payload;

  const apiResult = useApiGatewayCvpInvoker<T, U>(params?.api?.operationPath, {
    hostName: params.api.pecoBasePath,
    data: initialPayload as T & IDadosAutenticacao,
    autoFetch: params?.autoFetch,
    cacheKey: params?.cacheKey,
    cache: params?.cache,
    cacheTime: params?.cacheTime,
    handleResponse: params?.handleResponse,
    requestConfig: params?.requestConfig,
  });

  const {
    loading,
    response,
    responseBinary,
    invocarApiGatewayCvpComToken: baseFetch,
    invocarApiGatewayCvpComRetornoBinary: baseFetchBinary,
    setResponse,
    invalidateCache: baseInvalidateCache,
  } = apiResult;

  const getReactQueryKey = (fetchDataParamsCacheKey?: string) => {
    if (params?.cacheKey) return [params.api.operationPath, params.cacheKey];
    if (fetchDataParamsCacheKey)
      return [params.api.operationPath, fetchDataParamsCacheKey];
    return [params.api.operationPath];
  };

  const fetchByReactQueryClient = async (
    finalPayload: any,
    fetchDataParams = {} as IPecoFetchDataParams,
  ) => {
    return queryClient.fetchQuery({
      queryKey: getReactQueryKey(fetchDataParams.cacheKey),
      queryFn: ({ signal }: { signal?: AbortSignal }) =>
        baseFetch({
          ...finalPayload,
          signal,
        }),
      staleTime: tryGetValueOrDefault(
        [params.cacheTime],
        fetchDataParams.cacheTime,
      ),
    });
  };

  const fetchData = async (
    dynamicPayload = {},
    fetchDataParams = {} as IPecoFetchDataParams,
  ) => {
    try {
      // Adicionar marcadorControle ao payload
      const finalPayload = addMarcadorControleToPayload(dynamicPayload);

      if (params.cache || fetchDataParams.cache) {
        return await fetchByReactQueryClient(finalPayload, fetchDataParams);
      }

      const result = await baseFetch(finalPayload as any);
      return result;
    } catch (requestError: unknown) {
      const reqError = requestError as Error;
      toastError(reqError?.message);
      setError(requestError);
    }
    return undefined;
  };

  const invalidateCache = async (queryKey?: string) => {
    return baseInvalidateCache(queryKey || getReactQueryKey()[0]);
  };

  return {
    loading,
    response,
    responseBinary,
    error,
    fetchData,
    baseFetchBinary,
    setResponse,
    invalidateCache,
  };
}
