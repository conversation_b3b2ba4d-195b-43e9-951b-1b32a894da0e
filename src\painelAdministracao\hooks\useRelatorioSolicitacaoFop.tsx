import { useFormik } from 'formik';
import {
  api,
  IApiResponsePaginado,
  IApiResponsePaginadoBFF,
} from 'main/services';
import {
  FILE_NAME_EXPORT,
  FILE_NAME_FOP,
  TAMANO_PAGINA_DEFAULT,
} from 'painelAdministracao/constants/constants';
import { IEntradaRelatorioSolicitacao } from 'painelAdministracao/types/IEntradaRelatorioSolicitacao';
import { IRelatorioSolicitacaoFop } from 'painelAdministracao/types/IRelatorioSolicitacaoFop';
import { useCallback, useEffect, useState } from 'react';
import { IRelatorioSolicitaFopRetorno } from '../types/IRelatorioSolicitaFopRetorno';
import { PECOS_PAINEL_ADM } from 'painelAdministracao/config/endpoints';
import useFieldLink from 'main/hooks/useFieldLink';
import { filterOptionsFactory } from 'painelAdministracao/factories/filterOptionsFactory';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import { ICalendarDateProps } from 'painelAdministracao/types/ICalendarDateProps';
import getAuthData from 'main/features/Auth/utils/auth';
import { convertToCSV, downloadCSV } from 'painelAdministracao/utils/utils';

const useRelatorioSolicitacaoFop = (): IRelatorioSolicitaFopRetorno => {
  const [response, setResponse] =
    useState<IApiResponsePaginadoBFF<IRelatorioSolicitacaoFop[]>>();
  const [totalLinhas, setTotalLinhas] = useState(0);
  const [paginaAtual, setPaginaAtual] = useState(1);
  const [porPagina, setPorPagina] = useState(TAMANO_PAGINA_DEFAULT);
  const [loading, setLoading] = useState(true);
  const [dateLink] = useFieldLink<ICalendarDateProps>({
    dateEnd: null,
    dateStart: null,
  });

  const { user, sessionId } = getAuthData();

  const fetchData = async (
    solicitacaoMotivo: IEntradaRelatorioSolicitacao,
  ): Promise<IApiResponsePaginado<IRelatorioSolicitacaoFop[]>> => {
    return await api
      .post<
        IApiResponsePaginado<IRelatorioSolicitacaoFop[]>
      >(`/PortalEconomiario/${PECOS_PAINEL_ADM.RelatorioSolicitacaoFopPaginado}`, solicitacaoMotivo)
      .then(retorno => retorno.data);
  };
  const fetchMotivos = async (page: number) => {
    const { dados } = await fetchData({
      numeroPagina: page,
      tamanhoPagina: porPagina,
      retornarTotalItens: true,
      filtroGeral: formik.values.filtroGeral,
      codigoFop: formik.values.codigoFop,
      dataInicio: dateLink.get().value.dateStart,
      dataFinal: dateLink.get().value.dateEnd,
    });
    setPaginaAtual(page);
    setResponse(dados);
    setLoading(false);
    setTotalLinhas(dados?.paginacao?.totalItens ?? 0);
  };

  const mudarLinhasPorPagina = useCallback(
    async (numeroPorPagina: number, pagina: number) => {
      await fetchData({
        numeroPagina: pagina,
        tamanhoPagina: numeroPorPagina,
        retornarTotalItens: true,
        filtroGeral: formik.values.filtroGeral,
        codigoFop: formik.values.codigoFop,
        dataInicio: dateLink.get().value.dateStart,
        dataFinal: dateLink.get().value.dateEnd,
      });
      setPorPagina(numeroPorPagina);
    },
    [],
  );

  const mudarPagina = useCallback((paginaAtual: number) => {
    fetchMotivos(paginaAtual);
  }, []);

  const formik = useFormik<{
    filtroGeral: string;
    codigoFop?: number;
    dataInicio: Date | null;
    dataFinal: Date | null;
  }>({
    initialValues: {
      filtroGeral: '',
      codigoFop: undefined,
      dataInicio: null,
      dataFinal: null,
    },
    onSubmit: async () => {
      setLoading(true);
      fetchMotivos(paginaAtual);
    },
  });

  const [searchFilterTypeSelected, setSearchFilterTypeSelected] =
    useState<string>();

  const [selectFilter] = useFieldLink('');

  const handleSearchFilterSelected = (): void => {
    const filterSelectedValue = selectFilter.get().value;
    const filterTypeSelectedValue = filterOptionsFactory?.find(
      opt => opt.key === filterSelectedValue,
    );

    setSearchFilterTypeSelected(
      tryGetValueOrDefault([filterTypeSelectedValue?.key], ''),
    );
  };

  const clearForm = (): void => {
    formik.resetForm();
    dateLink.set({
      value: { dateStart: null, dateEnd: null },
      isValid: true,
      errorMsg: '',
    });
    selectFilter.set({
      value: '',
      isValid: true,
      errorMsg: '',
    });
  };

  const handleDownload = (): void => {
    const csv = convertToCSV(response, user, sessionId, FILE_NAME_EXPORT);

    downloadCSV(csv, FILE_NAME_FOP);
  };

  useEffect(() => {
    handleSearchFilterSelected();
  }, [selectFilter]);

  return {
    response,
    loading,
    fetchData,
    totalLinhas,
    mudarPagina,
    mudarLinhasPorPagina,
    fetchMotivos,
    formik,
    selectFilter,
    searchFilterTypeSelected,
    dateLink,
    clearForm,
    handleDownload,
  };
};

export default useRelatorioSolicitacaoFop;
