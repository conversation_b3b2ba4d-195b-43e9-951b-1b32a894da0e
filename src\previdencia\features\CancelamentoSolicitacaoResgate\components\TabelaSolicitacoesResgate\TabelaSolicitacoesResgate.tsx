import React from 'react';
import Table from 'main/components/Table';
import { Button, Display } from '@cvp/design-system/react';
import { formatarValorPadraoBrasileiro } from 'main/utils/money';
import { formatarData } from 'main/utils';
import { PrevRedirectByStatus } from 'previdencia/features/HistoricoSolicitacoes/types/enum';
import { useNavigate } from 'react-router-dom';
import { StatusResgate } from 'previdencia/types/StatusResgate';
import RenderConditional from 'main/components/RenderConditional';
import { COLUNAS_SOLICITACAO_RESGATE } from 'previdencia/features/CancelamentoSolicitacaoResgate/constants/constants';
import { TabelaSolicitacoesResgateProps } from 'previdencia/features/CancelamentoPrevidencia/types/TabelaSolitacoes';
import { TSolicitacaoResgateDataColumn } from '../../types/ResponseCancelamentoSolicitacaoResgate';
import { tryGetValueOrDefault } from 'main/utils/conditional';

const RenderBotaoReassinar: React.FC<{
  numeroResgate: string;
  statusResgate: string;
}> = ({ numeroResgate, statusResgate }) => {
  const navigate = useNavigate();

  return (
    <Button
      variant="primary"
      onClick={() => {
        navigate(PrevRedirectByStatus.RESGATE, {
          state: {
            from: 'HISTORICO_CANCELAMENTO_RESGATE',
            data: { idRequisicao: numeroResgate, statusResgate },
          },
        });
      }}
    >
      Assinar
    </Button>
  );
};

const TabelaSolicitacoesResgate: React.FC<TabelaSolicitacoesResgateProps> = ({
  data,
  onClick,
  loading,
}) => {
  const tableData: TSolicitacaoResgateDataColumn[] = tryGetValueOrDefault(
    [data],
    [],
  ).map(resgate => ({
    ...resgate,
    dataSolicitacao: formatarData(resgate.dataSolicitacao),
    valor: formatarValorPadraoBrasileiro(resgate.valorResgate),
    status: resgate.descricaoStaResgate,
    acao: (
      <Display>
        <RenderConditional
          condition={resgate.staRegaste === StatusResgate.AGUARDANDO_ASSINATURA}
        >
          <RenderBotaoReassinar
            numeroResgate={resgate.numeroResgate}
            statusResgate={resgate.staRegaste}
          />
        </RenderConditional>
        <RenderConditional condition={resgate.indicacaoCancelavel === 'S'}>
          <Button
            type="submit"
            variant="secondary"
            onClick={() => {
              onClick(resgate.numeroResgate);
            }}
            loading={loading}
          >
            Cancelar
          </Button>
        </RenderConditional>
      </Display>
    ),
  }));

  return (
    <Table
      noHeader
      responsive
      data={tableData}
      columns={COLUNAS_SOLICITACAO_RESGATE}
      noDataComponent="Não há dados para exibir."
      pagination
      paginationPerPage={10}
      paginationComponentOptions={{
        rowsPerPageText: 'Items por página',
        rangeSeparatorText: 'de',
      }}
    />
  );
};

export default TabelaSolicitacoesResgate;
