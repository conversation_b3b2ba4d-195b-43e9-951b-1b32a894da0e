import { STORAGE_BASE_KEY } from 'main/constants/app.config';
import { getTernaryResult } from 'main/utils/conditional';

export function setSessionItem(key: string, data: unknown) {
  window.sessionStorage.setItem(
    `${STORAGE_BASE_KEY}:${key}`,
    JSON.stringify(data),
  );
}

export function getSessionItem<T>(key: string): T {
  const itemRecovered = window.sessionStorage.getItem(
    `${STORAGE_BASE_KEY}:${key}`,
  );
  if (!itemRecovered) return {} as T;

  try {
    return JSON.parse(itemRecovered) as T;
  } catch {
    return itemRecovered as unknown as T;
  }
}

export const removeSessionItem = (key: string) => {
  window.sessionStorage.removeItem(`${STORAGE_BASE_KEY}:${key}`);
};

export function setLocalItem(key: string, data: unknown) {
  const dataType: string = String(typeof data);
  window.localStorage.setItem(
    `${STORAGE_BASE_KEY}:${key}`,
    getTernaryResult(dataType === 'string', String(data), JSON.stringify(data)),
  );
}

export function getLocalItem<T>(key: string): T | null {
  const itemRecovered = window.localStorage.getItem(
    `${STORAGE_BASE_KEY}:${key}`,
  );
  if (!itemRecovered) return null;

  try {
    return JSON.parse(itemRecovered) as T;
  } catch {
    return itemRecovered as unknown as T;
  }
}

export const removeLocalItem = (key: string) => {
  window.localStorage.removeItem(`${STORAGE_BASE_KEY}:${key}`);
};
