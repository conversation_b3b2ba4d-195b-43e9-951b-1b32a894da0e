import { ENDPOINT_FOP063 } from 'extranet/features/fops/constants/consts';
import { useFormFops } from 'extranet/hooks/useFormFops';
import { enviarFormulario } from 'extranet/services/obterEndpointArquivo';
import * as Enum from 'extranet/types/enum';
import { IInitialValuesType } from 'extranet/types/InterfacesFop63/IInitialValues';
import { ParamsLocationData } from 'extranet/types/IResponseObterListaFopsAtivos';
import getAuthData from 'main/features/Auth/utils/auth';
import { useLocationPeco } from 'main/hooks/useLocationPeco';
import { useToast } from 'main/hooks/useToast';
import { api } from 'main/services';
import { tryGetValueOrDefault } from 'main/utils/conditional';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

export const useEnviarAberturaFop63 = () => {
  const formFop = useFormFops();
  const location = useLocationPeco<ParamsLocationData>();
  const [loading, setLoading] = useState<boolean>(false);
  const { dataLocation } = location.state;
  const { toastError, toastSuccess } = useToast();
  const navigate = useNavigate();
  const enviarFop = async (
    codigoFop: number,
    numeroVersaoFop: number,
    nomeSolicitante: string,
    emailSolicitante: string,
    metadados: string,
  ) => {
    const metadadosSolicitacao = JSON.stringify(metadados);
    const data = await enviarFormulario(
      codigoFop,
      numeroVersaoFop,
      nomeSolicitante,
      emailSolicitante,
      metadadosSolicitacao,
    );
    setLoading(true);
    if (data?.dados?.mensagens) {
      if (data?.dados?.sucesso) {
        toastSuccess((data?.dados?.mensagens[0] || []).descricao);
        setTimeout(() => {
          navigate(ENDPOINT_FOP063.ExtranetFopPrevidencia);
        }, 0);
      }
      setLoading(false);
    } else {
      toastError('Erro ao registrar FOP.');
      setLoading(false);
    }
  };

  const { user } = getAuthData();
  const handleFormSubmit = async (values: IInitialValuesType) => {
    const arquivo = formFop.arquivoAnexoFop63.get().value;
    const data = new FormData();
    const file = arquivo[0];
    data.append('Arquivo', file);
    data.append('codigoFop', '63');
    data.append('numeroVersaoFop', '11');

    const tiposPermitidos = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
    ];
    if (file && !tiposPermitidos.includes(file.type)) {
      formFop.arquivoAnexoFop63.set({
        ...formFop.arquivoAnexoFop63.get(),
        isValid: false,
        errorMsg:
          'Tipo de arquivo inválido. Envie um arquivo Excel (.xls ou .xlsx).',
      });
      return;
    }

    const tamanhoMaximo = 3 * 1024 * 1024;
    if (file.size > tamanhoMaximo) {
      console.log('error: arquivo excede 3MB');
      formFop.arquivoAnexoFop63.set({
        ...formFop.arquivoAnexoFop63.get(),
        isValid: false,
        errorMsg: 'O tamanho do arquivo não pode exceder 3MB.',
      });
      return;
    }

    const response: any = await api.post(
      '/PortalEconomiario/PECO_EnviarArquivoSolicitacaoFOP',
      data,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );

    const updatedValoresPlano = values.valoresPlano.map(item => ({ ...item }));
    if (updatedValoresPlano.length) {
      const lastIdx = updatedValoresPlano.length - 1;
      if (!updatedValoresPlano[lastIdx].tempoMeses) {
        const prevTempo = updatedValoresPlano[lastIdx - 1]?.tempoMeses ?? '';
        const nums = prevTempo.match(/\d+/g);
        const lastNum = nums ? Number(nums.pop()) : null;
        if (lastNum !== null) {
          updatedValoresPlano[lastIdx].tempoMeses =
            `A partir de ${lastNum + 1} meses`;
        }
      }
    }

    const camposValoresPlano = updatedValoresPlano.reduce<
      Record<string, string>
    >((acc, item, idx) => {
      const i = idx + 1;
      acc[`valoresPlano-tempoMeses-${i}`] = item.tempoMeses;
      acc[`valoresPlano-porcentagemReversao-${i}`] = item.porcentagemReversao;
      return acc;
    }, {});

    const camposTabelaCuidado = values.tabelaCuidadoExtra.reduce<
      Record<string, string>
    >((acc, item, idx) => {
      const i = idx + 1;
      Object.entries(item).forEach(([key, val]) => {
        acc[`tabelaCuidadoExtra-${key}-${i}`] = val;
      });
      return acc;
    }, {});

    const camposValoresContribuicao = values.valoresContribuicao.reduce<
      Record<string, string>
    >((acc, item, idx) => {
      const i = idx + 1;
      Object.entries(item).forEach(([key, val]) => {
        acc[`valoresContribuicao-${key}-${i}`] = val;
      });
      return acc;
    }, {});

    const solicitacao: any = {
      ...values,
      ...camposValoresPlano,
      ...camposTabelaCuidado,
      ...camposValoresContribuicao,
      numeroDaAgencia: `${values.numeroDaAgencia}`,
      valoresPlano: JSON.stringify(values.valoresPlano),
      tabelaCuidadoExtra: JSON.stringify(values.tabelaCuidadoExtra),
      valoresContribuicao: JSON.stringify(values.valoresContribuicao),
      idunicosolicitacaofop: tryGetValueOrDefault(
        [response?.data?.dados?.entidade?.idUnicoSolicitacaoFop],
        '',
      ),
      nomeEmpresa: values.nomeDaEmpresa,
      cnpj: values.cnpjEmpresa,
      faturamento:
        Enum.SELECT_OPTIONS_FATURAMENTO.find(
          faturamento => faturamento.key === values.faturamento,
        )?.value || '',
      uf: Enum.SELECT_OPTION_UF.find(uf => uf.key === values.uf)?.value || '',
      agenciaFilial:
        Enum.SELECT_OPTIONS_FILIAL.find(
          filial => filial.key === values.agenciaFilial,
        )?.value || '',
      aporteInicial:
        Enum.SELECT_OPTIONS_REGRA_APORTE.find(
          aporte => aporte.key === values.aporteInicial,
        )?.value || '',
      formaDePagamento:
        Enum.SELECT_OPTIONS_PAGAMENTO.find(
          formaPagamento => formaPagamento.key === values.formaDePagamento,
        )?.value || '',
      linkSelectModalidade:
        Enum.SELECT_OPTIONS_MODALIDADE.find(
          modalidade => modalidade.key === values.linkSelectModalidade,
        )?.value || '',
      linkSelectModalidadePGBL:
        Enum.SELECT_OPTIONS_MODALIDADE_PGBL.find(
          modalidadepgbl =>
            modalidadepgbl.key === values.linkSelectModalidadePGBL,
        )?.value || '',
      linkSelectModalidadeVGBL:
        Enum.SELECT_OPTIONS_MODALIDADE_VGBL.find(
          modalidadeVgbl =>
            modalidadeVgbl.key === values.linkSelectModalidadeVGBL,
        )?.value || '',
      liberacaoDaReserva:
        Enum.SECT_OPTIONS_LIBERACAO_RESERVA.find(
          liberacao => liberacao.key === values.liberacaoDaReserva,
        )?.value || '',
      regraCalculo:
        Enum.SELECT_OPTIONS_REGRA.find(
          regra => regra.key === values.regraCalculo,
        )?.value || '',
      tipoBeneficioBasico:
        Enum.SELECT_OPTIONS_BENEFICIO.find(
          tipoBeneficio => tipoBeneficio.key === values.tipoBeneficioBasico,
        )?.value || '',
      prazoBeneficio:
        Enum.SELECT_OPTIONS_PRAZO_BENEFICIO.find(
          prazo => prazo.key === values.prazoBeneficio,
        )?.value || '',
      reversao:
        Enum.SELECT_OPTIONS_REVERSAO.find(
          reversao => reversao.key === values.reversao,
        )?.value || '',
      optionsValoresParticipante:
        Enum.SELECT_OPTIONS_VALORES_PARTICIPANTES.find(
          valoresParticipantes =>
            valoresParticipantes.key === values.optionsValoresParticipantes,
        )?.value,
      linkValorContribuicao:
        Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          valorContribuicao =>
            valorContribuicao.key === values.linkValorContribuicao,
        )?.value || '',
      linkValorContribuicaoEmpresa:
        Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          contribuicaoEmpresa =>
            contribuicaoEmpresa.key === values.linkValorContribuicaoEmpresa,
        )?.value || '',
      linkValorContribuicaoFuncionario:
        Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          contribuicaoFuncionario =>
            contribuicaoFuncionario.key ===
            values.linkValorContribuicaoFuncionario,
        )?.value || '',
      linkSelectCuidadoExtra:
        Enum.SELECT_OPTIONS_CUIDADO_EXTRA.find(
          cuidadoExtra => cuidadoExtra.key === values.linkSelectCuidadoExtra,
        )?.value || '',
      linkSelectRegraCuidadoExtraPensao:
        Enum.SELECT_OPTIONS_REGRA_CUIDADO_PENSAO.find(
          cuidadoExtraPensao =>
            cuidadoExtraPensao.key === values.linkSelectRegraCuidadoExtraPensao,
        )?.value || '',
      formaDePagamentoCuidado:
        Enum.SELECT_OPTIONS_PAGAMENTO.find(
          formaPagamentoCuidado =>
            formaPagamentoCuidado.key === values.formaDePagamentoCuidado,
        )?.value || '',
      linkSelectRegraCuidadoExtraPeculio:
        Enum.SELECT_OPTIONS_REGRA_CUIDADO_63.find(
          regraCuidado =>
            regraCuidado.key === values.linkSelectRegraCuidadoExtraPeculio,
        )?.value || '',
      linkValorContribuicaoCuidadoExtra:
        Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          ValorContribuicao =>
            ValorContribuicao.key === values.linkValorContribuicaoCuidadoExtra,
        )?.value || '',
      linkValorContribuicaoEmpresaCuidadoExtra:
        Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          ValorContribuicaoEmpresa =>
            ValorContribuicaoEmpresa.key ===
            values.linkValorContribuicaoEmpresaCuidadoExtra,
        )?.value || '',
      linkValorContribuicaoFuncionarioCuidadoExtra:
        Enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          ValorContribuicaoFuncionario =>
            ValorContribuicaoFuncionario.key ===
            values.linkValorContribuicaoFuncionarioCuidadoExtra,
        )?.value || '',
      linkSelectTipoFundo:
        Enum.SELECT_OPTIONS_TIPO_FUNDO.find(
          tipoFundo => tipoFundo.key === values.linkSelectTipoFundo,
        )?.value || '',
      linkSelectTipoConcessao:
        Enum.SELECT_OPTIONS_CONCESSAO_BENEFICIO.find(
          concessaoBeneficio =>
            concessaoBeneficio.key === values.linkSelectTipoConcessao,
        )?.value || '',
      linkSelectTipoPagamentoFatura:
        Enum.SELECT_OPTIONS_PAGAMENTO_FATURA.find(
          pagamentoFatura =>
            pagamentoFatura.key === values.linkSelectTipoPagamentoFatura,
        )?.value || '',
      linkSelectVencimentoFatura:
        Enum.SELECT_OPTIONS_VENCIMENTO_FATURA.find(
          vencimentoFatura =>
            vencimentoFatura.key === values.linkSelectVencimentoFatura,
        )?.value || '',
      linkSelectDadosCobranca:
        Enum.SELECT_OPTIONS_DADOS_COBRANCA.find(
          dadosCobranca => dadosCobranca.key === values.linkSelectDadosCobranca,
        )?.value || '',
      linkSelectDadosOperacao:
        Enum.SELECT_OPTIONS_OPERACAO.find(
          DadosOperacao => DadosOperacao.key === values.linkSelectDadosOperacao,
        )?.value || '',
      linkCusteioPagamento:
        Enum.SELECT_OPTION_CUSTEIO_MODALIDADE.find(
          custeiopagamento =>
            custeiopagamento.key === values.linkCusteioPagamento,
        )?.value || '',
      textOutraRegraLiberacaoDaReserva:
        formFop.textOutraRegraLiberacaoDaReserva,
      textOutraFormaPagamento: formFop.textOutraFormaPagamento,
      textOutraFormaPgEmpresa: formFop.textOutraFormaPgEmpresa,
      textOutraFormaPagamentoEmpresaCuidadoExtra:
        formFop.textOutraFormaPagamentoEmpresaCuidadoExtra,
      textOutraFormaPagamentoFuncionario:
        formFop.textOutraFormaPagamentoFuncionario,
      textOutraFormaPagamentoFuncionarioCuidadoExtra:
        formFop.textOutraFormaPagamentoFuncionarioCuidadoExtra,
      textOutraFormaPagamentoCuidadoExtra:
        formFop.textOutraFormaPagamentoCuidadoExtra,
      textInformacoesComplementares: values.textInformacoesComplementares,
    };

    const camposParaSinalizar = [
      'formaCusteioModalidadePlano',
      'tipoPagamentoContribuicao',
      'formaPagamentoRegraContratual',
      'perdaVinculo',
      'demisaoJustaCausa',
      'penalidades',
      'recursosInstituidora',
      'distribuicaoContaColetiva',
      'pagamentoContribuicao',
    ] as const;

    camposParaSinalizar.forEach(campo => {
      const valor = values[campo];
      if (valor) {
        const cleanValor = valor
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '');
        solicitacao[`${campo}-${cleanValor}`] = 'x';
        delete solicitacao[campo];
      }
    });

    delete solicitacao.valoresPlano;
    delete solicitacao.tabelaCuidadoExtra;
    delete solicitacao.valoresContribuicao;

    enviarFop(
      63,
      11,
      values.nomeCompletoResponsavel,
      values.emailResponsavel,
      solicitacao,
    );
  };

  return {
    user,
    handleFormSubmit,
    formFop,
    dataLocation,
    loading,
  };
};
