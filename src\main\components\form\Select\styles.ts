import styled from 'styled-components';
import { Select } from '@cvp/design-system/react';

export const SelectComponent = styled(Select)<{ isDisabled?: boolean }>(
  ({ theme: { color }, isDisabled }) => ({
    div: {
      background: isDisabled ? '#DBDCDE' : color.neutral['08'],
    },
  }),
);

export const ItemComponent = styled(Select.Item)(({ theme: { color } }) => ({
  color: color.neutral['03'],
  fontSize: '11px',
}));
