import {
  UNDO_COMMAND,
  REDO_COMMAND,
  FORMAT_TEXT_COMMAND,
  TextFormatType,
} from 'lexical';
import React from 'react';
import UndoIcon from 'assets/icons/arrow-counterclockwise.svg';
import Redo from 'assets/icons/arrow-clockwise.svg';
import Bold from 'assets/icons/type-bold.svg';
import Italic from 'assets/icons/type-italic.svg';
import Underline from 'assets/icons/type-underline.svg';

import { useToolbarPlugin } from 'painelAdministracao/hooks/useToolbarPlugin';
import { Divider } from 'main/components/Divider/Divider';
import { Button, Toolbar } from 'main/components/RichTextEditor/styles';
import { toolbarPluginConts } from 'main/constants/toolbarPluginConts';

export const ToolbarPlugin = (): React.ReactElement => {
  const { isBold, editor, isItalic, isUnderline } = useToolbarPlugin();

  return (
    <Toolbar>
      <Button onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}>
        <img src={UndoIcon} alt="Desfazer" />
      </Button>
      <Button onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}>
        <img src={Redo} alt="Refazer" />
      </Button>
      <Divider />
      <Button
        onClick={() =>
          editor.dispatchCommand(
            FORMAT_TEXT_COMMAND,
            toolbarPluginConts.BOLD as TextFormatType,
          )
        }
        active={isBold}
      >
        <img src={Bold} alt="Negrito" />
      </Button>
      <Button
        onClick={() =>
          editor.dispatchCommand(
            FORMAT_TEXT_COMMAND,
            toolbarPluginConts.ITALIC as TextFormatType,
          )
        }
        active={isItalic}
      >
        <img src={Italic} alt="Italico" />
      </Button>
      <Button
        onClick={() =>
          editor.dispatchCommand(
            FORMAT_TEXT_COMMAND,
            toolbarPluginConts.UNDERLINE as TextFormatType,
          )
        }
        active={isUnderline}
      >
        <img src={Underline} alt="Sublinhado" />
      </Button>
      <Divider />
    </Toolbar>
  );
};
