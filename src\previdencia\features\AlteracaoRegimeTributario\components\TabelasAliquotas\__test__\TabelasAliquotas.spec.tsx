import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { render, screen } from '@testing-library/react';

import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils';
import TabelasAliquotas from 'previdencia/features/AlteracaoRegimeTributario/components/TabelasAliquotas';
import * as CONSTS from 'previdencia/features/AlteracaoRegimeTributario/constants/constants';

describe('TabelasAliquotas', () => {
  const opcoesTributacaoIrrfDisponiveis = ['Opção 1', 'Opção 2'];
  const fundosAliquota = [
    {
      aliquota: '',
      fundo: '',
      valorDisponivel: '',
      valorIndisponivel: '',
      valorTotal: '',
    },
  ];

  const dadosTabela = {
    fundosAliquotaRegressivo: fundosAliquota,
    fundosAliquotaProgressivo: fundosAliquota,
  };

  const selecionarOpcaoRegimeTributarioMock = vi.fn();

  beforeEach(() => {
    selecionarOpcaoRegimeTributarioMock.mockClear();
  });
  const queryClient = new QueryClient();

  it('deve renderizar o SkeletonLoading quando os dados estão carregando', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <RenderPageWithThemeProviderAndRouter>
          <TabelasAliquotas
            opcoesTributacaoIrrfDisponiveis={opcoesTributacaoIrrfDisponiveis}
            loadingDadosAliquota
            opcaoRegimeTributario=""
            dadosTabela={dadosTabela as any}
            selecionarOpcaoRegimeTributario={
              selecionarOpcaoRegimeTributarioMock
            }
            tipoRegimeTributario={''}
          />
        </RenderPageWithThemeProviderAndRouter>
      </QueryClientProvider>,
    );

    const skeletons = screen.getAllByTestId('card-skeleton-block');
    expect(skeletons.length).toBeGreaterThan(0);

    skeletons.forEach(skeleton => {
      expect(skeleton).toBeInTheDocument();
    });
  });

  it('deve renderizar uma mensagem de erro se não houver dados disponíveis', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <RenderPageWithThemeProviderAndRouter>
          <TabelasAliquotas
            opcoesTributacaoIrrfDisponiveis={opcoesTributacaoIrrfDisponiveis}
            loadingDadosAliquota={false}
            opcaoRegimeTributario=""
            dadosTabela={undefined}
            selecionarOpcaoRegimeTributario={
              selecionarOpcaoRegimeTributarioMock
            }
            tipoRegimeTributario={''}
          />
        </RenderPageWithThemeProviderAndRouter>
      </QueryClientProvider>,
    );

    const alertaErro = screen.getByRole('alert');
    expect(alertaErro).toBeInTheDocument();
    expect(screen.getByText(CONSTS.NO_DATA_ALIQUOTA)).toBeInTheDocument();
  });
});
