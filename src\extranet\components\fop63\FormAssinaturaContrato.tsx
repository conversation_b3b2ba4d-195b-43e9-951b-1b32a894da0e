import * as DS from '@cvp/design-system/react';
import { IFormBase63 } from "extranet/types/InterfacesFop63/IFormBase63";
import { Field } from "formik";
import { TitleSection } from "main/styles/GlobalStyle";
import masks from "main/utils/masks";
import * as S from '../../../extranet/features/fops/pages/styles';
import * as Enum from '../../types/enum';

export const FormAssinaturaContrato: React.FC<IFormBase63> = ({
  setFieldValue,
  values,
  handleBlur,
  errors,
}) => {
  return (
    <div>
      <DS.Accordion open>
        <S.AccordionItem
          title={
            <TitleSection>
              {Enum.Titulos.AssinaturaContrato}
            </TitleSection>}
        >
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="nomeRepresentanteLegal"
              maxLength={50}
              label={Enum.AssinaturaContrato.NomeRepresentantelegal}
              component={DS.TextField}
              value={values.nomeRepresentanteLegal}
              error={errors.nomeRepresentanteLegal}
              errorMessage={errors.nomeRepresentanteLegal}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('nomeRepresentanteLegal', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="cpfRepresentanteLegal"
              label={Enum.AssinaturaContrato.CPFRepresentantelegal}
              component={DS.TextField}
              value={masks.cpf.mask(values.cpfRepresentanteLegal)}
              error={errors.cpfRepresentanteLegal}
              errorMessage={errors.cpfRepresentanteLegal}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('cpfRepresentanteLegal', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="emailRepresentanteLegal"
              label={Enum.AssinaturaContrato.EmailRepresentanteLegal}
              component={DS.TextField}
              maxLength={50}
              value={values.emailRepresentanteLegal}
              error={errors.emailRepresentanteLegal}
              errorMessage={errors.emailRepresentanteLegal}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('emailRepresentanteLegal', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="nomePrimeiraTestemunha"
              maxLength={50}
              label={Enum.AssinaturaContrato.NomeTestemunha}
              component={DS.TextField}
              value={values.nomePrimeiraTestemunha}
              error={errors.nomePrimeiraTestemunha}
              errorMessage={errors.nomePrimeiraTestemunha}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('nomePrimeiraTestemunha', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="cpfPrimeiraTestemunha"
              label={Enum.AssinaturaContrato.CPFTestemunha}
              component={DS.TextField}
              value={masks.cpf.mask(values.cpfPrimeiraTestemunha)}
              error={errors.cpfPrimeiraTestemunha}
              errorMessage={errors.cpfPrimeiraTestemunha}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('cpfPrimeiraTestemunha', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
          <DS.Grid.Item xs={1} lg={1 / 2}>
            <Field
              name="emailPrimeiraTestemunha"
              label={Enum.AssinaturaContrato.EmailTestemunha}
              component={DS.TextField}
              maxLength={50}
              value={values.emailPrimeiraTestemunha}
              error={errors.emailPrimeiraTestemunha}
              errorMessage={errors.emailPrimeiraTestemunha}
              onChange={({
                target: { value },
              }: React.ChangeEvent<HTMLInputElement>) => {
                setFieldValue('emailPrimeiraTestemunha', value);
              }}
              onBlur={handleBlur}
            />
          </DS.Grid.Item>
        </S.AccordionItem>
      </DS.Accordion>
    </div>
  )
}
