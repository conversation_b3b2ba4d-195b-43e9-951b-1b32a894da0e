import {
  FilterType,
  IFilterOption,
  ISearchTerms,
  ITagFilterOption,
} from 'main/types/TableFilters/IFilterOption';
import { IFilterableEntity } from '../IFilterableEntity';
import LinkedValue from 'main/features/Validation/types/LinkedValue';

export interface IUseTableFilter {
  initialData: IFilterableEntity[];
  filterOptions: IFilterOption[];
  filterTextPartial?: boolean;
  defaultFilter?: ISearchTerms;
  onSubmitCallback?: (searchTerms: ISearchTerms | undefined) => void;
}

export type SearchKey =
  | 'descricaoSegmento'
  | 'numeroProtocolo'
  | 'numeroSolicitacao';

export interface IUseTableFilterReturn {
  dataFiltered: IFilterableEntity[];
  selectFilter: LinkedValue<string>;
  searchText: LinkedValue<string>;
  searchFilterTypeSelected: FilterType;
  initialDate: Date | null;
  endDate?: Date | null;
  showClearFilters: boolean;
  tagsSelecteds: ITagFilterOption[];
  searchLabel: Partial<Record<SearchKey, string>>;
  setInitialDate: React.Dispatch<React.SetStateAction<Date | null>>;
  setEndDate: React.Dispatch<React.SetStateAction<Date | null | undefined>>;
  inputsAreValids: () => boolean;
  onSubmitSearch: () => void;
  clearFilters: () => void;
  handleTagsFilter: (option: ITagFilterOption) => void;
  setSearchFilterTypeSelected: React.Dispatch<React.SetStateAction<FilterType>>;
}
