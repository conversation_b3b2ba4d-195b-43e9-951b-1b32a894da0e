import styled from 'styled-components';
import { Breadcrumb } from '@cvp/design-system/react';
import { getTernaryResult } from 'main/utils/conditional';

export const BreadcrumbActive = styled(Breadcrumb.Item)<{ light?: boolean }>(
  ({ light, theme }) => ({
    color: getTernaryResult(
      !light,
      theme.color.neutral['03'],
      theme.color.neutral['06'],
    ),

    ':hover': {
      color: getTernaryResult(
        !light,
        theme.color.neutral['03'],
        theme.color.neutral['06'],
      ),
    },
  }),
);
