import styled from 'styled-components';
import ilustracaoBranco from 'assets/img/bgimage_branco.svg';
import ilustracaoAzul from 'assets/img/bgimage_azul.svg';
import IlustracaoConsulta from 'assets/img/Imagem_consulta.png';
import { getTernaryResult } from 'main/utils/conditional';

export const UnderHeaderLineStyle = styled.div`
  background-color: #afca0b;
  height: 7px;
`;

export const MiddleAppWrapper = styled.div<{ fundoAzul?: boolean }>(({ fundoAzul }) => ({
  backgroundColor: getTernaryResult(
    !fundoAzul,
    'rgb(245, 245, 245)',
    'rgb(18, 107, 207)',
  ),
  backgroundImage: `url("${getTernaryResult(
    !fundoAzul,
    ilustracaoAzul,
    ilustracaoBranco,
  )}")`,
  backgroundPosition: `calc(100% - 20px) calc(100% - 30px)`,
  backgroundRepeat: 'no-repeat',
  display: 'flex',
  flexGrow: 1,
}));

export const InitialImageWrapper = styled.div<{ consulta: boolean }>(
  ({ consulta, theme }) => ({
    backgroundImage: `url(${getTernaryResult(
      consulta,
      IlustracaoConsulta,
      'none',
    )})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'auto 100%',
    minHeight: '100%',
    paddingBottom: '75px',

    [theme.breakpoint.md()]: {
      paddingBottom: getTernaryResult(consulta, '0', '25px'),
    },
  }),
);
