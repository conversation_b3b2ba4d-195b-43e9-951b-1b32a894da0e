import { useQuery } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { obterValorImportanciaSegurada } from '../service/valorImportanciaSeguradaApi';

const useObterValorImportanciaSegurada = (codCliente: string) => {
  return useQuery({
    queryKey: ['valor-importancia-segurada', codCliente],
    queryFn: () => obterValorImportanciaSegurada(codCliente),
    staleTime: reactQueryCacheDuration(),
    retry: false,
  });
};

export default useObterValorImportanciaSegurada;
