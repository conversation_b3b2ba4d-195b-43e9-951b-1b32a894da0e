import * as DS from '@cvp/design-system/react';
import * as Enum from '../../types/enum';
import { TitleSection } from 'main/styles/GlobalStyle';
import * as ConstantsFop63 from '../../types/ConstFop63';
import { IFop63Base } from 'extranet/types/InterfacesFop63/IFop63Base';
import * as S from '../../../extranet/features/fops/pages/styles';
import { Label } from 'vida/features/AdicionarBeneficiario/styles';
import { TextoCaracteres } from 'extranet/hooks/useFormFops';

export const FormInformacoesComplementares: React.FC<IFop63Base> = ({
    setFieldValue,
    values,
}) => {
    return (
        <div>
            <DS.Accordion open>
                <S.AccordionItem
                    title={
                        <TitleSection>
                            {Enum.Titulos.InformacoesComplementares}
                        </TitleSection>}
                >
                    <DS.Grid.Item xs={1} lg={1 / 2}>
                        <Label>
                            {ConstantsFop63.MENSAGEM_INFORMACOES_COMPLEMENTARES}
                        </Label>
                        <S.TextAreaFop
                            name="textInformacoesComplementares"
                            spellCheck
                            value={values.textInformacoesComplementares}
                            maxLength={ConstantsFop63.LIMITE_CARACTERES}
                            placeholder=""
                            onChange={({
                                target: { value },
                            }: React.ChangeEvent<HTMLTextAreaElement>) =>
                                setFieldValue('textInformacoesComplementares', value)
                            }
                        />
                        <Label>
                            {TextoCaracteres(
                              ConstantsFop63.LIMITE_CARACTERES,
                              values.textInformacoesComplementares.length,
                            )}
                          </Label>
                    </DS.Grid.Item>
                </S.AccordionItem>
            </DS.Accordion>
        </div>
    )
}