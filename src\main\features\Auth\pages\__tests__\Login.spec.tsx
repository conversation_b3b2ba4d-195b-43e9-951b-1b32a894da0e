import faker from 'faker';
import { cleanup, render, screen, waitFor } from '@testing-library/react';
import user from '@testing-library/user-event';
import { BrowserRouter as Router } from 'react-router-dom';
import ThemeProvider from 'main/components/ThemeProvider';
import LoginPage from 'main/features/Auth/pages/Login';
import { act } from 'react-dom/test-utils';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

afterEach(cleanup);

const queryClient = new QueryClient();
it('deve verificar se a tela carrregou com todos os componentes necessários', () => {
  render(
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <Router>
          <LoginPage />
        </Router>
      </ThemeProvider>
    </QueryClientProvider>,
  );
  const inputMatricula = screen.getByTestId(/matricula-input/i);
  const botaoAcessar = screen.getByTestId(/Acessar/i);
  const botaoFazerCadastro = screen.getByTestId(/fazer-cadastro/i);
  const linkEsqueciMinhaSenha = screen.getByTestId(/esqueci-senha/i);

  expect(inputMatricula).toBeInTheDocument();
  expect(botaoAcessar).toBeInTheDocument();
  expect(botaoFazerCadastro).toBeInTheDocument();
  expect(linkEsqueciMinhaSenha).toBeInTheDocument();
});

it('deve clicar no botao fazer cadastro', async () => {
  render(
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <Router>
          <LoginPage />
        </Router>
      </ThemeProvider>
    </QueryClientProvider>,
  );

  const botaoAcessar = screen.getByTestId(/fazer-cadastro/i);

  act(() => {
    user.click(botaoAcessar);
  });
});

it('deve habilitar o botão de login se os dados estiverem corretos', async () => {
  const password = faker.internet.password();

  render(
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <Router>
          <LoginPage />
        </Router>
      </ThemeProvider>
    </QueryClientProvider>,
  );

  const inputMatricula = screen.getByTestId(/matricula-input/i);
  const inputPassword = screen.getByTestId(/password-input/i);
  const botaoAcessar = screen.getByTestId(/Acessar/i);

  user.type(inputMatricula, 'seg12333');
  user.type(inputPassword, password);

  waitFor(
    () => {
      expect(botaoAcessar).not.toHaveAttribute('disabled');
    },
    {
      timeout: 500,
    },
  );
});

it('deve submeter o formulário.', async () => {
  const password = faker.internet.password();

  render(
    <ThemeProvider>
      <Router>
        <LoginPage />
      </Router>
    </ThemeProvider>,
  );

  const inputMatricula = screen.getByTestId(/matricula-input/i);
  const inputPassword = screen.getByTestId(/password-input/i);
  const botaoAcessar = screen.getByTestId(/Acessar/i);

  user.type(inputMatricula, 'seg12333');
  user.type(inputPassword, password);

  waitFor(
    () => {
      expect(botaoAcessar).not.toHaveAttribute('disabled');
      act(() => {
        user.click(botaoAcessar);
      });
    },
    {
      timeout: 500,
    },
  );
});
