import React from 'react';

export interface IResponseCancelamentoResgate {
  aviso: string;
  indicadorCancelado: boolean;
  status: string;
}

export type IRequestCancelamentoResgate = {
  cpfCnpj?: string;
  numeroResgate?: string;
};

export type SolicitacaoResgate = {
  codigoEmpresa: string;
  codigoConta: string;
  nomeCliente: string;
  numeroResgate: string;
  staRegaste: string;
  descricaoStaResgate: string;
  codigoProduto: string;
  descricaoProduto: string;
  dataProgramado: string;
  dataPrevistaPagamento: string;
  dataPagamento: string;
  dataSolicitacao: string;
  dataInclusao: string;
  numeroCpf: string;
  valorResgate: number;
  tipoCanal: number;
  tipoResgate: string;
  inicacaoResgateProgramado: string;
  indicacaoAssinatura: string;
  indicacaoCancelavel: string;
};

export type TSolicitacoesResgate = {
  acao?: React.JSX.Element;
  valor?: string;
  status?: string;
};

export type TSolicitacaoResgateDataColumn = SolicitacaoResgate &
  TSolicitacoesResgate;
