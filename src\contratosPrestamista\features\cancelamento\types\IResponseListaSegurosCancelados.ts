import { IFilterableEntity } from 'main/types/IFilterableEntity';

export interface IResponseSegurosQuery {
  isLoading: boolean;
  data?: IResponseListaSegurosCancelados[];
}
export interface IResponseListaSegurosCancelados extends IFilterableEntity {
  codigoAgenciaVenda: number;
  codigoDoEstipulante: string;
  cpfCnpj: string;
  dataHoraEmissaoDaProposta: string;
  dtaFimReversao: string;
  numeroContratoTerceiro: string;
  numeroLinhaDoProduto: string;
  numeroProposta: string;
  numeroPropostaEstipulante: string;
  nomeSegurado: string;
  numContrato: string;
  statusReversao: string;
  valorPremio: number;
  valorImportanciaSegurada: number;
  valorPremioLiquido: number;
  reversaoPermitida: boolean;
}
