import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';
import faker from 'faker';
import * as apiModule from 'main/services';
import React from 'react';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { useTabelaApoliceAssistencias } from 'seguros/hooks';

vi.mock('main/features/Auth/utils/auth', () => ({
  default: vi.fn(() => ({
    user: {
      marcadorControle: 'test-marcador-controle',
    },
    tokenInfo: { expiresIn: '' },
    sessionId: '',
    digitalAgency: false,
  })),
}));

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ComponentesPosVendaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: apiModule.api,
            authMode: 'DEFAULT_GI',
            usernameKey: '@portal-eco:nomeAcesso',
            storageMode: 'LOCAL',
            operationPath: 'PortalEconomiario/',
          }}
        >
          {children}
        </ApiGatewayCvpProvider>
      </ComponentesPosVendaProvider>
    </QueryClientProvider>
  );
};

describe('seguros/hooks/useTabelaApoliceAssistencias', () => {
  let mockUseContext: any;
  let mockInvocarApiGatewayCvpComToken: any;
  let mockResponseData: any;
  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

  beforeEach(() => {
    // Gerar os dados mock uma única vez para usar em todo o teste
    mockResponseData = getMockResponse();

    const mockResponse = {
      sucessoBFF: true,
      entidade: mockResponseData,
    };

    // Criar spy para a função invocarApiGatewayCvpComToken
    mockInvocarApiGatewayCvpComToken = vi.fn().mockResolvedValue(mockResponse);

    // Mock do useApiGatewayCvpInvoker
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: mockInvocarApiGatewayCvpComToken,
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    const numContratoFake = faker.datatype.number().toString();
    mockUseContext = vi.spyOn(React, 'useContext').mockReturnValue({
      clienteVida: { numCertificado: numContratoFake },
    });
  });

  afterEach(() => {
    mockUseContext.mockRestore();
    vi.clearAllMocks();
  });

  it('Requisição: Deve consultar API corretamente', async () => {
    const { result } = renderHook(() => useTabelaApoliceAssistencias(), {
      wrapper: createWrapper(),
    });

    // Como o autoFetch está ativo, a resposta deve estar disponível imediatamente
    await waitFor(() => {
      expect(result.current.contratos.length).toBe(1);
    });

    // Verificar se o resultado contém os dados esperados
    expect(result.current.loading).toBe(false);
    expect(result.current.contratos).toEqual(mockResponseData);
  });
});

function getMockResponse() {
  const numContratoFake = faker.datatype.number();

  return [
    {
      id: null,
      numeroContrato: numContratoFake,
      cpfCnpj: faker.datatype.number(),
      dataAbertura: null,
      dataFechamento: null,
      nomeTitular: faker.name.findName(),
      nomeContato: faker.name.findName(),
      numeroAssistencia: null,
      nomePlano: null,
      segmento: 'VIDA',
      telefonePrincipal: faker.phone.phoneNumber(),
      telefoneSecundario: faker.phone.phoneNumber(),
      nomeBu: 'SIAS',
      servicos: Array.from({ length: 3 }).map(() => ({
        numeroContrato: numContratoFake,
        numeroServico: faker.datatype.number().toString(),
        nome: faker.random.words(3),
        descricaoServico: faker.random.words(3),
        descricaoAssistenciaDetalhada: faker.random.words(3),
        regraUtilizacao: null,
        quantidade: null,
      })),
    },
  ];
}
