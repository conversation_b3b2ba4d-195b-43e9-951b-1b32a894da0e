import { useContext } from 'react';
import { useFormik } from 'formik';
import { AuthContext } from 'main/features/Auth/contexts/AuthContext';
import { TFormMotivoUsoFOP } from 'extranet/types/IFormRegistrarSolicitacaoFOP';
import { getTernaryResult } from 'main/utils/conditional';
import { TModalMotivoFop } from 'main/features/Administracao/types/IFops';
import { usePecoRegistrarSolicitacaoFOP } from 'extranet/hooks/usePecoRegistrarSolicitacaoFOP';
import { submitMotivo, validateMotivo } from 'extranet/utils/ModalMotivo';
import {
  FOP_TEXTS,
  FOP_VALORES,
  initialValues,
} from 'extranet/features/fops/constants/consts';

export const useModalMotivoUsoFops = ({
  segmento,
  fop,
  onClose,
  onConfirm,
}: TModalMotivoFop): TFormMotivoUsoFOP => {
  const { user } = useContext(AuthContext);

  const { loading: loadingMotivoFop, salvarMotivo } =
    usePecoRegistrarSolicitacaoFOP();

  const formik = useFormik({
    initialValues,
    validate: validateMotivo,
    onSubmit: (values, formikHelpers) =>
      submitMotivo({
        values,
        user,
        segmento,
        fop,
        onConfirm,
        resetMotivo: formikHelpers.resetForm,
        salvarMotivo,
      }),
  });

  const {
    values: { descricaoMotivo },
    errors,
    touched,
  } = formik;

  const descricaoContador = descricaoMotivo.trim().length;

  const labelContador = `${descricaoContador} ${getTernaryResult(descricaoContador === 1, 'caracter', 'caracteres')} de ${FOP_VALORES.MOTIVO.CARACTERES.MAX} (${FOP_TEXTS.MOTIVO.CARACTERES.MIN})`;

  const desabilitarBotaoSeguir =
    descricaoContador < FOP_VALORES.MOTIVO.CARACTERES.MIN;

  const errorDescricaoMotivo = getTernaryResult(
    !!touched.descricaoMotivo,
    errors.descricaoMotivo,
    undefined,
  );

  const handleDescricaoMotivo = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ): void => {
    const motivo = e.target.value.replace('  ', ' ');
    const motivoTamanho = motivo.length;
    const podeAlterar = motivoTamanho <= FOP_VALORES.MOTIVO.CARACTERES.MAX;

    if (podeAlterar) formik.setFieldValue('descricaoMotivo', motivo);
  };

  const handleSubmit = (): void => {
    formik.submitForm();
  };

  const handleClose = (): void => {
    formik.resetForm();
    onClose?.(undefined);
  };

  return {
    user,
    descricaoMotivo,
    labelContador,
    desabilitarBotaoSeguir,
    errorDescricaoMotivo,
    loadingMotivoFop,
    handleDescricaoMotivo,
    handleSubmit,
    handleClose,
  };
};
