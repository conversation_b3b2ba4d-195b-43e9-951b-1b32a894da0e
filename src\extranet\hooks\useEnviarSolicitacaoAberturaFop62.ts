import { EnviarArquivoSolicitacaoFOP } from 'extranet/config/endpoints';
import * as Constants from 'extranet/features/fops/constants/constantsFop62';
import { ENDPOINT_FOP062 } from 'extranet/features/fops/constants/consts';
import { useFormFops } from 'extranet/hooks/useFormFops';
import * as Select_enum from 'extranet/types/enum';
import * as enumFop from 'extranet/types/enumFop62';
import { ParamsLocationData } from 'extranet/types/IResponseObterListaFopsAtivos';
import getAuthData from 'main/features/Auth/utils/auth';
import { useLocationPeco } from 'main/hooks/useLocationPeco';
import { usePeco } from 'main/hooks/usePeco';
import { useToast } from 'main/hooks/useToast';
import { api } from 'main/services';
import { getTernaryResult } from 'main/utils/conditional';
import { useNavigate } from 'react-router-dom';
import { ENDPOINT_FOP } from '../config/endpoints';

const useEnviarSolicitacaoAberturaFop62 = () => {
  const formFop = useFormFops();
  const location = useLocationPeco<ParamsLocationData>();
  const { dataLocation } = location.state;
  const { toastError, toastSuccess } = useToast();
  const { user } = getAuthData();
  const { fetchData, loading } = usePeco({
    api: {
      operationPath: ENDPOINT_FOP.REGISTRAR_SOLICITACAO,
    },
  });
  const navigate = useNavigate();
  const enviarFop = async (
    codigoFop: number,
    numeroVersaoFop: number,
    nomeSolicitante: string,
    emailSolicitante: string,
    metadados: any,
  ) => {
    const metadadosSolicitacao = JSON.stringify(metadados);
    const result = await fetchData({
      codigoFop,
      numeroVersaoFop,
      nomeSolicitante,
      emailSolicitante,
      metadadosSolicitacao,
    });

    if (result?.mensagens) {
      if (result?.sucessoBFF) {
        toastSuccess((result?.mensagens[0] || []).descricao);
        setTimeout(() => {
          navigate(ENDPOINT_FOP062.ExtranetFopPrevidencia);
        }, 0);
      }
    } else {
      toastError(enumFop.EnumErrors.erroFop);
    }
  };

  const validarArquivoAnexado = () => {
    const arquivo = formFop.arquivoAnexoFop.get().value;
    const file = arquivo[0];

    if (!file) {
      console.log('error');
      formFop.arquivoAnexoFop.set({
        ...formFop.arquivoAnexoFop.get(),
        isValid: false,
        errorMsg: enumFop.EnumErrorMessage.error,
      });
      return false;
    }

    const tiposPermitidos = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
    ];

    if (!tiposPermitidos.includes(file.type)) {
      formFop.arquivoAnexoFop.set({
        ...formFop.arquivoAnexoFop.get(),
        isValid: false,
        errorMsg:
          'Tipo de arquivo inválido. Envie um arquivo Excel (.xls ou .xlsx).',
      });
      return false;
    }

    const tamanhoMaximo = 3 * 1024 * 1024;
    if (file.size > tamanhoMaximo) {
      console.log('error: arquivo excede 3MB');
      formFop.arquivoAnexoFop.set({
        ...formFop.arquivoAnexoFop.get(),
        isValid: false,
        errorMsg: 'O tamanho do arquivo não pode exceder 3MB.',
      });
      return false;
    }

    return true;
  };
  const handleFormSubmit = async (values: any) => {
    const arquivo = formFop.arquivoAnexoFop.get().value;

    const data = new FormData();
    const file = arquivo[0];
    data.append('Arquivo', file);
    data.append('codigoFop', '62');
    data.append('numeroVersaoFop', '7');

    const isArquivoValido = validarArquivoAnexado();

    if (!isArquivoValido) return;

    const response = await api.post(EnviarArquivoSolicitacaoFOP(), data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    const responseForm = getTernaryResult(
      response?.data?.dados?.entidade?.idUnicoSolicitacaoFop,
      response?.data?.dados?.entidade?.idUnicoSolicitacaoFop,
      '',
    );

    const form = {
      ...values,
      idunicosolicitacaofop: responseForm,
      nomeEmpresa: values.nomeEmpresa,
      cnpj: values.cnpj,
      filial: Select_enum.SELECT_OPTIONS_FILIAL.find(
        filial => filial.key === values.filial,
      )?.value,
      regraParaCalculo: Constants.selectOptionsRegra.find(
        regra => regra.key === values.regraParaCalculo,
      )?.value,
      tipoDeBeneficioBasico: Select_enum.SELECT_OPTIONS_BENEFICIO.find(
        tipobeneficio => tipobeneficio.key === values.tipoDeBeneficioBasico,
      )?.value,
      prazoDeBeneficio: Select_enum.SELECT_OPTIONS_PRAZO_BENEFICIO.find(
        x => x.key === values.prazoDeBeneficio,
      )?.value,
      selectReversao: Select_enum.SELECT_OPTIONS_REVERSAO.find(
        x => x.key === values.selectReversao,
      )?.value,
      formaPagamento: Constants.selectOptionsPagamento.find(
        x => x.key === values.formaPagamento,
      )?.value,
      valoresParticipantes: Constants.selectOptionsValoresParticipantes.find(
        x => x.key === values.valoresParticipantes,
      )?.value,
      valorContribuicao: Select_enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
        x => x.key === values.valorContribuicao,
      )?.value,
      valorContribuicaoEmpresa:
        Select_enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          x => x.key === values.valorContribuicaoEmpresa,
        )?.value,
      valorContribuicaoFuncionario:
        Select_enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          x => x.key === values.valorContribuicaoFuncionario,
        )?.value,
      tipoCuidadoExtra: Select_enum.SELECT_OPTIONS_CUIDADO_EXTRA.find(
        x => x.key === values.tipoCuidadoExtra,
      )?.value,
      regraCuidadoExtra: Select_enum.SELECT_OPTIONS_REGRA_CUIDADO.find(
        x => x.key === values.regraCuidadoExtra,
      )?.value,
      anosPensao: Select_enum.SELECT_OPTIONS_ANOS_PENSAO.find(
        x => x.key === values.anosPensao,
      )?.value,
      regraCuidadoExtraPensao:
        Select_enum.SELECT_OPTIONS_REGRA_CUIDADO_PENSAO.find(
          x => x.key === values.regraCuidadoExtraPensao,
        )?.value,
      formaPagamentoCuidado: Constants.selectOptionsPagamento.find(
        x => x.key === values.formaPagamentoCuidado,
      )?.value,
      valoresParticipanteCuidado:
        Constants.selectOptionsValoresParticipantes.find(
          x => x.key === values.valoresParticipanteCuidado,
        )?.value,
      valorContribuicaoCuidadoExtra:
        Select_enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          x => x.key === values.valorContribuicaoCuidadoExtra,
        )?.value,
      valorContribuicaoEmpresaCuidadoExtra:
        Select_enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          x => x.key === values.valorContribuicaoEmpresaCuidadoExtra,
        )?.value,
      valorContribuicaoFuncionarioCuidadoExtra:
        Select_enum.SELECT_OPTIONS_VALORES_CONTRIBUICAO.find(
          x => x.key === values.valorContribuicaoFuncionarioCuidadoExtra,
        )?.value,
      textOutraFormaPagamento: formFop.textOutraFormaPagamento,
      textOutraFormaPagamentoEmpresa: formFop.textOutraFormaPagamentoEmpresa,
      textOutraFormaPagamentoEmpresaCuidadoExtra:
        formFop.textOutraFormaPagamentoEmpresaCuidadoExtra,
      textOutraFormaPagamentoFuncionario:
        formFop.textOutraFormaPagamentoFuncionario,
      textOutraFormaPagamentoFuncionarioCuidadoExtra:
        formFop.textOutraFormaPagamentoFuncionarioCuidadoExtra,
      textOutraFormaPagamentoCuidadoExtra:
        formFop.textOutraFormaPagamentoCuidadoExtra,
      textInformacoesComplementares: formFop.textInformacoesComplementares,
    };
    enviarFop(62, 7, values.nomeCompleto, values.email, form);
  };

  return {
    user,
    handleFormSubmit,
    formFop,
    dataLocation,
    loading,
    validarArquivoAnexado,
  };
};
export default useEnviarSolicitacaoAberturaFop62;
