import { act, waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';
import ThemeProvider from 'main/components/ThemeProvider';
import { api } from 'main/services';
import { RenderPageWithAppContextAndQueryClient } from 'main/utils/testUtils';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import useLoginSso from '../../hooks/useLoginSso';

vi.mock('../../utils/auth', () => ({
  default: vi.fn(() => ({
    user: {
      marcadorControle: 'test-marcador-controle',
    },
    tokenInfo: { expiresIn: '' },
    sessionId: '',
    digitalAgency: false,
  })),
}));

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

vi.mock('react-router-dom', async importOriginal => {
  const actual = await importOriginal();
  return {
    ...(actual as object),
    useNavigate: vi.fn(),
    useLocation: vi.fn(),
  };
});

const mockNavigate = vi.fn();
const mockLocation = {
  key: '',
  pathname: '/acesso/sso',
  hash: '',
  search: '?sid=1234',
  state: undefined,
};

// Removidas variáveis não utilizadas mockResponseError e mockResponseSucesso

describe('<LoginSso />', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useNavigate as any).mockReturnValue(mockNavigate);
    (useLocation as any).mockReturnValue(mockLocation);
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <ThemeProvider>
      <RenderPageWithAppContextAndQueryClient>
        <ComponentesPosVendaProvider>
          <ApiGatewayCvpProvider
            configure={{
              defaultAxiosClient: api,
              authMode: 'DEFAULT_GI',
              usernameKey: '@portal-eco:nomeAcesso',
              storageMode: 'LOCAL',
              operationPath: 'PortalEconomiario/',
            }}
          >
            {children}
          </ApiGatewayCvpProvider>
        </ComponentesPosVendaProvider>
      </RenderPageWithAppContextAndQueryClient>
    </ThemeProvider>
  );

  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

  it('deve recuperar todos os parâmetros com sucesso', async () => {
    const mockResponse = {
      sucessoBFF: true,
      entidade: {
        nomeAcesso: 'C060267',
        cpf: '00000000000',
        nomeUsuario: 'DANIELLA CRISTINA SOARES BARBOSA',
        email: '<EMAIL>',
        agenciaVinculada: 5648,
        situacao: null,
        perfil: 'ECONOMIARIO',
        marcadorControle: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
      },
    };

    // Mock do useApiGatewayCvpInvoker
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi.fn().mockResolvedValue(mockResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    act(() => {
      renderHook(() => useLoginSso(vi.fn()), { wrapper });
    });

    await waitFor(() => {
      expect(
        mockUseApiGatewayCvpInvoker().invocarApiGatewayCvpComToken,
      ).toBeCalled();
    });
  });

  it('deve redirecionar para o login após receber uma resposta com erro', async () => {
    const mockErrorResponse = {
      sucessoBFF: false,
      entidade: undefined,
      mensagens: [
        {
          codigo: 'INFUSVAS0',
          descricao: 'Ocorreu um erro ao validar o token',
        },
      ],
    };

    // Mock do useApiGatewayCvpInvoker com erro
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockErrorResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi
        .fn()
        .mockResolvedValue(mockErrorResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    const { result } = renderHook(() => useLoginSso(vi.fn()), { wrapper });

    act(() => {
      result.current.validateSessionId();
    });

    await waitFor(() => {
      expect(mockNavigate).toBeCalledWith('/login');
    });
  });

  it('deve logar o usuário através do SSO com sucesso', async () => {
    const mockSuccessResponse = {
      sucessoBFF: true,
      entidade: {
        nomeAcesso: 'C060267',
        cpf: '00000000000',
        nomeUsuario: 'DANIELLA CRISTINA SOARES BARBOSA',
        email: '<EMAIL>',
        agenciaVinculada: 5648,
        situacao: null,
        perfil: 'ECONOMIARIO',
        marcadorControle: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9',
      },
    };

    // Mock do useApiGatewayCvpInvoker
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockSuccessResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi
        .fn()
        .mockResolvedValue(mockSuccessResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    const { result } = renderHook(() => useLoginSso(vi.fn()), { wrapper });

    act(() => {
      result.current.validateSessionId();
    });

    await waitFor(() => {
      expect(mockNavigate).toBeCalledWith('/');
    });
  });
});
