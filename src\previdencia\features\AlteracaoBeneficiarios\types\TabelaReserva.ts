export interface IReserva {
  beneficiario: string;
  parentesco: string;
  dataNascimento: string;
  percentual: number | string;
  remover?: string;
}

export interface IReservaColum extends IReserva {
  beneficiarioElement: React.JSX.Element;
  parentescoElement: React.JSX.Element;
  dataNascimentoElement: React.JSX.Element;
  percentualElement: React.JSX.Element;
  removerElement?: React.JSX.Element;
}

export interface IReservaTabela {
  beneficiario: string | JSX.Element;
  parentesco: string | JSX.Element;
  dataNascimento: string | JSX.Element;
  percentual: string | JSX.Element;
  remover: false | JSX.Element;
}