import { render, screen } from '@testing-library/react';
import CardDadosSegurado from '../CardDadosSegurado';
import '@testing-library/jest-dom';
import { dadosCertificadoSeguro } from '../mocks/dadosCertificadoSeguro';
import { informacoesSegurado } from '../mocks/informacoesSegurado';
import { RenderPageWithAppContextAndQueryClient } from 'main/utils/testUtils';
import { RenderPageWithThemeProviderAndRouter } from 'main/utils/testUtils/RenderPageWithThemeProviderAndRouter';

vi.mock('/src/assets/icons/rounded-warning.svg?react', () => ({
  default: () => 'SvgMock',
}));

vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(() => ({
    loading: false,
    response: null,
    responseBinary: null,
    error: null,
    fetchData: vi.fn(),
    invocarApiGatewayCvpComToken: vi.fn(),
    invocarApiGatewayCvpComRetornoBinary: vi.fn(),
    setResponse: vi.fn(),
    invalidateCache: vi.fn(),
  })),
}));

describe('Button component', () => {
  it('Deve renderizar o componente CardDadosSegurado com o label informado', () => {
    const loadingDadosSegurado = false;
    render(
      <RenderPageWithAppContextAndQueryClient>
        <RenderPageWithThemeProviderAndRouter>
          <CardDadosSegurado
            cpfCnpj="39242531880"
            dadosCertificadoSeguro={dadosCertificadoSeguro}
            informacoesSegurado={informacoesSegurado}
            loadingDadosSegurado={loadingDadosSegurado}
          />
        </RenderPageWithThemeProviderAndRouter>
      </RenderPageWithAppContextAndQueryClient>,
    );

    const textElement = screen.getByTestId('nameDadosSegurado');
    expect(textElement).toHaveTextContent('RAFAEL OLIVEIRA LUCAS');
  });
  it('Mostrar o botão AtualizarDados ativado', () => {
    const loadingDadosSegurado = false;
    render(
      <RenderPageWithAppContextAndQueryClient>
        <RenderPageWithThemeProviderAndRouter>
          <CardDadosSegurado
            cpfCnpj="39242531880"
            dadosCertificadoSeguro={dadosCertificadoSeguro}
            informacoesSegurado={informacoesSegurado}
            loadingDadosSegurado={loadingDadosSegurado}
          />
        </RenderPageWithThemeProviderAndRouter>
      </RenderPageWithAppContextAndQueryClient>,
    );

    const buttonElement = screen.getByTestId('btnAtualizarDadosSegurado');

    expect(buttonElement).not.toBeDisabled();
  });
  it('Mostrar o botão AtualizarDados desativado', () => {
    const loadingDadosSegurado = false;
    const dadosCertificadoSeguroModificado = {
      ...dadosCertificadoSeguro,
      dadosGeraisCertificado: {
        ...dadosCertificadoSeguro.dadosGeraisCertificado,
        statusContrato: 'CANCELADO',
      },
    };
    render(
      <RenderPageWithAppContextAndQueryClient>
        <RenderPageWithThemeProviderAndRouter>
          <CardDadosSegurado
            cpfCnpj="39242531880"
            dadosCertificadoSeguro={dadosCertificadoSeguroModificado}
            informacoesSegurado={informacoesSegurado}
            loadingDadosSegurado={loadingDadosSegurado}
          />
        </RenderPageWithThemeProviderAndRouter>
      </RenderPageWithAppContextAndQueryClient>,
    );

    const buttonElement = screen.getByTestId('btnAtualizarDadosSegurado');

    expect(buttonElement).toBeDisabled();
  });
});
