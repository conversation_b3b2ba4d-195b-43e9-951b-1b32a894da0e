import { waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';
import faker from 'faker';
import { api } from 'main/services';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { IRelatorioConfig } from 'relatorios/features/powerbi/types/IRelatorioConfig';
import { IRelatorioEmbed } from 'relatorios/features/powerbi/types/IRelatorioEmbed';
import { usePowerBI } from '../usePowerBI';

vi.mock('main/features/Auth/utils/auth', () => ({
  default: vi.fn(() => ({
    user: {
      marcadorControle: 'test-marcador-controle',
    },
    tokenInfo: { expiresIn: '' },
    sessionId: '',
    digitalAgency: false,
  })),
}));

// Mock do useApiGatewayCvpInvoker
vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

afterEach(() => {
  vi.clearAllMocks();
});

describe('relatorios/features/powerbi/hooks/usePowerBI', () => {
  const queryClient = new QueryClient();

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ComponentesPosVendaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: api,
            authMode: 'DEFAULT_GI',
            usernameKey: '@portal-eco:nomeAcesso',
            storageMode: 'LOCAL',
            operationPath: 'PortalEconomiario/',
          }}
        >
          {children}
        </ApiGatewayCvpProvider>
      </ComponentesPosVendaProvider>
    </QueryClientProvider>
  );

  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

  it('Deve executar PECO_ObterRelatorioEmbed', async () => {
    const relatorioConfigMock: IRelatorioConfig = {
      GROUP_ID: faker.datatype.uuid(),
      RELATORIO_ID: faker.datatype.uuid(),
    };

    const entidadeMock: IRelatorioEmbed = {
      id: faker.random.word(),
      name: faker.random.word(),
      datasetId: faker.datatype.uuid(),
      datasetWorkspaceId: faker.datatype.uuid(),
      embedUrl: faker.internet.url(),
      embedToken: faker.datatype.uuid(),
      embedTokenExpiration: faker.date.soon().toString(),
    };

    const mockResponse = {
      sucessoBFF: true,
      sucessoGI: true,
      entidade: entidadeMock,
      mensagens: [],
    };

    // Mock do useApiGatewayCvpInvoker com autoFetch simulado
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi.fn().mockResolvedValue(mockResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    const { result } = renderHook(() => usePowerBI(relatorioConfigMock), {
      wrapper,
    });

    // Como o autoFetch está ativo, a resposta deve estar disponível imediatamente
    await waitFor(() => {
      expect(result.current.reportConfig.embedUrl).toBe(entidadeMock.embedUrl);
    });

    expect(result.current.reportConfig.accessToken).toBe(
      entidadeMock.embedToken,
    );
  });
});
