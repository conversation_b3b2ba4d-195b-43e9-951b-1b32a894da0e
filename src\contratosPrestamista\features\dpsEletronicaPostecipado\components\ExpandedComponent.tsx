import React, { useMemo } from "react";
import { TableColumn } from 'react-data-table-component';
import { Display } from 'main/features/Auth/components';
import Table, { setTableType } from 'main/components/Table';
import { TExpandedComponentProp } from "contratosPrestamista/features/dpsEletronicaPostecipado/types/IPropostaDPSExpandedComponent";
import { ColunasListaStatusPropostaDpsAgrupado } from "contratosPrestamista/features/dpsEletronicaPostecipado/constants/ColunasListaStatusPropostaDps";
import { IResponseListarStatusPropostaDps } from "contratosPrestamista/features/dpsEletronicaPostecipado/types/IResponseListarStatusPropostaDps";
import { tryGetValueOrDefault } from "main/utils/conditional";

const ExpandedComponent: React.FC = (row: unknown): JSX.Element => {
  const { data } = row as TExpandedComponentProp;
  const { socios } = data;

  const colunasSocio = useMemo(
    () => ColunasListaStatusPropostaDpsAgrupado(),
    [],
  );

  return (
    <Display style={{ padding: '0 15px', width: '90%', margin: '0 auto' }}>
      <Table
        columns={setTableType<TableColumn<IResponseListarStatusPropostaDps>[]>(
          colunasSocio,
        )}
        data={tryGetValueOrDefault([socios], [])}
      />
    </Display>
  );
};

export default ExpandedComponent;
