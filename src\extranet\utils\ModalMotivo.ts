import {
  FOP_TEXTS,
  FOP_VALORES,
} from 'extranet/features/fops/constants/consts';
import { TPayloadRegistrarSolicitacaoFop } from 'extranet/types/RegistrarSolicitacao';
import {
  TMotivo,
  TMotivoInitialValues,
  TSubmitMotivo,
} from 'main/features/Administracao/types/IFops';
import { toastError, toastSuccess } from 'main/hooks/useToast';
import {
  checkIfSomeItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';

/**
 * Verifica se o comprimento de `descricaoMotivo` está dentro dos limites mínimo e máximo
 * definidos por `FOP_VALORES.MOTIVO.CARACTERES.MIN` e `FOP_VALORES.MOTIVO.CARACTERES.MAX`.
 * Se o valor for muito curto ou muito longo, retorna um objeto com a mensagem de erro apropriada
 * de `FOP_TEXTS.MOTIVO.CARACTERES.MIN` ou `FOP_TEXTS.MOTIVO.CARACTERES.MAX`.
 * Se o valor for válido, retorna `undefined`.
 *
 * @param values - O objeto contendo o `descricaoMotivo` a ser validado.
 * @returns Um objeto com a mensagem de erro se inválido, ou `undefined` se válido.
 */
export const validateMotivo = (
  values: TMotivoInitialValues,
): TMotivoInitialValues | undefined => {
  const descricaoContador = values.descricaoMotivo.trim().length;

  if (descricaoContador < FOP_VALORES.MOTIVO.CARACTERES.MIN)
    return {
      descricaoMotivo: FOP_TEXTS.MOTIVO.CARACTERES.MIN,
    };

  if (descricaoContador > FOP_VALORES.MOTIVO.CARACTERES.MAX)
    return {
      descricaoMotivo: FOP_TEXTS.MOTIVO.CARACTERES.MAX,
    };
  return undefined;
};

/**
 * Esta função monta o payload necessário usando os dados do usuário, FOP e motivo fornecidos,
 * chama a função `salvarMotivo` para persistir os dados e processa a resposta para determinar
 * sucesso ou falha, exibindo notificações (toasts) apropriadas e disparando callbacks conforme necessário.
 *
 * @param segmento - Identificador do segmento relacionado ao motivo.
 * @param user - Objeto do usuário contendo informações de acesso e contato.
 * @param values - Valores do formulário do motivo, incluindo a descrição.
 * @param fop - Lista de FOPs.
 * @param salvarMotivo - Função assíncrona para salvar o motivo, retorna um objeto de resposta.
 * @param resetMotivo - Callback para resetar o estado do formulário do motivo.
 * @param onConfirm - Callback executado após submissão bem-sucedida.
 * @returns Uma promise que resolve quando a operação é concluída.
 */
export const submitMotivo = async ({
  segmento,
  user,
  values,
  fop,
  salvarMotivo,
  resetMotivo,
  onConfirm,
}: TSubmitMotivo): Promise<void> => {
  const metadados: TMotivo = {
    matriculaResponsavel: user.nomeAcesso,
    descricaoMotivo: values.descricaoMotivo.trim(),
    segmento,
  };

  const metadadosSolicitacao = JSON.stringify(metadados);

  const payload: TPayloadRegistrarSolicitacaoFop = {
    codigoFop: tryGetValueOrDefault([fop?.codigo], 0),
    numeroVersaoFop: tryGetValueOrDefault([fop?.numeroVersao], 0),
    nomeSolicitante: user.nomeUsuario,
    emailSolicitante: user.email,
    enviarEmail: false,
    metadadosSolicitacao,
  };

  try {
    const response = await salvarMotivo(payload);

    const erro = checkIfSomeItemsAreTrue([
      !response?.sucessoBFF,
      !response?.sucessoGI,
    ]);

    if (response?.mensagens) {
      const mensagem = tryGetValueOrDefault(response.mensagens, {
        descricao: '',
        codigo: '0',
      }).descricao;

      if (!erro) {
        onConfirm(fop);
        toastSuccess(mensagem);
        resetMotivo();
      } else {
        toastError(mensagem);
      }
    }
  } catch {
    toastError(FOP_TEXTS.MOTIVO.ERRO);
  }
};
