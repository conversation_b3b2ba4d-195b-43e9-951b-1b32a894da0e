import { IInitialValuesType } from './InterfacesFop63/IInitialValues';

export const InitialValues: IInitialValuesType = {
  nomeDaEmpresa: '',
  atividadePrincipalEmpresa: '',
  cnpjEmpresa: '',
  faturamento: '',
  emailInstitucional: '',
  logradouro: '',
  bairro: '',
  cidade: '',
  cep: '',
  uf: '',
  agenciaFilial: '',
  agenciaSr: '',
  numeroDaAgencia: '',
  nomeDaAgencia: '',
  matriculaIndicador: '',
  nomeIndicador: '',
  nomeCompletoResponsavel: '',
  emailResponsavel: '',
  telefoneResponsavel: '',
  numeroDeParticipantes: '',
  regraCalculo: '',
  formaDePagamento: '',
  formaDePagamentoCuidado: '',
  linkSelectRegraCuidadoExtraPeculio: '',
  tipoBeneficioBasico: '',
  prazoBeneficio: '',
  reversao: '',
  optionsValoresParticipantes: '',
  valoresParticipantesCuidado: '',
  valorFixoContribuicao: '',
  valorPercentualContribuicao: '',
  valorFixoEmpresaContribuicao: '',
  linkValorContribuicao: '',
  linkSelectTipoConcessao: '',
  linkSelectTipoPagamentoFatura: '',
  linkSelectDadosCobranca: '',
  linkSelectCuidadoExtra: '',
  linkValorContribuicaoCuidadoExtra: '',
  linkValorContribuicaoEmpresa: '',
  linkValorContribuicaoEmpresaCuidadoExtra: '',
  linkValorContribuicaoFuncionario: '',
  linkValorContribuicaoFuncionarioCuidadoExtra: '',
  linkSelectTipoFundo: '',
  linkSelectVencimentoFatura: '',
  linkSelectDadosOperacao: '',
  linkSelectModalidade: '',
  linkSelectModalidadePGBL: '',
  linkSelectModalidadeVGBL: '',
  linkSelectRegraCuidadoExtraPensao: '',
  linkSelectAnosPensao: '',
  valorFixoEmpresaCuidadoExtraContribuicao: '',
  valorFixoFuncionarioContribuicao: '',
  valorFixoFuncionarioCuidadoExtraContribuicao: '',
  valorPercentualEmpresaContribuicao: '',
  valorPercentualEmpresaCuidadoExtraContribuicao: '',
  valorPercentualFuncionarioContribuicao: '',
  valorPercentualFuncionarioCuidadoExtraContribuicao: '',
  valorFixoCuidadoExtraContribuicao: '',
  valorPercentualCuidadoExtraContribuicao: '',
  nomeRepresentante: '',
  emailRepresentante: '',
  cargoRepresentante: '',
  aporteInicial: '',
  valorAporteInicial: '',
  liberacaoDaReserva: '',
  valorPortabilidade: '',
  prazoContribuicao: '',
  idadeAposentadoria: '',
  aporteUnico: '',
  agencia: '',
  conta: '',
  nomeRepresentanteLegal: '',
  cpfRepresentanteLegal: '',
  emailRepresentanteLegal: '',
  nomePrimeiraTestemunha: '',
  cpfPrimeiraTestemunha: '',
  emailPrimeiraTestemunha: '',
  formaCusteioModalidadePlano: '',
  pagamentoContribuicaoParticipante: '',
  PagamentoContribuicaoEmpresa: '',
  tipoPagamentoContribuicao: '',
  valorContribuicaoParticipante: '',
  valorContribuicaoEmpresa: '',
  diaPagamento: '',
  formaPagamentoRegraContratual: '',
  agenciaPagamentoConta: '',
  contaPagamentoConta: '',
  operacaoPagamentoConta: '',
  perdaVinculo: '',
  demisaoJustaCausa: '',
  penalidades: '',
  recursosInstituidora: '',
  distribuicaoContaColetiva: '',
  tempoMinimoPlano: '',
  descricaoFundo: '',
  textInformacoesComplementares: '',
  valortempo: '',
  contribuicaoTabela: '',
  tempoMesesReversao: '',
  pagamentoContribuicao: '',
  valoresPlano: [
    { tempoMeses: '', porcentagemReversao: '' },
    { tempoMeses: '', porcentagemReversao: '' },
  ],
  tabelaCuidadoExtra: [
    {
      descricao: '',
      valorContribuicao: '',
      empresaPorcentagem: '',
      participantePorcentagem: '',
      empresaPorcentagemPensaoPrazoCerto: '',
      participantePorcentagemPensaoPrazoCerto: '',
    },
    {
      descricao: '',
      valorContribuicao: '',
      empresaPorcentagem: '',
      participantePorcentagem: '',
      empresaPorcentagemPensaoPrazoCerto: '',
      participantePorcentagemPensaoPrazoCerto: '',
    },
  ],
  valoresContribuicao: [
    {
      descricaoGrupo: '',
      participante: '',
      empresa: '',
      valorContribuicao: '',
    },
  ],
  linkCusteioPagamento: '',
  tipoDeContribuicaoCuidadoExtra:''
};
export const MENSAGEM_INFORMACOES_COMPLEMENTARES =
  'Descreva as informações complementares sobre o plano ou regras não previstas anteriormente.';
export const MENSAGEM_CAMPO_OBRIGATORIO = 'Campo obrigatório.';
export const SELECIONE = 'Selecione';
export const SELECIONE_UF = 'Selecione UF';
export const SELECIONE_RELACIONAMENTO = 'Selecione Relacionamento';
export const SELECIONE_FATURAMENTO = 'Selecione Faturamento';

export const MENSAGEM_DOMINIOS_CAIXA =
  'Favor informar um e-mail com domínio Caixa ou Caixa Vida e Previdência';
export const CPF_INVALIDO = 'Favor informar um CPF valido';
export const DOMINIO_CVP = '@caixavidaeprevidencia.com.br';
export const DOMINIO_CAIXA = '@caixa.gov.br';
export const MENSAGEM_EMAIL_INVALIDO = 'Informe um E-mail Válido';
export const MENSAGEM_NUMERO_MINIMO_PARTICIPANTE =
  'A quantidade mínima para contratação de planos Empresariais é de 5 colaboradores. Para a quantidade de participantes preenchida, efetuar a contratação dos produtos Prev Sócio diretamente no SIGPF.';
export const GRUPOS = 'Grupo(s)';
export const DESCRICAO_GRUPOS = 'Descrição do grupo ou todos os colaboradores';
export const TIPO_CUIDADO_EXTRA = 'Tipo Cuidado Extra';
export const PECULIO = 'Pecúlio';
export const PENSAO_PRAZO = 'Pensão Prazo Certo';
export const VALOR_CONTRIBUICAO = 'Valor contribuição ou Valor da Cobertura';
export const EMPRESA_PORCENTAGEM = 'Empresa Porcentagem do Custeio';
export const PORCENTAGEM_PARTICIPANTE = 'Participante Porcentagem do Custeio';
export const PARTICIPANTE = 'Participante';
export const EMPRESA = 'Empresa';
export const VALOR_MAXIMO_EMPRESA = 'Valor Máximo Contribuição Empresa';
export const TEXTO_CONTADOR = 'Caracteres restantes: ';
export const LIMITE_CARACTERES = 940;
export const TEMPO_MINIMO_PLANO = 'O plano deve ter duração mínima de 5 anos.';
