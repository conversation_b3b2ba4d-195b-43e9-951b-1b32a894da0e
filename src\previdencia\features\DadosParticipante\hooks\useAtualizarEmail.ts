import { useContext } from 'react';
import { useToast } from 'main/hooks/useToast';
import { UseQueryResult } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import * as AtualizarEmailApi from 'previdencia/features/DadosParticipante/services/atualizarEmail.api';
import { IApiResponse } from 'main/services';
import { AppContext } from 'main/contexts/AppContext';
import { RequestAlterarEmail } from '../types/DadosParticipante';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useAtualizarEmail = (
  request: RequestAlterarEmail | undefined,
  onCancelar: () => void,
): UseQueryResult<IApiResponse<undefined> | undefined> => {
  const { toastError, toastSuccess } = useToast();
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-atualizar-email', cpfCnpj],
    queryFn: () =>
      AtualizarEmailApi.atualizarEmail(cpfCnpj, numCertificado, request),

    staleTime: reactQueryCacheDuration(),
    retry: false,
    enabled: false,
    onError: (erro: Error) => toastError(erro.message),
    onSuccess: data => {
      if (data?.dados?.mensagens) {
        onCancelar();
        toastSuccess(String(data.dados.mensagens[0].descricao));
      }
    },
  });
};

export default useAtualizarEmail;
