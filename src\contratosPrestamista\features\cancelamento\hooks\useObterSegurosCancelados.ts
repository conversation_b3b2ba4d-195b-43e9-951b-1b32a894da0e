import { useQuery } from '@tanstack/react-query';
import * as Cancelamento from 'contratosPrestamista/features/cancelamento/services/cancelamento';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';
import { useToast } from 'main/hooks/useToast';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';

export const useObterSegurosCancelados = () => {
  return useQuery({
    queryKey: ['cancelamentos'],
    queryFn: Cancelamento.obterListaSegurosCancelados,
    staleTime: reactQueryCacheDuration(),
    retry: false,
  });
};

export const useObterDetalhesSeguroCancelado = (
  id: string,
  numeroLinhaDoProduto: string,
  codigoDoEstipulante: string,
) => {
  const { toastError } = useToast();
  return useQueryCallbacks({
    queryKey: ['cancelamentos', id],
    queryFn: () =>
      Cancelamento.obterDetalhesSeguroCancelado(
        id,
        numeroLinhaDoProduto,
        codigoDoEstipulante,
      ),
    staleTime: reactQueryCacheDuration(),
    retry: false,
    onError: (erro: Error) => toastError(erro.message),
  });
};
