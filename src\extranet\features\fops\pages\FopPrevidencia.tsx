import { Card, Display, Divider, Grid, Text } from '@cvp/design-system/react';
import ListarFops from 'extranet/components/ListarFops/ListarFops';
import React from 'react';

import ModalMotivoUsoFop from 'extranet/components/ListarFops/ModalMotivoUsoFop';
import * as TextosFop from 'extranet/features/fops/constants/constsFopPrevidencia';
import { useFormFopsPrevidencia } from 'extranet/hooks/useFormFopsPrevidencia';
import RenderConditional from 'main/components/RenderConditional';
import SkeletonLoading from 'main/components/SkeletonLoading';
import { TIPOS_SEGMENTO } from 'main/features/Administracao/types/IFops';
import { LinkFop } from 'main/styles/GlobalStyle';

const FopPrevidencia: React.FC = () => {
  const {
    openMotivo,
    handleOpenMotivo,
    loadingFops,
    goToFOP62,
    goToFOP63,
    listarFopsProps,
  } = useFormFopsPrevidencia();

  if (loadingFops) {
    return <SkeletonLoading blocks={4} />;
  }

  return (
    <>
      <RenderConditional condition={loadingFops}>
        <SkeletonLoading blocks={4} />
      </RenderConditional>
      <RenderConditional condition={!loadingFops}>
        <Display type="display-block">
          <Card>
            <Card.Content padding={[4, 4, 4]}>
              <Grid>
                <Grid.Item xs={1}>
                  <Text
                    variant="headline-05"
                    color="primary"
                    key="formulario-titulo"
                  >
                    {TextosFop.TEXTO_FORMULARIO}
                  </Text>
                </Grid.Item>
              </Grid>
              <Divider />
              <Text variant="body01-lg" color="primary" margin>
                <strong> {TextosFop.TEXTO_MANUTENCAO}</strong>
              </Text>
              <Text variant="body02-md" margin>
                {TextosFop.INFORMATIVO_DIGITAL}
              </Text>
              <ListarFops tipoFop="prev_manutencao" {...listarFopsProps} />
              <Text variant="body01-lg" color="primary" margin>
                {TextosFop.ADESAO}
              </Text>
              <ListarFops tipoFop="prev_adesao" {...listarFopsProps} />
              <Text variant="body02-md" color="primary" margin>
                {TextosFop.SAIDA}
              </Text>
              <ListarFops tipoFop="prev_saida" {...listarFopsProps} />
              <Text variant="body02-md" color="primary" margin>
                {TextosFop.PESSOA_JURIDICA}
              </Text>
              <Text variant="body01-sm" color="primary" margin>
                <LinkFop variant="text" onClick={goToFOP62}>
                  <b>{TextosFop.VERSAO_FOP_62} </b>
                  {TextosFop.DESCRICAO_FOP_62}
                </LinkFop>
              </Text>
              <Text variant="body01-sm" color="primary" margin>
                <LinkFop variant="text" onClick={goToFOP63}>
                  <b>{TextosFop.VERSAO_FOP_63} </b>
                  {TextosFop.DESCRICAO_FOP_63}
                </LinkFop>
              </Text>
              <ListarFops tipoFop="prev_pj" {...listarFopsProps} />
              <Text variant="body02-md" color="primary" margin>
                {TextosFop.OUTROS_DOCUEMNTOS}
              </Text>
              <ListarFops tipoFop="prev_outros" {...listarFopsProps} />
              <ListarFops tipoFop="corp_outros" {...listarFopsProps} />
              <Divider />
            </Card.Content>
          </Card>
          <ModalMotivoUsoFop
            fop={openMotivo}
            segmento={TIPOS_SEGMENTO.PREVIDENCIA}
            onClose={handleOpenMotivo}
            onConfirm={listarFopsProps.baixarArquivoFop}
          />
        </Display>
      </RenderConditional>
    </>
  );
};

export default FopPrevidencia;
