import React from 'react';
import * as S from './styles';
import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import RenderConditional from 'main/components/RenderConditional';
import { ITextAreaProps } from './types';

const TextArea: React.FC<ITextAreaProps> = ({
  value,
  disabled,
  error,
  legend,
  errorMessage,
  onChange,
  ...props
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
    onChange?.(e);
  };

  return (
    <S.Container>
      <S.TextArea
        {...props}
        value={value}
        disabled={disabled}
        error={error}
        onChange={handleChange}
      />
      <RenderConditional
        condition={checkIfSomeItemsAreTrue([
          !!legend,
          checkIfAllItemsAreTrue([!!error, !!errorMessage]),
        ])}
      >
        <S.Legend isValid={!error}>
          {tryGetValueOrDefault([errorMessage], legend)}
        </S.Legend>
      </RenderConditional>
    </S.Container>
  );
};

export default TextArea;
