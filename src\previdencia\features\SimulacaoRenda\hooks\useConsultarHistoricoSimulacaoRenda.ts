import { useContext } from 'react';
import { UseQueryResult } from '@tanstack/react-query';
import { reactQueryCacheDuration } from 'portabilidade/config/constants/app.config';
import { useToast } from 'main/hooks/useToast';
import { AppContext } from 'main/contexts/AppContext';
import { ResponseHistoricoSimulacaoRenda } from 'previdencia/features/SimulacaoRenda/types/historicoSimulacaoRenda';
import * as ConsultarHistoricoSimulacaoRendaApi from 'previdencia/features/SimulacaoRenda/services/consultarHistoricoSimulacaoRenda.api';
import { useQueryCallbacks } from 'main/hooks/useQueryCallbacks';

const useConsultarHistoricoSimulacaoRenda = (): UseQueryResult<
  ResponseHistoricoSimulacaoRenda | undefined
> => {
  const { toastError } = useToast();
  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  return useQueryCallbacks({
    queryKey: ['prev-consultar-historico-simulacao-renda', numCertificado],
    queryFn: () =>
      ConsultarHistoricoSimulacaoRendaApi.consultarHistoricoSimulacaoRenda(
        cpfCnpj,
        numCertificado,
      ),

    gcTime: reactQueryCacheDuration(),
    refetchOnWindowFocus: false,
    retry: false,
    onError: (erro: Error) => toastError(erro.message),
  });
};

export default useConsultarHistoricoSimulacaoRenda;
