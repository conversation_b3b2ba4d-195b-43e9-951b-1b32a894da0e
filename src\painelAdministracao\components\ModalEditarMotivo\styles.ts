import styled from 'styled-components';
import { Modal as ModalBase, Grid, Text } from '@cvp/design-system/react';

export const Modal = styled(ModalBase)({
  maxWidth: '600px',
});

export const GridPersonalizado = styled(Grid)({
  width: '100%',
  padding: '10px',
  textAlign: 'center',
});

export const TextPersonalizado = styled(Text)({
  textAlign: 'left',
  marginTop: '10px',
  marginBottom: '10px',
});
