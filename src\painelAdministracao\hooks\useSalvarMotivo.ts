import { usePeco } from 'main/hooks/usePeco';
import { PECOS_PAINEL_ADM } from 'painelAdministracao/config/endpoints';
import { IAtivarMotivoRequest } from 'painelAdministracao/types/IAtivarMotivoRequest';
import { IAtivarMotivoResponse } from 'painelAdministracao/types/IAtivarMotivoResponse';
import { IUseSalvarMotivo } from 'painelAdministracao/types/IUseSalvarMotivo';

export const useSalvarMotivo = (): IUseSalvarMotivo => {
  const {
    fetchData: fetchDataMotivoFop,
    loading: loadingMotivoFop,
    response,
  } = usePeco<IAtivarMotivoRequest, IAtivarMotivoResponse>({
    api: {
      operationPath: PECOS_PAINEL_ADM.AtivarMotivoSolicitacaoFOP,
    },
  });

  return {
    fetchDataMotivoFop,
    loadingMotivoFop,
    response,
  };
};
