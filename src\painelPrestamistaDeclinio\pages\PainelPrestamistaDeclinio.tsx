import {
  <PERSON><PERSON>,
  Card,
  Display as Wrapper,
  Skeleton,
  Text,
} from '@cvp/design-system/react';
import RenderConditional from 'main/components/RenderConditional';
import {
  checkIfSomeItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import { removerMascaraValorMonetario } from 'main/utils/money';
import HeaderPainelPrestamistaDeclinio from 'painelPrestamistaDeclinio/components/HeaderPainelPrestamistaDeclinio';
import ModalDetalhesPrestamistaDeclinio from 'painelPrestamistaDeclinio/components/ModalDetalhes/ModalDetalhesPrestamistaDeclinio';
import TabelaPainelPrestamistaDeclinio from 'painelPrestamistaDeclinio/components/TabelaPainelPrestamistaDeclinio';
import { colunasPrestamistaDeclinioDetalhesFactory } from 'painelPrestamistaDeclinio/factories/colunasPrestamistaDeclinioDetalhesFactory';
import { colunasPrestamistaDeclinioFilhosFactory } from 'painelPrestamistaDeclinio/factories/colunasPrestamistaDeclinioFilhosFactory';
import { dadosInadimplenciaPrestamistaDeclinioFactory } from 'painelPrestamistaDeclinio/factories/dadosInadimplenciaPrestamistaDeclinioFactory';
import { totalPrestamistaDeclinioFactory } from 'painelPrestamistaDeclinio/factories/totalPrestamistaDeclinioFactory';
import { useInadimplenciaPrestamistaDeclinio } from 'painelPrestamistaDeclinio/hook/useInadimplenciaPrestamistaDeclinio';
import {
  DadosTabelaPrestamistaDeclinio,
  InadimplenciaPrestamistaDeclinioDetalhes,
  ModalDetalhesPrestamistaDeclinioProps,
} from 'painelPrestamistaDeclinio/types/InadimplenciaPrestamistaDeclinioTypes';
import { verificarVisaoAgencia } from 'painelPrestamista/utils/verificarVisaoAgencia';
import { useCallback, useMemo, useState } from 'react';
import { TableColumn } from 'react-data-table-component';
import { BUTTON_EXPORT_DATA } from 'painelPrestamistaDeclinio/constants/constants';
import { ETipoVisao } from 'painelPrestamistaDeclinio/types/ETipoVisao';
import ModalTipoDeArquivo from 'painelPrestamistaDeclinio/components/ModalTipoDeArquivo/ModalTipoDeArquivo';
import { useDownloadFile } from 'painelPrestamistaDeclinio/hook/useDownloadFile';

const initialSetModalProps = {
  open: false,
  dados: undefined,
};
const PainelPrestamistaDeclinio = () => {
  const {
    unidade,
    loading,
    handleConsulta,
    dadosInadimplencia,
    dadosInadimplenciaAgencia,
    loadingInadimplenciaAgencia,
    handleBackSteps,
    disableButtonBack,
    responseHierarquiaAgenciaPrestamista,
    loadingHerarquiaAgencia,
  } = useInadimplenciaPrestamistaDeclinio();

  const {
    openModalDados,
    handleClose,
    handleOpen,
    setFormatoArquivo,
    handleChangeFormat,
    loadingExportar,
  } = useDownloadFile(unidade);

  const [modalProps, setModalProps] =
    useState<ModalDetalhesPrestamistaDeclinioProps>(initialSetModalProps);

  const openModal = useCallback((item: DadosTabelaPrestamistaDeclinio) => {
    setModalProps({
      open: true,
      dados: item as InadimplenciaPrestamistaDeclinioDetalhes,
      onClose: () => setModalProps(initialSetModalProps),
    });
  }, []);

  const tipoVisao =
    responseHierarquiaAgenciaPrestamista?.entidade?.tipoHierarquia;
  const dataHierarquia = dadosInadimplencia?.entidade;
  const visaoAgencia = verificarVisaoAgencia(tipoVisao);

  const colunas = visaoAgencia
    ? colunasPrestamistaDeclinioDetalhesFactory(openModal)
    : colunasPrestamistaDeclinioFilhosFactory(
        dadosInadimplencia?.entidade?.tipofilhos,
        handleConsulta,
      );

  const loadingHierarquia = checkIfSomeItemsAreTrue([
    loading,
    loadingInadimplenciaAgencia,
  ]);

  const total = totalPrestamistaDeclinioFactory(
    visaoAgencia,
    dataHierarquia,
    dadosInadimplenciaAgencia?.entidade?.agencias,
  );

  const dados = dadosInadimplenciaPrestamistaDeclinioFactory(
    visaoAgencia,
    dataHierarquia,
    dadosInadimplenciaAgencia?.entidade?.agencias,
  );

  const exibirTotal = useMemo(
    () => !!removerMascaraValorMonetario(total),
    [total],
  );

  return (
    <>
      <HeaderPainelPrestamistaDeclinio
        tipoVisao={
          loadingHerarquiaAgencia ? '-' : tryGetValueOrDefault([tipoVisao], '-')
        }
        unidade={unidade}
      />
      <Wrapper type="Wrapper-block">
        <Card>
          <Card.Content>
            <Wrapper>
              <Button
                disabled={disableButtonBack}
                type="submit"
                variant="outlined"
                onClick={handleBackSteps}
              >
                Voltar
              </Button>
              <RenderConditional condition={tipoVisao === ETipoVisao.AGENCIA}>
                <Button onClick={handleOpen}>{BUTTON_EXPORT_DATA}</Button>
              </RenderConditional>
            </Wrapper>

            <TabelaPainelPrestamistaDeclinio
              dados={dados}
              colunas={colunas as TableColumn<DadosTabelaPrestamistaDeclinio>[]}
              loading={loadingHierarquia}
            />

            <RenderConditional condition={exibirTotal}>
              <Text variant="body02-sm" color="text" margin>
                <Wrapper alignItems="center">
                  Total:{' '}
                  <RenderConditional condition={!loadingHierarquia}>
                    <strong>{total}</strong>
                  </RenderConditional>
                  <RenderConditional condition={loadingHierarquia}>
                    <Skeleton lines={1} />
                  </RenderConditional>
                </Wrapper>
              </Text>
            </RenderConditional>
          </Card.Content>
        </Card>
      </Wrapper>

      <ModalDetalhesPrestamistaDeclinio {...modalProps} />

      <ModalTipoDeArquivo
        open={openModalDados}
        handleClose={handleClose}
        setFormatoArquivo={setFormatoArquivo}
        handleChangeFormat={handleChangeFormat}
        loadingExportar={loadingExportar}
      />
    </>
  );
};

export default PainelPrestamistaDeclinio;
