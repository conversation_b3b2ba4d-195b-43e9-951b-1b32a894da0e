export type TSelectItem<T> = {
  value: T;
  text: string;
  selected?: boolean;
};

export interface ISelectProps<T> {
  selected?: T;
  options: TSelectItem<T>[];
  label?: string;
  error?: boolean;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  legend?: string;
  hidden?: boolean;
  errorMsg?: string;
  onKeyPress?: (e: { charCode: number; preventDefault: () => void }) => void;
  onChange: (value: T) => void;
}
