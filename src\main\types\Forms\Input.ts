import InputMask from '../InputMask';

export interface IInputProps {
  value: string;
  inputMask?: InputMask;
  label?: string;
  error?: boolean;
  placeholder?: string;
  type?: string;
  maxLength?: string;
  disabled?: boolean;
  legend?: string;
  hidden?: boolean;
  errorMsg?: string;
  onKeyPress?: (e: { charCode: number; preventDefault: () => void }) => void;
  onChange: (value: string) => void;
}
