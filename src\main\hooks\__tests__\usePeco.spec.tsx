import { ReactNode } from 'react';
import { waitFor } from '@testing-library/react';
import { act, renderHook } from '@testing-library/react-hooks';
import { api } from 'main/services';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  ApiGatewayCvpProvider,
  ThemeProvider as ComponentesPosVendaProvider,
  useApiGatewayCvpInvoker,
} from '@cvp/componentes-posvenda';
import { usePeco } from '../usePeco';

vi.mock('../../features/Auth/utils/auth', () => ({
  default: () => ({
    user: {
      marcadorControle: 'TEST_MARCADOR_CONTROLE',
    },
  }),
}));

vi.mock('@cvp/componentes-posvenda', () => ({
  ApiGatewayCvpProvider: ({ children }: { children: React.ReactNode }) =>
    children,
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  useApiGatewayCvpInvoker: vi.fn(),
}));

const mockedSuccessResponseCache = {
  result: {
    sucessoGI: true,
    sucessoBFF: true,
    entidade: 'by cache',
    mensagens: [
      {
        codigo: 'ERGASOA1',
        descricao: 'Agências recuperadas com sucesso',
      },
    ],
  },
};

describe('main/hooks/usePeco - TestUnit', () => {
  const queryClient = new QueryClient();
  const wrapper = ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <ComponentesPosVendaProvider>
        <ApiGatewayCvpProvider
          configure={{
            defaultAxiosClient: api,
            authMode: 'DEFAULT_GI',
            usernameKey: '@portal-eco:nomeAcesso',
            storageMode: 'LOCAL',
            operationPath: 'PortalEconomiario/',
          }}
        >
          {children}
        </ApiGatewayCvpProvider>
      </ComponentesPosVendaProvider>
    </QueryClientProvider>
  );

  const mockUseApiGatewayCvpInvoker = useApiGatewayCvpInvoker as any;

  it('deve testar a chamada de uma api com sucesso sem cache', async () => {
    const mockResponse = {
      sucessoBFF: true,
      entidade: { id: 1, value: 'SEM_CACHE' },
    };

    // Mock do useApiGatewayCvpInvoker
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi.fn().mockResolvedValue(mockResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    const { result } = renderHook(
      () => usePeco({ api: { operationPath: 'PECO_Test' }, autoFetch: false }),
      { wrapper },
    );
    await act(async () => {
      await result.current.fetchData();
    });

    const { response } = result.current;

    waitFor(() => {
      expect((response?.entidade as any)?.value).toBe('SEM_CACHE');
      expect(response?.sucessoBFF).toBeTruthy();
      expect(queryClient.getQueryData(['PECO_Test'])).toBeFalsy();
    });
  });

  it('deve testar a chamada de uma api com erro', async () => {
    const mockError = new Error('ocorreu um erro ao realizar a consulta');

    // Mock do useApiGatewayCvpInvoker com erro
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: null,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi.fn().mockRejectedValue(mockError),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    const { result } = renderHook(
      () => usePeco({ api: { operationPath: 'PECO_Test' }, autoFetch: false }),
      { wrapper },
    );
    await act(async () => {
      await result.current.fetchData();
    });

    const { error, response } = result.current;

    expect(error).toBeInstanceOf(Error);
    expect(response).toBeNull();
  });

  it('deve testar a chamada de uma api e armezenar os dados em cache com react-query sem autofetch', async () => {
    const mockResponse = {
      sucessoBFF: true,
      entidade: 'TESTE_PECO_SEM_AUTO_FETCH',
    };

    // Mock do useApiGatewayCvpInvoker
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi.fn().mockResolvedValue(mockResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    const { result } = renderHook(
      () =>
        usePeco({
          api: { operationPath: 'PECO_Test' },
          autoFetch: false,
          cache: true,
        }),
      { wrapper },
    );

    await act(async () => {
      await result.current.fetchData();
    });

    const { response } = result.current;

    waitFor(() => {
      expect(queryClient.getQueryData(['PECO_Test'])).toBeTruthy();
      expect(response?.sucessoBFF).toBeTruthy();
      expect(response?.entidade).toEqual('TESTE_PECO_SEM_AUTO_FETCH');
    });
  });

  it('deve testar a chamada de uma api e armezenar os dados em cache com react-query com autofetch', async () => {
    const mockResponse = {
      sucessoBFF: true,
      entidade: 'TESTE_PECO_AUTO_FETCH',
    };

    // Mock do useApiGatewayCvpInvoker
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi.fn().mockResolvedValue(mockResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    await act(async () => {
      const { result } = renderHook(
        () =>
          usePeco({
            api: { operationPath: 'PECO_Test' },
            autoFetch: true,
            cache: true,
          }),
        { wrapper },
      );
      const { response } = result.current;

      waitFor(() => {
        expect(queryClient.getQueryData(['PECO_Test'])).toBeTruthy();
        expect(response?.sucessoBFF).toBeTruthy();
        expect(response?.entidade).toEqual('TESTE_PECO_AUTO_FETCH');
      });
    });
  });

  it('deve testar a chamada de uma api usando cache do react-query', async () => {
    const mockCachedResponse = {
      sucessoBFF: true,
      entidade: 'by cache',
    };

    // Mock do useApiGatewayCvpInvoker com resposta do cache
    mockUseApiGatewayCvpInvoker.mockReturnValue({
      loading: false,
      response: mockCachedResponse,
      responseBinary: null,
      invocarApiGatewayCvpComToken: vi
        .fn()
        .mockResolvedValue(mockCachedResponse),
      invocarApiGatewayCvpComRetornoBinary: vi.fn(),
      setResponse: vi.fn(),
      invalidateCache: vi.fn(),
    });

    queryClient.setQueryData(['PECO_Test'], mockedSuccessResponseCache);

    const { result } = renderHook(
      () =>
        usePeco({
          api: { operationPath: 'PECO_Test' },
          autoFetch: true,
          cache: true,
        }),
      { wrapper },
    );

    const { response } = result.current;

    waitFor(() => {
      expect(response?.sucessoBFF).toBeTruthy();
      expect(response?.entidade).toEqual('by cache');
    });
  });
});
