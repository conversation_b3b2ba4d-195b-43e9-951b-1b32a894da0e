import {
  Button,
  Display,
  Text,
  Select,
  TextField,
} from '@cvp/design-system/react';
import Calendar from 'main/components/Calendar/Calendar';
import RenderConditional from 'main/components/RenderConditional';
import { checkIfSomeItemsAreTrue } from 'main/utils/conditional';
import {
  BUTTONS_NAME,
  MOTIVOS_VAZIOS,
  TAMANO_PAGINA_DEFAULT,
  TRADUCAO_TABELA,
} from 'painelAdministracao/constants/constants';
import { required } from 'main/features/Validation/validations';
import { COLUNAS_RELATORIO_SOLICITACAO } from 'painelAdministracao/constants/tabelaRelatorioSolicitacao';
import { mapearRelatorioSolicitacaoFactory } from 'painelAdministracao/factories/mapearRelatorioSolicitacaoFactory';
import useRelatorioSolicitacaoFop from 'painelAdministracao/hooks/useRelatorioSolicitacaoFop';
import {
  Card,
  DisplayPersonalizado,
  DivBotoes,
  DivCamposFiltro,
  DivLayout,
  Form,
  InputBoxPersonal,
  InputSelectPersonal,
  Label,
} from 'painelAdministracao/pages/styles';
import { Table, TableSkeleton } from 'portabilidade/components/Table';
import { useEffect } from 'react';
import { filterOptionsFactory } from 'painelAdministracao/factories/filterOptionsFactory';

const RelatorioSolicitacaoFop: React.FC = () => {
  const {
    response,
    loading,
    totalLinhas,
    mudarPagina,
    mudarLinhasPorPagina,
    fetchMotivos,
    formik,
    selectFilter,
    searchFilterTypeSelected,
    dateLink,
    clearForm,
    handleDownload,
  } = useRelatorioSolicitacaoFop();

  useEffect(() => {
    fetchMotivos(1);
    formik.submitForm();
  }, []);

  return (
    <Display type="block">
      <Card>
        <Card.Content>
          <Text variant="headline-06" color="primary" margin>
            Relatório de motivos de solicitação FOPs
          </Text>
        </Card.Content>
      </Card>
      <Card>
        <Card.Content>
          <DisplayPersonalizado>
            <Form onSubmit={formik.handleSubmit}>
              <DivLayout>
                <DivCamposFiltro>
                  <RenderConditional
                    condition={filterOptionsFactory?.length > 0}
                    component={
                      <InputSelectPersonal
                        label="Selecione o filtro"
                        placeholder="Escolha a opção"
                        link={selectFilter}
                        validationRules={[required()]}
                        data-testid="select-options"
                      >
                        {filterOptionsFactory.map(item => {
                          return (
                            <Select.Item
                              selected={selectFilter.get().value === item.key}
                              key={item.key}
                              value={item.key}
                              text={item.value}
                              data-testid={`option-${item.key}`}
                            />
                          );
                        })}
                      </InputSelectPersonal>
                    }
                  />

                  <RenderConditional
                    condition={searchFilterTypeSelected === 'codigoFop'}
                    component={
                      <InputBoxPersonal>
                        <Label>Digite o termo da pesquisa</Label>
                        <TextField
                          type="text"
                          name="codigoFop"
                          value={formik.values.codigoFop}
                          onChange={formik.handleChange}
                        />
                      </InputBoxPersonal>
                    }
                  />
                  <RenderConditional
                    condition={searchFilterTypeSelected === 'filtroGeral'}
                    component={
                      <InputBoxPersonal>
                        <Label>Digite o termo da pesquisa</Label>
                        <TextField
                          type="text"
                          name="filtroGeral"
                          value={formik.values.filtroGeral}
                          onChange={formik.handleChange}
                        />
                      </InputBoxPersonal>
                    }
                  />
                  <RenderConditional
                    condition={searchFilterTypeSelected === 'date'}
                    component={
                      <Calendar
                        id="periodo"
                        placeholder=""
                        range
                        value={dateLink.get().value.dateStart}
                        endDate={dateLink.get().value.dateEnd}
                        onChange={(dateStart, dateEnd) => {
                          dateLink.set({
                            value: { dateStart, dateEnd },
                            isValid: true,
                            errorMsg: '',
                          });
                        }}
                        data-testid="calendario-pesquisa"
                      />
                    }
                  />
                </DivCamposFiltro>
                <DivBotoes>
                  <Button type="submit">{BUTTONS_NAME.BUSCAR}</Button>
                  <Button
                    disabled={checkIfSomeItemsAreTrue([loading])}
                    variant="outlined"
                    onClick={clearForm}
                  >
                    {BUTTONS_NAME.LIMPAR}
                  </Button>
                  <Button variant="secondary" onClick={handleDownload}>
                    {BUTTONS_NAME.EXPORT_FOAP}
                  </Button>
                </DivBotoes>
              </DivLayout>
            </Form>
          </DisplayPersonalizado>

          <RenderConditional condition={checkIfSomeItemsAreTrue([loading])}>
            <TableSkeleton colunas={COLUNAS_RELATORIO_SOLICITACAO()} />
          </RenderConditional>
          <Table
            responsive
            data={mapearRelatorioSolicitacaoFactory(
              response?.entidade ?? MOTIVOS_VAZIOS,
            )}
            columns={COLUNAS_RELATORIO_SOLICITACAO()}
            noDataComponent={TRADUCAO_TABELA.SEM_DADOS}
            pagination
            progressPending={loading}
            progressComponent={<></>}
            paginationServer
            paginationPerPage={TAMANO_PAGINA_DEFAULT}
            paginationTotalRows={totalLinhas}
            onChangeRowsPerPage={mudarLinhasPorPagina}
            onChangePage={mudarPagina}
            paginationRowsPerPageOptions={[15, 25, 50]}
            paginationComponentOptions={{
              rowsPerPageText: TRADUCAO_TABELA.ITEM_POR_PAGINA,
              rangeSeparatorText: TRADUCAO_TABELA.ITEM_POR_PAGINA,
            }}
          />
        </Card.Content>
      </Card>
    </Display>
  );
};

export default RelatorioSolicitacaoFop;
