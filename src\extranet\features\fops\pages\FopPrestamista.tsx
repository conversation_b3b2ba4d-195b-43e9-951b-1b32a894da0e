import React from 'react';
import { Display, Card, Text, Grid, Divider } from '@cvp/design-system/react';
import ListarFops from 'extranet/components/ListarFops/ListarFops';
import SkeletonLoading from 'main/components/SkeletonLoading';
import { useFormFops } from 'extranet/hooks/useFormFops';
import ModalMotivoUsoFop from 'extranet/components/ListarFops/ModalMotivoUsoFop';
import { TIPOS_SEGMENTO } from 'main/features/Administracao/types/IFops';
import { FOP_TEXTS } from 'extranet/features/fops/constants/consts';

const FopPrestamista: React.FC = () => {
  const { loadingFops, openMotivo, handleOpenMotivo, listarFopsProps } =
    useFormFops();

  if (loadingFops) {
    return <SkeletonLoading blocks={3} />;
  }

  return (
    <Display type="display-block">
      <Card>
        <Card.Content padding={[4, 4, 4]}>
          <Grid>
            <Grid.Item xs={1}>
              <Text
                variant="headline-05"
                color="primary"
                key="formulario-titulo"
              >
                {FOP_TEXTS.TITULOS.PRESTAMISTA}
              </Text>
            </Grid.Item>
          </Grid>
          <Divider />
          <ListarFops tipoFop="prest" {...listarFopsProps} />
          <ListarFops tipoFop="prest_vida" {...listarFopsProps} />
          <ListarFops tipoFop="corp_outros" {...listarFopsProps} />
          <Divider />
        </Card.Content>
      </Card>

      <ModalMotivoUsoFop
        fop={openMotivo}
        segmento={TIPOS_SEGMENTO.PRESTAMISTA}
        onClose={handleOpenMotivo}
        onConfirm={listarFopsProps.baixarArquivoFop}
      />
    </Display>
  );
};
export default FopPrestamista;
