import { usePeco } from 'main/hooks/usePeco';
import { PECOS_PRESTAMISTA_DECLINIO } from 'painelPrestamistaDeclinio/config/endpoints';
import { IExportarDeclinioPrestamistaDetalhesAgenciaPayload } from 'painelPrestamistaDeclinio/types/IExportarDeclinioPrestamistaDetalhesAgenciaPayload';
import { TExportarDeclinioPrestamistaDetalhesAgenciaResponse } from 'painelPrestamistaDeclinio/types/TExportarDeclinioPrestamistaDetalhesAgenciaResponse';
import { IPecoExportDeclinioPrestamistaDetalhesAgenciaReturn } from 'painelPrestamistaDeclinio/types/IPecoExportDeclinioPrestamistaDetalhesAgenciaReturn';

export const usePecoExportarDeclinioPrestamistaDetalhesAgencia =
  (): IPecoExportDeclinioPrestamistaDetalhesAgenciaReturn => {
    const { responseBinary, loading, fetchData, setResponse } = usePeco<
      IExportarDeclinioPrestamistaDetalhesAgenciaPayload,
      TExportarDeclinioPrestamistaDetalhesAgenciaResponse
    >({
      api: {
        operationPath:
          PECOS_PRESTAMISTA_DECLINIO.ExportarDeclinioPrestamistaDetalhesAgencia,
      },
      handleResponse: { throwToastErrorBFF: false },
      requestConfig: {
        responseType: 'arraybuffer',
      },
    });

    return {
      responseExportar: responseBinary,
      loadingExportar: loading,
      fechDataExportar: fetchData,
      setResponseExportar: setResponse,
    };
  };
