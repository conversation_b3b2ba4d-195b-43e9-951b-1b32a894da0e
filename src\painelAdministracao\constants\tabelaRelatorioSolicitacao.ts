import { TableColumn } from 'react-data-table-component';
import { IRelatorioSolicitacaoFopFactory } from 'painelAdministracao/types/IRelatorioSolicitacaoFopFactory';

export const COLUNAS_RELATORIO_SOLICITACAO =
  (): TableColumn<IRelatorioSolicitacaoFopFactory>[] => [
    {
      name: 'Data',
      cell: row => row.dataSolicitacao,
      width: '130px',
      sortable: true,
    },
    {
      name: 'Matrícula',
      selector: row => row.matriculaResponsavel,
      width: '180px',
      sortable: true,
    },
    {
      name: 'Produto',
      selector: row => row.segmento,
      width: '150px',
      sortable: true,
    },
    {
      name: 'FOP',
      cell: row => row.servicoSelecionado,
      sortable: true,
    },
    {
      name: 'Descrição do motivo',
      cell: row => row.descricaoMotivo,
      width: '350px',
      sortable: true,
    },
  ];
