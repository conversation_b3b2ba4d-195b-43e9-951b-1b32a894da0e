import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Grid, Display, Text, Button, Card } from '@cvp/design-system/react';
import { AppContext } from 'main/contexts/AppContext';
import { usePrevidenciaContext } from 'previdencia/contexts/PrevidenciaContextProvider';
import ButtonsEtapasAporte from 'previdencia/features/Aporte/components/ButtonsEtapasAporte';
import GerarAporteDadosDistribuicao from 'previdencia/features/Aporte/components/GerarAporte/GerarAporteDadosDistribuicao';
import { usePecoValidarAporte } from 'previdencia/features/Aporte/hooks/usePecoAporte';
import * as FACTORY from 'previdencia/features/Aporte/factories/aporteFactory';
import * as CONST from 'previdencia/features/Aporte/constants';
import * as APORTE_TYPES from 'previdencia/features/Aporte/types/Aporte';

import usePrevSummary from 'previdencia/hooks/usePrevSummary';
import {
  checkIfAllItemsAreTrue,
  checkIfSomeItemsAreTrue,
  tryGetValueOrDefault,
} from 'main/utils/conditional';
import ModalBlur from 'main/components/ModalBlur/ModalBlur';
import RenderConditional from 'main/components/RenderConditional';
import ModalTipoDocuSign from 'main/components/AssinaturaDocuSign/ModalTipoDocuSign';
import { InfoGridItemInline } from 'previdencia/components/PrevidenciaResumo/InfoGridItemInline/InfoGridItemInline';
import { useFormik } from 'formik';
import { usePecoSolicitarAssinatura } from 'previdencia/features/Aporte/hooks/usePecoAssinaturaEletronica';
import { useObterContatosDefaultPrevidencia } from 'main/hooks/useObterContatosDefaultPrevidencia';
import { ISolicitarAssinaturaPayload } from 'previdencia/features/Aporte/types/IUsePecoSolicitarAssinatura';
import ModalTokenDocuSign from 'main/components/AssinaturaDocuSign/ModalTokenDocuSign';
import { useMessageEvent } from 'main/hooks/useMessageEvent';
import { IMessageEvent } from 'main/types/IMessageEvent';
import InputGridItemInline from '../InputGrid';
import ModalConfirmaCertificado from './ConfirmaCertificado';
import { validarVerificacaoCamposAporte } from '../../utils/aporteUtils';
import { ContainerBotaoConclusaoAporte } from '../../styles';

const GerarAporte: React.FC<APORTE_TYPES.IGerarAporteProps> = ({
  fundosDistribuicao,
  aporteFormaPagamento,
  aporteDadosBancarios,
  aporteOrigemRecursos,
  renderizarComprovante,
}) => {
  const { featureData } =
    usePrevidenciaContext<Partial<APORTE_TYPES.IAporteContextData>>();
  const [confirmarAporte, setConfirmarAporte] = useState(false);
  const [confirmarCertificado, setConfirmarCertificado] = useState(false);

  const [openModalTipo, setOpenModalTipo] = useState<boolean>(false);
  const [openModalAssinatura, setOpenModalAssinatura] =
    useState<boolean>(false);
  const [urlAssinatura, setUrlAssinatura] = useState<string>('');
  const [exibirBotaoConcluirAporte, setExibirBotaoConcluirAporte] =
    useState<boolean>(false);

  const { informacaoPorCertificado } = usePrevSummary();

  const { subscribe, unsubscribe } = useMessageEvent();

  const {
    cliente: { cpfCnpj, numCertificado },
  } = useContext(AppContext);

  const formik = useFormik({
    initialValues: {
      primeiroNome: '',
      finalCPF: '',
    },
    validate: values =>
      validarVerificacaoCamposAporte(values, {
        nome: tryGetValueOrDefault(
          [informacaoPorCertificado?.pessoa?.pessoaFisica?.nome],
          '',
        ),
        cpfCnpj: tryGetValueOrDefault([cpfCnpj], ''),
      }),
    onSubmit: () => undefined,
  });

  const { data: dadosContatosDefault } = useObterContatosDefaultPrevidencia();

  const { solicitarAssinatura, isLoadingSolicitacaoAssinatura } =
    usePecoSolicitarAssinatura();

  const { validarAporteFetch, validaAporteLoading } = usePecoValidarAporte({
    primeiroNome: formik.values.primeiroNome,
    finalCPF: formik.values.finalCPF,
  });

  const isCertificadoVGBL: boolean =
    informacaoPorCertificado?.produto?.modalidade?.toUpperCase() ===
    CONST.TIPOS_CERTIFICADO.VGBL;

  const requestAporte: APORTE_TYPES.RequestEfetuarAporte =
    FACTORY.requisicaoEfetuarAporte({
      cpfCnpj,
      numCertificado,
      aporteFormaPagamento: aporteFormaPagamento.get().value,
      aporteDadosBancarios: aporteDadosBancarios.get().value,
      fundosDistribuicao: fundosDistribuicao.get().value,
      pepPositivo: featureData?.pepPositivo,
      aporteOrigemRecursos: aporteOrigemRecursos.get().value,
    });

  const dadosCliente = {
    email: dadosContatosDefault?.emailDefault,
    cpfCnpj,
    mobileNumber: dadosContatosDefault?.numerosTelefone,
  };

  const dadosSolicitante: ISolicitarAssinaturaPayload = {
    nome: tryGetValueOrDefault([dadosContatosDefault?.nome], ''),
    cpf: tryGetValueOrDefault([cpfCnpj], ''),
    telefone: tryGetValueOrDefault([dadosContatosDefault?.numerosTelefone], ''),
    email: tryGetValueOrDefault([dadosContatosDefault?.emailDefault], ''),
    numeroCertificado: tryGetValueOrDefault([numCertificado], ''),
  };

  const abrirSolicitaAporte = () => {
    setConfirmarAporte(true);
  };

  const handleAssinar = async (): Promise<void> => {
    const dadosAssinatura = await solicitarAssinatura(dadosSolicitante);

    const urlAssinaturaEletronica =
      dadosAssinatura?.entidade?.tokenAssinatura?.url;

    if (urlAssinaturaEletronica) {
      setUrlAssinatura(urlAssinaturaEletronica);
      setOpenModalAssinatura(true);
      setOpenModalTipo(false);
    }
  };

  const handleCloseModalTipo = async (): Promise<void> => {
    setOpenModalTipo(false);
    featureData?.iniciarProcessoAporte?.();
  };

  const verificarCamposAporte = async () => {
    const resposta = await validarAporteFetch();

    if (resposta?.entidade?.camposValidos) {
      setConfirmarAporte(false);
      setConfirmarCertificado(true);
    } else {
      featureData?.etapaAnterior?.();
    }
  };

  const handleCloseModaisFluxoAssinatura = async () => {
    renderizarComprovante();
    setOpenModalAssinatura(false);
    setOpenModalTipo(false);
  };

  const iniciarFluxoAssinatura = async () => {
    if (isCertificadoVGBL) {
      setConfirmarCertificado(false);
      setOpenModalTipo(true);
    } else {
      setConfirmarCertificado(false);
      renderizarComprovante();
    }
  };

  const dadosPagamento = FACTORY.dadosGerarAporte(
    numCertificado,
    aporteFormaPagamento.get().value,
    aporteDadosBancarios.get().value,
  );

  const validationErrors = checkIfSomeItemsAreTrue([
    !!formik.errors.finalCPF,
    !!formik.errors.primeiroNome,
    !formik.values.finalCPF,
    !formik.values.primeiroNome,
  ]);

  const fecharSolicitacaoAporte = useCallback(() => {
    formik.resetForm();
    setConfirmarCertificado(false);
    setConfirmarAporte(false);
  }, []);

  useEffect(() => {
    subscribe(async event => {
      const {
        data: { eventName, eventSuccess },
      } = event as MessageEvent<IMessageEvent>;

      const isSucessoAssinatura: boolean = checkIfAllItemsAreTrue([
        eventName === 'retornoAcaoConcluirAssinatura',
        eventSuccess,
      ]);

      if (isSucessoAssinatura) {
        setOpenModalTipo(false);
        setExibirBotaoConcluirAporte(true);
      }
    });

    return () => {
      unsubscribe(() => setExibirBotaoConcluirAporte(false));
    };
  }, []);

  return (
    <>
      <RenderConditional condition={confirmarAporte}>
        <ModalBlur open={confirmarAporte} onClose={fecharSolicitacaoAporte}>
          <>
            <Display>
              <Text variant="headline-06" color="primary">
                Deseja realizar o aporte?
              </Text>
            </Display>

            <Card>
              <Card.Content>
                <InfoGridItemInline
                  icon="user"
                  label="Titular"
                  value={tryGetValueOrDefault(
                    [informacaoPorCertificado?.pessoa?.pessoaFisica?.nome],
                    '',
                  )}
                />
                <Display style={{ margin: 0 }}>
                  <InfoGridItemInline
                    icon="documentPaper"
                    label="Certificado"
                    value={tryGetValueOrDefault(
                      [informacaoPorCertificado?.certificadoNumero],
                      '',
                    )}
                    width="135px"
                  />
                  <InfoGridItemInline
                    icon="documentBack"
                    label="Modalidade / Regime Tributário"
                    value={`${informacaoPorCertificado?.produto?.modalidade} / ${informacaoPorCertificado?.regimeTributario} `}
                  />
                </Display>
                <Display gap="100px" style={{ margin: 0 }}>
                  <InfoGridItemInline
                    icon="financialMoneyBag"
                    label="Valor"
                    value={tryGetValueOrDefault(
                      [dadosPagamento[0].valorAporte],
                      '',
                    )}
                    width="135px"
                  />
                  <InfoGridItemInline
                    icon="financialWallet"
                    label="Forma de Pagamento"
                    value={tryGetValueOrDefault(
                      [requestAporte?.Pagamento?.DescricaoPagamento],
                      '',
                    )}
                  />
                </Display>
                <InputGridItemInline
                  name="primeiroNome"
                  label="Informe o primeiro nome do cliente"
                  placeholder="Primeiro nome"
                  icon="personTicked"
                  value={formik.values.primeiroNome}
                  error={formik.errors.primeiroNome}
                  disabled={validaAporteLoading}
                  handleChange={formik.handleChange}
                />
                <InputGridItemInline
                  name="finalCPF"
                  label="Informe os 4 últimos dígitos do CPF"
                  placeholder="Últimos dígitos"
                  icon="documentTicked"
                  value={formik.values.finalCPF}
                  error={formik.errors.finalCPF}
                  disabled={validaAporteLoading}
                  handleChange={formik.handleChange}
                />
              </Card.Content>
            </Card>
            <Grid>
              <Grid.Item xs={1}>
                <ButtonsEtapasAporte
                  proximaEtapa={verificarCamposAporte}
                  buttonLabel="Confirmar Aporte"
                  disabled={checkIfSomeItemsAreTrue([
                    validaAporteLoading,
                    validationErrors,
                  ])}
                />
              </Grid.Item>
            </Grid>
          </>
        </ModalBlur>
      </RenderConditional>

      <Text variant="body02-md" color="primary" style={{ marginTop: 20 }}>
        Distribuição:
      </Text>

      <GerarAporteDadosDistribuicao fundosDistribuicao={fundosDistribuicao} />

      <Button variant="primary" onClick={abrirSolicitaAporte}>
        Solicitar Aporte
      </Button>

      <ModalConfirmaCertificado
        informacaoPorCertificado={informacaoPorCertificado}
        confirmarCertificado={confirmarCertificado}
        cancelConfirmarCertificado={fecharSolicitacaoAporte}
        validaConfirmarCertificado={iniciarFluxoAssinatura}
      />

      <RenderConditional condition={openModalTipo}>
        <ModalTipoDocuSign
          open={openModalTipo}
          executarAssinatura={handleAssinar}
          handleClose={handleCloseModalTipo}
          tituloModal={CONST.TITULO_ASSINATURA_APORTE}
          loading={isLoadingSolicitacaoAssinatura}
          dadosCliente={dadosCliente}
        />
      </RenderConditional>

      <RenderConditional
        condition={checkIfAllItemsAreTrue([
          openModalAssinatura,
          !!urlAssinatura,
        ])}
      >
        <ModalTokenDocuSign
          open={openModalAssinatura}
          urlTokenPage={urlAssinatura}
          exibirChildren={exibirBotaoConcluirAporte}
        >
          <ContainerBotaoConclusaoAporte>
            <Button
              variant="primary"
              onClick={handleCloseModaisFluxoAssinatura}
            >
              Concluir Aporte
            </Button>
          </ContainerBotaoConclusaoAporte>
        </ModalTokenDocuSign>
      </RenderConditional>
    </>
  );
};

export default GerarAporte;
