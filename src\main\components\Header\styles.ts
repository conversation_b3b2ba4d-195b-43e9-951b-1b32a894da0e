import {
  Button as <PERSON><PERSON><PERSON><PERSON>,
  Container as <PERSON><PERSON>er<PERSON><PERSON>,
  Modal as <PERSON><PERSON><PERSON>,
} from '@cvp/design-system/react';
import styled from 'styled-components';

export const Container = styled(ContainerBase)`
  @media print {
    display: none;
  }
`;

export const HeaderImageStyle = styled.img`
  align-self: flex-end;
  margin: 0;
`;

export const HeaderBar = styled.div(({ theme: { color } }) => ({
  background: 'rgb(0, 92, 169)',
  color: color.neutral['08'],
  [`@media print`]: {
    display: 'none',
  },
  padding: '0 15px',
  position: 'relative',
}));

export const LoggedUserInfo = styled.div({
  padding: '2px 24px 0 24px',
});

export const Button = styled(ButtonBase)({
  textAlign: 'initial',
  padding: 0,
});

export const Modal = styled(ModalDS)(({ theme: { breakpoint } }) => ({
  [breakpoint.md()]: {
    maxWidth: '80%',
  },
  [breakpoint.lg()]: {
    maxWidth: '50%',
  },
}));
