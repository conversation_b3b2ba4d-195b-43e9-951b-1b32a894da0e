import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getSelection, $isElementNode, $isRangeSelection } from 'lexical';
import { EnumDirectionAlignment } from 'main/types/EnumDirectionAlignment';
import { IToolbarPlugin } from 'painelAdministracao/types/IToolbarPlugin';
import { useEffect, useState } from 'react';

export const useToolbarPlugin = (): IToolbarPlugin => {
  const [editor] = useLexicalComposerContext();
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);

  const handleApplyTextAlign = (alignment: EnumDirectionAlignment): void => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const nodes = selection.getNodes();
        nodes.forEach(node => {
          const parent = node.getParent();
          if ($isElementNode(parent)) {
            parent.setFormat(alignment);
          }
        });
      }
    });
  };

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          setIsBold(selection.hasFormat('bold'));
          setIsItalic(selection.hasFormat('italic'));
          setIsUnderline(selection.hasFormat('underline'));
        }
      });
    });
  }, [editor]);

  return {
    handleApplyTextAlign,
    isBold,
    isItalic,
    isUnderline,
    editor,
  };
};
