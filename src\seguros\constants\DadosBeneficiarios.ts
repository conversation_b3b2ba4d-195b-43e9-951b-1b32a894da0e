import { getTernaryResult } from 'main/utils/conditional';
import { ConditionalStyles } from 'react-data-table-component';
import { TBeneficiariosResult } from 'seguros/types/DadosBeneficiarios';
import {
  EStatus,
  TDadosBenefStatus,
  TStatus,
} from 'seguros/types/ManterBeneficiariosVida';

export const TEXTOS_DADOS_BENEFICIARIOS = {
  TITULO: 'Informações de Beneficiários',
  SUBTITULO: 'Dados dos Beneficiários',
  AVISOS: {
    TOTAL_PERCENTUAL: (somaPercentuais: number): string =>
      `A soma do percentual dos beneficiários precisa ser 100%, a soma atual é ${somaPercentuais}%.`,
    ZERO_PERCENTUAL:
      'Remova todos os beneficiários com valor zerado ou atribua algum valor percentual.',
    HERDEIROS_LEGAIS:
      'A soma da porcentagem dos beneficiários é inferior a 100%. Neste caso, o restante será designado aos seus HERDEIROS LEGAIS.',
  },
  VALIDACOES: {
    NOME: 'Insira o nome do beneficiário',
    CPF: 'CPF inválido',
    NASCIMENTO: 'Data inválida',
    PARENTESCO: 'Selecione o parentesco',
  },
};

export const OPERACOES_BENEFICIARIOS = {
  CONSULTA_BENEFICIARIO: '01',
  ALTERACAO_BENEFICIARIO: '02',
  INCLUSAO_BENEFICIARIO: '03',
  LISTA_BENEFICIARIOS: '04',
  EXCLUSAO_BENEFICIARIO: '05',
};

export const BENEFICIARIO_VALORES_INICIAIS = {
  NOME: '',
  CPF: undefined,
  PARENTESCO: '',
  NASCIEMNTO: undefined,
};

export const valoresIniciais = {
  nomeBeneficiario: BENEFICIARIO_VALORES_INICIAIS.NOME,
  numeroCpf: BENEFICIARIO_VALORES_INICIAIS.CPF,
  grauParentesco: BENEFICIARIO_VALORES_INICIAIS.PARENTESCO,
  dataNascimento: BENEFICIARIO_VALORES_INICIAIS.NASCIEMNTO,
};

export const TIPO_BENEFICIARIO = {
  HERDEIROS: {
    PARENTESCO: 'HERDEIROS',
    NOME: 'HERDEIROS LEGAIS',
  },
};

export const textoAlertaExcluir = (status?: EStatus, nome?: string): string =>
  `Tem certeza de que deseja ${getTernaryResult(
    status === EStatus.REMOVIDO,
    'recuperar',
    'excluir',
  )} o beneficiário ${nome}`;

export const conditionalRowStyles:
  | ConditionalStyles<TBeneficiariosResult & TStatus>[]
  | undefined = [
  {
    when: row => row.status === EStatus.REMOVIDO,
    style: {
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
      textDecoration: 'line-through',
    },
  },
];

export const emptyArrayBeneficiarios: TDadosBenefStatus = [];
