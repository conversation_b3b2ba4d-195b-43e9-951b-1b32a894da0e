import { FormikErrors } from 'formik';
import {
  IFormikConsultaOcorrenciaValues,
  EnumBuscaPor,
} from '../types/ConsultarOcorrencia';
import { TSelectItem } from 'main/types/Forms/Select';

export const FormConsultaOcorrenciaInitialValues: IFormikConsultaOcorrenciaValues =
  {
    inputDate: {
      initialDate: null,
      finalDate: new Date(),
    },
    selected: undefined,
    cpfCnpj: '',
    protocolo: '',
  };

export const FormConsultaOcorrenciaInitialErrors: FormikErrors<IFormikConsultaOcorrenciaValues> =
  {
    inputDate: {
      initialDate: 'Data inválida',
    },
    selected: 'Selecione a forma de consulta',
    cpfCnpj: 'Preencha o CPF',
    protocolo: 'Preencha o protocolo',
  };

export const FormConsultaOcorrenciaOptions: TSelectItem<
  IFormikConsultaOcorrenciaValues['selected']
>[] = [
  {
    value: EnumBuscaPor.CLIENTE,
    text: 'CPF/CNPJ',
  },
  {
    value: EnumBuscaPor.PROTOCOLO,
    text: 'Protocolo',
  },
];
