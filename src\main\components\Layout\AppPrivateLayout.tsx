import React, { PropsWithChildren } from 'react';
import Header from 'main/components/Header';
import { Wrapper } from 'main/components/Menu/Sidebar';
import Footer from './AppFooter';
import AppMiddle from './AppMiddle';

const AppPrivateLayout: React.FC<PropsWithChildren> = ({ children }) => (
  <Wrapper>
    <Header />
    <AppMiddle>{children}</AppMiddle>
    <Footer />
  </Wrapper>
);

export default AppPrivateLayout;
